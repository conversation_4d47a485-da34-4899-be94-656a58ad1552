// Configuration et connexion MySQL native
const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'mtp_db',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 3306
};

async function getConnection() {
  return await mysql.createConnection(dbConfig);
}

module.exports = { getConnection, dbConfig };
