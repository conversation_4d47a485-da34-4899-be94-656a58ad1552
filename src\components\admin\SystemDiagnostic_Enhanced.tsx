import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Server,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  RefreshCw,
  Download,
  Eye,
  Settings,
  Zap,
  Shield,
  Globe,
  FileText,
  Users,
  TrendingUp
} from 'lucide-react';

interface SystemMetric {
  id: string;
  name: string;
  value: number | string;
  unit?: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  description: string;
  lastUpdated: string;
  trend?: 'up' | 'down' | 'stable';
}

interface SystemCheck {
  id: string;
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'running';
  message: string;
  details?: string;
  duration?: number;
  lastRun: string;
}

export const SystemDiagnostic_Enhanced: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'security' | 'logs'>('overview');
  
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([
    {
      id: 'cpu',
      name: 'Utilisation CPU',
      value: 23,
      unit: '%',
      status: 'healthy',
      description: 'Charge processeur du serveur',
      lastUpdated: new Date().toISOString(),
      trend: 'stable'
    },
    {
      id: 'memory',
      name: 'Mémoire RAM',
      value: 67,
      unit: '%',
      status: 'warning',
      description: 'Utilisation de la mémoire vive',
      lastUpdated: new Date().toISOString(),
      trend: 'up'
    },
    {
      id: 'disk',
      name: 'Espace Disque',
      value: 45,
      unit: '%',
      status: 'healthy',
      description: 'Utilisation de l\'espace de stockage',
      lastUpdated: new Date().toISOString(),
      trend: 'stable'
    },
    {
      id: 'response_time',
      name: 'Temps de Réponse',
      value: 156,
      unit: 'ms',
      status: 'healthy',
      description: 'Temps de réponse moyen de l\'API',
      lastUpdated: new Date().toISOString(),
      trend: 'down'
    }
  ]);

  const [systemChecks, setSystemChecks] = useState<SystemCheck[]>([
    {
      id: 'database',
      name: 'Connexion Base de Données',
      status: 'pass',
      message: 'Connexion établie avec succès',
      duration: 45,
      lastRun: new Date().toISOString()
    },
    {
      id: 'api',
      name: 'Services API',
      status: 'pass',
      message: 'Tous les endpoints répondent correctement',
      duration: 120,
      lastRun: new Date().toISOString()
    },
    {
      id: 'auth',
      name: 'Système d\'Authentification',
      status: 'pass',
      message: 'JWT et sessions fonctionnels',
      duration: 67,
      lastRun: new Date().toISOString()
    },
    {
      id: 'storage',
      name: 'Stockage Fichiers',
      status: 'warning',
      message: 'Espace de stockage à 85% de capacité',
      details: 'Considérer l\'ajout d\'espace de stockage',
      duration: 89,
      lastRun: new Date().toISOString()
    },
    {
      id: 'backup',
      name: 'Sauvegardes Automatiques',
      status: 'pass',
      message: 'Dernière sauvegarde: il y a 2 heures',
      duration: 234,
      lastRun: new Date().toISOString()
    },
    {
      id: 'security',
      name: 'Sécurité & Firewall',
      status: 'pass',
      message: 'Aucune intrusion détectée',
      duration: 156,
      lastRun: new Date().toISOString()
    }
  ]);

  const runDiagnostic = async () => {
    setIsRunning(true);
    
    // Simuler les tests de diagnostic
    for (let i = 0; i < systemChecks.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSystemChecks(prev => prev.map((check, index) => 
        index === i 
          ? { ...check, status: 'running' as const, message: 'Test en cours...' }
          : check
      ));
    }

    // Finaliser les résultats
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setSystemChecks(prev => prev.map(check => ({
      ...check,
      status: Math.random() > 0.8 ? 'warning' : 'pass',
      message: check.status === 'warning' ? check.message : 'Test réussi',
      lastRun: new Date().toISOString(),
      duration: Math.floor(Math.random() * 200) + 50
    })));

    setLastUpdate(new Date());
    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'fail':
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'fail':
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'running':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down':
        return <TrendingUp className="h-4 w-4 text-green-500 transform rotate-180" />;
      default:
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const overallStatus = systemChecks.every(check => check.status === 'pass') ? 'healthy' :
                       systemChecks.some(check => check.status === 'fail') ? 'critical' : 'warning';

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${
            overallStatus === 'healthy' ? 'bg-green-100' :
            overallStatus === 'critical' ? 'bg-red-100' : 'bg-yellow-100'
          }`}>
            {getStatusIcon(overallStatus)}
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Diagnostic Système</h2>
            <p className="text-sm text-gray-500">
              Dernière mise à jour: {lastUpdate.toLocaleTimeString('fr-FR')}
            </p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={runDiagnostic}
            disabled={isRunning}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
            {isRunning ? 'Test en cours...' : 'Lancer Diagnostic'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Vue d\'ensemble', icon: Activity },
            { id: 'performance', name: 'Performance', icon: Zap },
            { id: 'security', name: 'Sécurité', icon: Shield },
            { id: 'logs', name: 'Logs', icon: FileText }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Métriques système */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques Système</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {systemMetrics.map((metric) => (
                <motion.div
                  key={metric.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg border ${getStatusColor(metric.status)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">{metric.name}</span>
                    {getTrendIcon(metric.trend)}
                  </div>
                  <div className="text-2xl font-bold">
                    {metric.value}{metric.unit}
                  </div>
                  <p className="text-xs mt-1 opacity-75">{metric.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Tests système */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tests Système</h3>
            <div className="space-y-3">
              {systemChecks.map((check) => (
                <motion.div
                  key={check.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(check.status)}
                    <div>
                      <h4 className="font-medium text-gray-900">{check.name}</h4>
                      <p className="text-sm text-gray-600">{check.message}</p>
                      {check.details && (
                        <p className="text-xs text-gray-500 mt-1">{check.details}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    {check.duration && (
                      <span className="text-sm text-gray-500">{check.duration}ms</span>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'performance' && (
        <div className="text-center py-8">
          <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Analyse de Performance</h3>
          <p className="text-gray-600">Graphiques de performance détaillés à venir</p>
        </div>
      )}

      {activeTab === 'security' && (
        <div className="text-center py-8">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Audit de Sécurité</h3>
          <p className="text-gray-600">Rapport de sécurité détaillé à venir</p>
        </div>
      )}

      {activeTab === 'logs' && (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Logs Système</h3>
          <p className="text-gray-600">Visualiseur de logs en temps réel à venir</p>
        </div>
      )}
    </div>
  );
};

export default SystemDiagnostic_Enhanced;
