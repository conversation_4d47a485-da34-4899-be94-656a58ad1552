import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { api } from '../services/api';

// Types pour l'état admin
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  avatar?: string;
  permissions: string[];
}

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

interface AdminState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  notifications: Notification[];
  unreadNotifications: number;
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  language: 'fr' | 'ar' | 'en';
  error: string | null;
}

// Actions
type AdminAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'CLEAR_NOTIFICATIONS' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'SET_LANGUAGE'; payload: 'fr' | 'ar' | 'en' }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET_STATE' };

// État initial
const initialState: AdminState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  notifications: [],
  unreadNotifications: 0,
  sidebarCollapsed: false,
  theme: 'light',
  language: 'fr',
  error: null,
};

// Reducer
const adminReducer = (state: AdminState, action: AdminAction): AdminState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload };
    
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadNotifications: newNotifications.filter(n => !n.read).length,
      };
    
    case 'MARK_NOTIFICATION_READ':
      const updatedNotifications = state.notifications.map(n =>
        n.id === action.payload ? { ...n, read: true } : n
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadNotifications: updatedNotifications.filter(n => !n.read).length,
      };
    
    case 'CLEAR_NOTIFICATIONS':
      return {
        ...state,
        notifications: [],
        unreadNotifications: 0,
      };
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    
    case 'SET_LANGUAGE':
      return { ...state, language: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
};

// Context
interface AdminContextType {
  state: AdminState;
  dispatch: React.Dispatch<AdminAction>;
  // Actions helpers
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'fr' | 'ar' | 'en') => void;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

// Provider
interface AdminProviderProps {
  children: ReactNode;
}

export const AdminProvider: React.FC<AdminProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(adminReducer, initialState);

  // Initialisation
  useEffect(() => {
    const initializeAdmin = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        // Vérifier l'authentification
        const token = localStorage.getItem('auth_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (token && userInfo) {
          try {
            const user = JSON.parse(userInfo);
            dispatch({ type: 'SET_USER', payload: user });
            dispatch({ type: 'SET_AUTHENTICATED', payload: true });
            
            // Charger les notifications
            await loadNotifications();
          } catch (e) {
            console.error('Erreur lors du parsing des infos utilisateur:', e);
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');
          }
        }
        
        // Charger les préférences
        const savedTheme = localStorage.getItem('admin_theme') as 'light' | 'dark';
        const savedLanguage = localStorage.getItem('admin_language') as 'fr' | 'ar' | 'en';
        const savedSidebarState = localStorage.getItem('admin_sidebar_collapsed');
        
        if (savedTheme) dispatch({ type: 'SET_THEME', payload: savedTheme });
        if (savedLanguage) dispatch({ type: 'SET_LANGUAGE', payload: savedLanguage });
        if (savedSidebarState) dispatch({ type: 'TOGGLE_SIDEBAR' });
        
      } catch (error) {
        console.error('Erreur lors de l\'initialisation admin:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Erreur d\'initialisation' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAdmin();
  }, []);

  // Charger les notifications
  const loadNotifications = async () => {
    try {
      // Simuler des notifications pour l'instant
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'warning',
          title: 'Rapport ACAPS en retard',
          message: 'Le rapport trimestriel Q2 2024 doit être soumis avant le 31 juillet',
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: '/admin/compliance-acaps'
        },
        {
          id: '2',
          type: 'info',
          title: 'Nouveau prospect',
          message: '5 nouveaux prospects ont été ajoutés aujourd\'hui',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          read: false,
          actionUrl: '/admin/leads-insurance'
        },
        {
          id: '3',
          type: 'success',
          title: 'Contrat signé',
          message: 'Le contrat AUTO-2024-001234 a été signé par M. Alami',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          read: true,
          actionUrl: '/admin/quotes-pro'
        }
      ];

      mockNotifications.forEach(notification => {
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      });
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    }
  };

  // Actions helpers
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const response = await api.login(email, password);
      
      if (response.success && response.data) {
        const user: User = {
          id: response.data.user?.id || '1',
          name: response.data.user?.name || 'Admin User',
          email: email,
          role: 'admin',
          permissions: ['all']
        };

        dispatch({ type: 'SET_USER', payload: user });
        dispatch({ type: 'SET_AUTHENTICATED', payload: true });
        
        // Sauvegarder dans localStorage
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('user_info', JSON.stringify(user));
        
        // Charger les notifications
        await loadNotifications();
        
        return true;
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.message || 'Erreur de connexion' });
        return false;
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Erreur de connexion' });
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    dispatch({ type: 'RESET_STATE' });
    window.location.href = '/admin/login';
  };

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const fullNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: fullNotification });
  };

  const markNotificationRead = (id: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id });
  };

  const clearNotifications = () => {
    dispatch({ type: 'CLEAR_NOTIFICATIONS' });
  };

  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
    localStorage.setItem('admin_sidebar_collapsed', (!state.sidebarCollapsed).toString());
  };

  const setTheme = (theme: 'light' | 'dark') => {
    dispatch({ type: 'SET_THEME', payload: theme });
    localStorage.setItem('admin_theme', theme);
  };

  const setLanguage = (language: 'fr' | 'ar' | 'en') => {
    dispatch({ type: 'SET_LANGUAGE', payload: language });
    localStorage.setItem('admin_language', language);
  };

  const contextValue: AdminContextType = {
    state,
    dispatch,
    login,
    logout,
    addNotification,
    markNotificationRead,
    clearNotifications,
    toggleSidebar,
    setTheme,
    setLanguage,
  };

  return (
    <AdminContext.Provider value={contextValue}>
      {children}
    </AdminContext.Provider>
  );
};

// Hook pour utiliser le context
export const useAdmin = (): AdminContextType => {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

export default AdminContext;
