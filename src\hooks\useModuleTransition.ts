import { useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { AdminModule } from '../types/admin';

interface UseModuleTransitionOptions {
  onBeforeTransition?: (fromModule: AdminModule, toModule: AdminModule) => Promise<boolean>;
  onAfterTransition?: (fromModule: AdminModule, toModule: AdminModule) => void;
  preserveState?: boolean;
}

interface UseModuleTransitionReturn {
  transitionTo: (module: AdminModule) => Promise<void>;
  isTransitioning: boolean;
  currentModule: AdminModule | null;
  error: string | null;
}

const moduleStateMap = new Map<string, any>();

export const useModuleTransition = ({
  onBeforeTransition,
  onAfterTransition,
  preserveState = true
}: UseModuleTransitionOptions = {}): UseModuleTransitionReturn => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentModule, setCurrentModule] = useState<AdminModule | null>(
    getCurrentModuleFromPath(location.pathname)
  );

  const transitionTo = useCallback(async (targetModule: AdminModule) => {
    setIsTransitioning(true);
    setError(null);

    try {
      // Vérifier si la transition est autorisée
      if (onBeforeTransition && currentModule) {
        const canTransition = await onBeforeTransition(currentModule, targetModule);
        if (!canTransition) {
          throw new Error('Transition non autorisée');
        }
      }

      // Sauvegarder l'état du module actuel si nécessaire
      if (preserveState && currentModule) {
        const state = window.__ADMIN_MODULE_STATE__?.[currentModule];
        if (state) {
          moduleStateMap.set(currentModule, state);
        }
      }

      // Effectuer la transition
      navigate(`/admin/${targetModule}`);
      setCurrentModule(targetModule);

      // Restaurer l'état du module cible si disponible
      if (preserveState) {
        const savedState = moduleStateMap.get(targetModule);
        if (savedState) {
          window.__ADMIN_MODULE_STATE__ = {
            ...window.__ADMIN_MODULE_STATE__,
            [targetModule]: savedState
          };
        }
      }

      // Notifier après la transition
      if (onAfterTransition) {
        onAfterTransition(currentModule as AdminModule, targetModule);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la transition');
    } finally {
      setIsTransitioning(false);
    }
  }, [currentModule, navigate, onBeforeTransition, onAfterTransition, preserveState]);

  return {
    transitionTo,
    isTransitioning,
    currentModule,
    error
  };
};

// Utilitaire pour extraire le module actuel depuis le chemin
function getCurrentModuleFromPath(path: string): AdminModule | null {
  const match = path.match(/\/admin\/([^/]+)/);
  return match ? match[1] as AdminModule : null;
}

// Déclaration pour TypeScript
declare global {
  interface Window {
    __ADMIN_MODULE_STATE__?: Record<string, any>;
  }
}

export default useModuleTransition;
