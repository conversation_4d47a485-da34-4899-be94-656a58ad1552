import { useState, useEffect } from 'react';
import { DigitalCampaign } from '../../types/admin';
import aiService from '../../services/aiService';

interface UseCampaignAIReturn {
  loading: boolean;
  optimizations: {
    type: 'TARGETING' | 'CONTENT' | 'TIMING' | 'BUDGET';
    title: string;
    description: string;
    expectedImprovement: number;
    confidence: number;
    implementation: {
      steps: string[];
      difficulty: 'EASY' | 'MEDIUM' | 'HARD';
      estimatedTime: string;
    };
  }[];
  contentSuggestions: {
    title: string;
    description: string;
    callToAction: string;
    targetAudience: string[];
    predictedEngagement: number;
  }[];
  predictions: {
    reach: number;
    engagement: number;
    conversions: number;
    roi: number;
    confidence: number;
  };
  error: string | null;
}

export const useCampaignAI = (
  campaign: DigitalCampaign | null,
  realTimeMetrics?: boolean
): UseCampaignAIReturn => {
  const [loading, setLoading] = useState(false);
  const [optimizations, setOptimizations] = useState<UseCampaignAIReturn['optimizations']>([]);
  const [contentSuggestions, setContentSuggestions] = useState<UseCampaignAIReturn['contentSuggestions']>([]);
  const [predictions, setPredictions] = useState<UseCampaignAIReturn['predictions']>({
    reach: 0,
    engagement: 0,
    conversions: 0,
    roi: 0,
    confidence: 0
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const optimizeCampaign = async () => {
      if (!campaign) return;

      setLoading(true);
      setError(null);

      try {
        // Obtenir les recommandations d'optimisation
        const recommendations = await aiService.optimizeCampaign(campaign);

        // Classifier les optimisations
        const classifiedOptimizations = recommendations
          .filter(rec => rec.type === 'CAMPAIGN')
          .map(rec => ({
            type: rec.metadata.optimizationType,
            title: rec.title,
            description: rec.description,
            expectedImprovement: rec.expectedImpact,
            confidence: rec.confidence,
            implementation: rec.metadata.implementation
          }));

        setOptimizations(classifiedOptimizations);

        // Générer des suggestions de contenu
        const contentResponse = await aiService.generateContent('SOCIAL', {
          campaign,
          targetAudience: campaign.targetAudience
        });

        setContentSuggestions(
          contentResponse.variations.map((content, index) => ({
            title: `Variante ${index + 1}`,
            description: content,
            callToAction: campaign.content.callToAction?.text || '',
            targetAudience: campaign.targetAudience.interests || [],
            predictedEngagement: 0 // À calculer avec l'IA
          }))
        );

        // Prédire les résultats
        const prediction = await aiService.predictOutcome(campaign, 'CAMPAIGN');
        setPredictions({
          reach: campaign.targetAudience.location?.length || 0 * 1000,
          engagement: prediction.probability * 100,
          conversions: (prediction.probability * 100) * 0.1,
          roi: prediction.probability * 2.5,
          confidence: prediction.confidence
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Erreur lors de l\'optimisation de la campagne:', err);
      } finally {
        setLoading(false);
      }
    };

    optimizeCampaign();

    // Si le suivi en temps réel est activé
    if (realTimeMetrics && campaign?.status === 'RUNNING') {
      const interval = setInterval(optimizeCampaign, 5 * 60 * 1000); // Toutes les 5 minutes
      return () => clearInterval(interval);
    }
  }, [campaign, realTimeMetrics]);

  return {
    loading,
    optimizations,
    contentSuggestions,
    predictions,
    error
  };
};

export default useCampaignAI;
