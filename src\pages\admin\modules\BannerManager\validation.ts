import { z } from 'zod';

export const BannerValidation = z.object({
  title: z.string()
    .min(3, 'Le titre doit contenir au moins 3 caractères')
    .max(100, 'Le titre ne doit pas dépasser 100 caractères'),
  
  content: z.string()
    .min(10, 'Le contenu doit contenir au moins 10 caractères')
    .max(500, 'Le contenu ne doit pas dépasser 500 caractères'),
  
  type: z.enum(['info', 'warning', 'success', 'error', 'promotion']),
  
  backgroundColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(\d{1,3},\s*\d{1,3},\s*\d{1,3}\)$/, 
           'Couleur de fond invalide'),
  
  textColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(\d{1,3},\s*\d{1,3},\s*\d{1,3}\)$/, 
           'Couleur de texte invalide'),
  
  startDate: z.string()
    .refine((date) => !isNaN(Date.parse(date)), 'Date de début invalide'),
  
  endDate: z.string()
    .refine((date) => !isNaN(Date.parse(date)), 'Date de fin invalide'),
  
  showContact: z.boolean(),
  
  priority: z.number()
    .int('La priorité doit être un nombre entier')
    .min(1, 'La priorité minimale est 1')
    .max(10, 'La priorité maximale est 10'),
  
  targetPages: z.array(z.string())
    .min(1, 'Sélectionnez au moins une page cible'),
})
.refine(
  (data) => {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    return end > start;
  },
  {
    message: 'La date de fin doit être postérieure à la date de début',
    path: ['endDate'],
  }
);

export type BannerValidationType = z.infer<typeof BannerValidation>;
