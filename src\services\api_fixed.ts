/**
 * Service API unifié pour communiquer avec le backend
 */

const baseURL = import.meta.env.PROD ? '/api' : 'http://localhost:3001/api';

class ApiService {
  private baseURL: string;
  private token: string | null;

  constructor() {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('token');
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (response.status === 401) {
        this.logout();
        window.location.href = '/admin/login';
        throw new Error('Non autorisé');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  private async get(endpoint: string): Promise<any> {
    return this.request(endpoint, { method: 'GET' });
  }

  private async post(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  private async put(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  private async delete(endpoint: string): Promise<any> {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // === AUTHENTIFICATION ===

  async login(email: string, password: string) {
    const response = await this.post('/auth/login', { email, password });
    if (response.success && response.data?.token) {
      this.token = response.data.token;
      localStorage.setItem('token', this.token);
    }
    return response;
  }

  logout() {
    this.token = null;
    localStorage.removeItem('token');
  }

  async getCurrentUser() {
    return this.get('/auth/me');
  }

  // === LEADS ===

  async getLeads(params?: {
    page?: number;
    limit?: number;
    status?: string;
    product?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/leads${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createLead(leadData: any) {
    return this.post('/leads', leadData);
  }

  async updateLead(id: string, data: any) {
    return this.put(`/leads/${id}`, data);
  }

  async deleteLead(id: string) {
    return this.delete(`/leads/${id}`);
  }

  async getLead(id: string) {
    return this.get(`/leads/${id}`);
  }

  // === CLIENTS ===

  async getClients(params?: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/clients${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createClient(clientData: any) {
    return this.post('/clients', clientData);
  }

  async updateClient(id: string, data: any) {
    return this.put(`/clients/${id}`, data);
  }

  async deleteClient(id: string) {
    return this.delete(`/clients/${id}`);
  }

  async getClient(id: string) {
    return this.get(`/clients/${id}`);
  }

  // === DEVIS ===

  async getQuotes(params?: {
    page?: number;
    limit?: number;
    status?: string;
    clientId?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/quotes${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createQuote(quoteData: any) {
    return this.post('/quotes', quoteData);
  }

  async updateQuote(id: string, data: any) {
    return this.put(`/quotes/${id}`, data);
  }

  async sendQuote(id: string) {
    return this.post(`/quotes/${id}/send`);
  }

  async deleteQuote(id: string) {
    return this.delete(`/quotes/${id}`);
  }

  // === SINISTRES ===

  async getClaims(params?: {
    page?: number;
    limit?: number;
    status?: string;
    clientId?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/claims${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createClaim(claimData: any) {
    return this.post('/claims', claimData);
  }

  async updateClaim(id: string, data: any) {
    return this.put(`/claims/${id}`, data);
  }

  async deleteClaim(id: string) {
    return this.delete(`/claims/${id}`);
  }

  // === BANDEAUX ===

  async getBanners() {
    return this.get('/banners');
  }

  async getActiveBanners() {
    return this.get('/banners/active');
  }

  async createBanner(bannerData: any) {
    return this.post('/banners', bannerData);
  }

  async updateBanner(id: string, data: any) {
    return this.put(`/banners/${id}`, data);
  }

  async deleteBanner(id: string) {
    return this.delete(`/banners/${id}`);
  }

  async toggleBanner(id: string) {
    return this.post(`/banners/${id}/toggle`);
  }

  // === BLOG ===

  async getBlogPosts(params?: {
    page?: number;
    limit?: number;
    status?: string;
    category?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/blog${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createBlogPost(postData: any) {
    return this.post('/blog', postData);
  }

  async updateBlogPost(id: string, data: any) {
    return this.put(`/blog/${id}`, data);
  }

  async deleteBlogPost(id: string) {
    return this.delete(`/blog/${id}`);
  }

  async getBlogPost(id: string) {
    return this.get(`/blog/${id}`);
  }

  // === CONTENU ===

  async getContentPages(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/content/pages${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async createContentPage(pageData: any) {
    return this.post('/content/pages', pageData);
  }

  async updateContentPage(id: string, data: any) {
    return this.put(`/content/pages/${id}`, data);
  }

  async deleteContentPage(id: string) {
    return this.delete(`/content/pages/${id}`);
  }

  async getContentPage(id: string) {
    return this.get(`/content/pages/${id}`);
  }

  // === UPLOAD ===

  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
    
    return response.json();
  }

  async getMediaFiles(params?: {
    page?: number;
    limit?: number;
    type?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/upload/media${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async deleteMediaFile(id: string) {
    return this.delete(`/upload/${id}`);
  }

  // === ANALYTICS ===

  async getDashboardStats() {
    return this.get('/analytics/dashboard');
  }

  async getLeadsAnalytics(period?: string) {
    const params = period ? `?period=${period}` : '';
    return this.get(`/analytics/leads${params}`);
  }

  async getRevenueAnalytics(period?: string) {
    const params = period ? `?period=${period}` : '';
    return this.get(`/analytics/revenue${params}`);
  }

  async getPerformanceMetrics() {
    return this.get('/analytics/performance');
  }
}

// Instance globale de l'API
export const api = new ApiService();
