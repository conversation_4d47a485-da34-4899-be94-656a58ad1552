import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Download,
  Send,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Building,
  Target,
  Activity,
  Copy,
  Printer,
  Mail
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface Quote {
  id: string;
  number: string;
  clientName: string;
  clientEmail: string;
  clientCompany?: string;
  title: string;
  description: string;
  status: 'DRAFT' | 'SENT' | 'VIEWED' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED';
  type: 'QUOTE' | 'CONTRACT';
  totalAmount: number;
  validUntil: string;
  createdDate: string;
  sentDate?: string;
  acceptedDate?: string;
  items: QuoteItem[];
  notes: string;
  assignedTo: string;
  tags: string[];
}

interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export const QuotesContracts: React.FC = () => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [filteredQuotes, setFilteredQuotes] = useState<Quote[]>([]);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{show: boolean, message: string, type: 'success' | 'error'}>({
    show: false, message: '', type: 'success'
  });

  const [newQuote, setNewQuote] = useState<Partial<Quote>>({
    clientName: '',
    clientEmail: '',
    clientCompany: '',
    title: '',
    description: '',
    status: 'DRAFT',
    type: 'QUOTE',
    totalAmount: 0,
    validUntil: '',
    items: [],
    notes: '',
    assignedTo: 'current-user',
    tags: []
  });

  // Données de démonstration
  const demoQuotes: Quote[] = [
    {
      id: '1',
      number: 'DEV-2024-001',
      clientName: 'Ahmed Benali',
      clientEmail: '<EMAIL>',
      clientCompany: 'TechCorp Maroc',
      title: 'Solution CRM Complète',
      description: 'Implémentation d\'une solution CRM complète avec modules personnalisés',
      status: 'SENT',
      type: 'QUOTE',
      totalAmount: 150000,
      validUntil: '2024-02-15',
      createdDate: '2024-01-05',
      sentDate: '2024-01-06',
      items: [
        {
          id: '1',
          description: 'Licence CRM Premium',
          quantity: 1,
          unitPrice: 100000,
          total: 100000
        },
        {
          id: '2',
          description: 'Formation utilisateurs',
          quantity: 5,
          unitPrice: 5000,
          total: 25000
        },
        {
          id: '3',
          description: 'Support technique 1 an',
          quantity: 1,
          unitPrice: 25000,
          total: 25000
        }
      ],
      notes: 'Client intéressé par une solution complète. Rendez-vous prévu pour présentation.',
      assignedTo: 'Mohammed MOUMEN',
      tags: ['CRM', 'Premium', 'Formation']
    },
    {
      id: '2',
      number: 'CON-2024-001',
      clientName: 'Fatima El Mansouri',
      clientEmail: '<EMAIL>',
      clientCompany: 'StartupMA',
      title: 'Contrat de Maintenance',
      description: 'Contrat de maintenance annuel pour infrastructure IT',
      status: 'ACCEPTED',
      type: 'CONTRACT',
      totalAmount: 75000,
      validUntil: '2024-12-31',
      createdDate: '2024-01-03',
      sentDate: '2024-01-04',
      acceptedDate: '2024-01-08',
      items: [
        {
          id: '1',
          description: 'Maintenance préventive mensuelle',
          quantity: 12,
          unitPrice: 5000,
          total: 60000
        },
        {
          id: '2',
          description: 'Support technique 24/7',
          quantity: 1,
          unitPrice: 15000,
          total: 15000
        }
      ],
      notes: 'Contrat signé. Début des prestations le 1er février.',
      assignedTo: 'Sarah ALAMI',
      tags: ['Maintenance', 'Support', 'Annuel']
    },
    {
      id: '3',
      number: 'DEV-2024-002',
      clientName: 'Youssef Tazi',
      clientEmail: '<EMAIL>',
      clientCompany: 'Consulting Plus',
      title: 'Audit Sécurité IT',
      description: 'Audit complet de sécurité informatique et recommandations',
      status: 'DRAFT',
      type: 'QUOTE',
      totalAmount: 30000,
      validUntil: '2024-03-01',
      createdDate: '2024-01-12',
      items: [
        {
          id: '1',
          description: 'Audit sécurité infrastructure',
          quantity: 1,
          unitPrice: 20000,
          total: 20000
        },
        {
          id: '2',
          description: 'Rapport détaillé et recommandations',
          quantity: 1,
          unitPrice: 10000,
          total: 10000
        }
      ],
      notes: 'En cours de finalisation. Attente validation interne.',
      assignedTo: 'Karim BENJELLOUN',
      tags: ['Audit', 'Sécurité', 'Consulting']
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setQuotes(demoQuotes);
      setFilteredQuotes(demoQuotes);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Filtrer les devis/contrats
    let filtered = quotes;

    if (searchTerm) {
      filtered = filtered.filter(quote =>
        quote.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quote.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quote.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quote.clientCompany?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(quote => quote.status === statusFilter);
    }

    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(quote => quote.type === typeFilter);
    }

    setFilteredQuotes(filtered);
  }, [quotes, searchTerm, statusFilter, typeFilter]);

  const statusOptions = [
    { value: 'DRAFT', label: 'Brouillon', color: 'bg-gray-100 text-gray-800' },
    { value: 'SENT', label: 'Envoyé', color: 'bg-blue-100 text-blue-800' },
    { value: 'VIEWED', label: 'Consulté', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'ACCEPTED', label: 'Accepté', color: 'bg-green-100 text-green-800' },
    { value: 'REJECTED', label: 'Refusé', color: 'bg-red-100 text-red-800' },
    { value: 'EXPIRED', label: 'Expiré', color: 'bg-orange-100 text-orange-800' }
  ];

  const typeOptions = [
    { value: 'QUOTE', label: 'Devis', color: 'bg-blue-100 text-blue-800' },
    { value: 'CONTRACT', label: 'Contrat', color: 'bg-green-100 text-green-800' }
  ];

  const getStatusInfo = (status: string) => {
    return statusOptions.find(s => s.value === status) || statusOptions[0];
  };

  const getTypeInfo = (type: string) => {
    return typeOptions.find(t => t.value === type) || typeOptions[0];
  };

  const generateQuoteNumber = (type: string) => {
    const prefix = type === 'QUOTE' ? 'DEV' : 'CON';
    const year = new Date().getFullYear();
    const count = quotes.filter(q => q.type === type).length + 1;
    return `${prefix}-${year}-${count.toString().padStart(3, '0')}`;
  };

  const handleAddQuote = async () => {
    try {
      const quoteToAdd: Quote = {
        ...newQuote as Quote,
        id: Date.now().toString(),
        number: generateQuoteNumber(newQuote.type || 'QUOTE'),
        createdDate: new Date().toISOString().split('T')[0],
        items: newQuote.items || []
      };

      setQuotes([...quotes, quoteToAdd]);
      setNewQuote({
        clientName: '',
        clientEmail: '',
        clientCompany: '',
        title: '',
        description: '',
        status: 'DRAFT',
        type: 'QUOTE',
        totalAmount: 0,
        validUntil: '',
        items: [],
        notes: '',
        assignedTo: 'current-user',
        tags: []
      });
      setShowAddModal(false);
      setToast({show: true, message: 'Devis/Contrat ajouté avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de l\'ajout', type: 'error'});
    }
  };

  const handleEditQuote = async () => {
    if (!selectedQuote) return;

    try {
      const updatedQuotes = quotes.map(quote =>
        quote.id === selectedQuote.id ? selectedQuote : quote
      );
      setQuotes(updatedQuotes);
      setShowEditModal(false);
      setSelectedQuote(null);
      setToast({show: true, message: 'Devis/Contrat modifié avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la modification', type: 'error'});
    }
  };

  const handleDeleteQuote = async (quoteId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce devis/contrat ?')) {
      try {
        setQuotes(quotes.filter(quote => quote.id !== quoteId));
        setToast({show: true, message: 'Devis/Contrat supprimé avec succès', type: 'success'});
      } catch (error) {
        setToast({show: true, message: 'Erreur lors de la suppression', type: 'error'});
      }
    }
  };

  const handleSendQuote = async (quoteId: string) => {
    try {
      const updatedQuotes = quotes.map(quote =>
        quote.id === quoteId
          ? { ...quote, status: 'SENT' as const, sentDate: new Date().toISOString().split('T')[0] }
          : quote
      );
      setQuotes(updatedQuotes);
      setToast({show: true, message: 'Devis/Contrat envoyé avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de l\'envoi', type: 'error'});
    }
  };

  const handleDuplicateQuote = async (quote: Quote) => {
    try {
      const duplicatedQuote: Quote = {
        ...quote,
        id: Date.now().toString(),
        number: generateQuoteNumber(quote.type),
        status: 'DRAFT',
        createdDate: new Date().toISOString().split('T')[0],
        sentDate: undefined,
        acceptedDate: undefined,
        title: `${quote.title} (Copie)`
      };

      setQuotes([...quotes, duplicatedQuote]);
      setToast({show: true, message: 'Devis/Contrat dupliqué avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la duplication', type: 'error'});
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Devis et Contrats</h1>
            <p className="text-gray-600">Gérez vos devis et contrats commerciaux</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Nouveau Devis
          </button>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Documents</p>
                <p className="text-2xl font-bold text-gray-900">{quotes.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Acceptés</p>
                <p className="text-2xl font-bold text-gray-900">
                  {quotes.filter(q => q.status === 'ACCEPTED').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Valeur Totale</p>
                <p className="text-2xl font-bold text-gray-900">
                  {quotes.reduce((sum, quote) => sum + quote.totalAmount, 0).toLocaleString()} DH
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taux Acceptation</p>
                <p className="text-2xl font-bold text-gray-900">
                  {quotes.length > 0 ? Math.round((quotes.filter(q => q.status === 'ACCEPTED').length / quotes.length) * 100) : 0}%
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Rechercher un devis/contrat..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les statuts</option>
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les types</option>
              {typeOptions.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des devis/contrats */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Numéro
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Titre
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Montant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Validité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredQuotes.map((quote) => {
                const statusInfo = getStatusInfo(quote.status);
                const typeInfo = getTypeInfo(quote.type);
                const isExpired = new Date(quote.validUntil) < new Date();

                return (
                  <motion.tr
                    key={quote.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{quote.number}</div>
                      <div className="text-sm text-gray-500">{quote.createdDate}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{quote.clientName}</div>
                      <div className="text-sm text-gray-500">{quote.clientEmail}</div>
                      {quote.clientCompany && (
                        <div className="text-sm text-gray-500">{quote.clientCompany}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{quote.title}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">{quote.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeInfo.color}`}>
                        {typeInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusInfo.color}`}>
                        {statusInfo.label}
                      </span>
                      {isExpired && quote.status !== 'ACCEPTED' && (
                        <div className="text-xs text-red-600 mt-1">Expiré</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {quote.totalAmount.toLocaleString()} DH
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {quote.validUntil}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedQuote(quote);
                            setShowDetailModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                          title="Voir détails"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedQuote(quote);
                            setShowEditModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDuplicateQuote(quote)}
                          className="text-green-600 hover:text-green-700"
                          title="Dupliquer"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                        {quote.status === 'DRAFT' && (
                          <button
                            onClick={() => handleSendQuote(quote.id)}
                            className="text-blue-600 hover:text-blue-700"
                            title="Envoyer"
                          >
                            <Send className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => handleDeleteQuote(quote.id)}
                          className="text-red-600 hover:text-red-700"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Toast */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({...toast, show: false})}
        />
      )}
    </div>
  );
};

export default QuotesContracts;
