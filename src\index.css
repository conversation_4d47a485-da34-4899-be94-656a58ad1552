@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles Markdown */
.markdown-body {
  @apply text-gray-900 leading-normal break-words;
}

.markdown-body h1 {
  @apply text-4xl font-bold mb-4 mt-6;
}

.markdown-body h2 {
  @apply text-3xl font-bold mb-4 mt-6;
}

.markdown-body h3 {
  @apply text-2xl font-bold mb-3 mt-6;
}

.markdown-body p {
  @apply mb-4;
}

.markdown-body ul {
  @apply list-disc pl-5 mb-4;
}

.markdown-body ol {
  @apply list-decimal pl-5 mb-4;
}

.markdown-body li {
  @apply mb-2;
}

.markdown-body blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic my-4;
}

.markdown-body code {
  @apply bg-gray-100 rounded px-1 py-0.5 font-mono text-sm;
}

.markdown-body pre {
  @apply bg-gray-100 rounded p-4 mb-4 overflow-x-auto;
}

.markdown-body pre code {
  @apply bg-transparent p-0;
}

.markdown-body a {
  @apply text-blue-600 hover:underline;
}

.markdown-body table {
  @apply w-full border-collapse mb-4;
}

.markdown-body th, 
.markdown-body td {
  @apply border border-gray-300 p-2;
}

.markdown-body th {
  @apply bg-gray-100;
}

.markdown-body img {
  @apply max-w-full h-auto rounded-lg my-4;
}
