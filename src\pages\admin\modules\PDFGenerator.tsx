import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Download,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Layout,
  Car,
  Home as HomeIcon,
  Heart,
  Shield,
  X,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { Toast } from '../../../components/common/Toast';

// Interface pour les templates
interface Template {
  id: number;
  name: string;
  description: string;
  category: string;
  type: string;
  icon: string;
  color: string;
  fields: string[];
  lastModified: string;
  usage: number;
  status: 'active' | 'inactive';
}

// Mapping des icônes
const iconMap = {
  Car,
  HomeIcon,
  Heart,
  Shield,
  FileText
};

// Validation d'un template
function validateTemplate(template: Partial<Template>): string[] {
  const errors: string[] = [];
  
  if (!template.name?.trim()) {
    errors.push('Le nom du template est requis');
  }
  
  if (!template.description?.trim()) {
    errors.push('La description du template est requise');
  }
  
  if (!template.category?.trim()) {
    errors.push('La catégorie du template est requise');
  }
  
  if (!template.type?.trim()) {
    errors.push('Le type du template est requis');
  }
  
  if (template.icon && !iconMap[template.icon]) {
    errors.push('L\'icône sélectionnée est invalide');
  }
  
  return errors;
}

export const PDFGenerator: React.FC = () => {
  // États principaux
  const [selectedTab, setSelectedTab] = useState('templates');
  const [toast, setToast] = useState(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [generatedDocuments, setGeneratedDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [documentFormData, setDocumentFormData] = useState<any>({});
  const [templateEditorState, setTemplateEditorState] = useState<Partial<Template>>({
    name: '',
    description: '',
    category: '',
    type: '',
    color: 'bg-blue-500',
    icon: 'Car',
    fields: []
  });

  // Charger les templates depuis l'API
  useEffect(() => {
    async function loadTemplates() {
      try {
        setIsLoading(true);
        const templates = await PDFService.getTemplates();
        setTemplates(templates);
      } catch (error) {
        console.error('Erreur lors du chargement des templates:', error);
        showToast('Erreur lors du chargement des templates', 'error');
      } finally {
        setIsLoading(false);
      }
    }
    loadTemplates();
  }, []);

  // Charger les documents générés
  useEffect(() => {
    async function loadDocuments() {
      try {
        const documents = await PDFService.getGeneratedDocuments();
        setGeneratedDocuments(documents);
      } catch (error) {
        console.error('Erreur lors du chargement des documents:', error);
        showToast('Erreur lors du chargement des documents', 'error');
      }
    }
    loadDocuments();
  }, []);

  // Sauvegarder les templates
  useEffect(() => {
    try {
      localStorage.setItem('pdfTemplates', JSON.stringify(templates));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des templates:', error);
      showToast('Erreur lors de la sauvegarde des templates', 'error');
    }
  }, [templates]);

  // Notifications
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // Gestion des templates
  const addTemplate = async (template: Partial<Template>) => {
    try {
      setIsLoading(true);
      const newTemplate = await PDFService.createTemplate(template);
      setTemplates(prev => [...prev, newTemplate]);
      showToast('Template ajouté avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'ajout du template:', error);
      showToast('Erreur lors de l\'ajout du template', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const editTemplate = async (template: Partial<Template>) => {
    try {
      setIsLoading(true);
      const updatedTemplate = await PDFService.updateTemplate(template.id!, template);
      setTemplates(prev => 
        prev.map(t => t.id === template.id ? updatedTemplate : t)
      );
      showToast('Template modifié avec succès');
    } catch (error) {
      console.error('Erreur lors de la modification du template:', error);
      showToast('Erreur lors de la modification du template', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTemplate = async (templateId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce template ?')) {
      try {
        setIsLoading(true);
        await PDFService.deleteTemplate(templateId);
        setTemplates(prev => prev.filter(t => t.id !== templateId));
        showToast('Template supprimé avec succès');
      } catch (error) {
        console.error('Erreur lors de la suppression du template:', error);
        showToast('Erreur lors de la suppression du template', 'error');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Génération de PDF
  const generatePDF = async (templateId: number, data: any) => {
    try {
      setIsLoading(true);
      const pdfBlob = await PDFService.generatePDF(templateId, data);
      
      // Créer une URL pour le téléchargement
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `document_${Date.now()}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      showToast('PDF généré avec succès');
      setShowGenerateModal(false);
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      showToast('Erreur lors de la génération du PDF', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const previewPDF = async (templateId: number, data: any) => {
    try {
      setIsLoading(true);
      const previewUrl = await PDFService.previewPDF(templateId, data);
      setPreviewUrl(previewUrl);
    } catch (error) {
      console.error('Erreur lors de la prévisualisation du PDF:', error);
      showToast('Erreur lors de la prévisualisation du PDF', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handlers
  const handleTemplateEditorSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateTemplate(templateEditorState);
    if (errors.length > 0) {
      showToast(errors.join(', '), 'error');
      return;
    }

    if (selectedTemplate) {
      editTemplate({ ...templateEditorState, id: selectedTemplate.id });
    } else {
      addTemplate(templateEditorState);
    }

    setShowTemplateEditor(false);
    setSelectedTemplate(null);
    setTemplateEditorState({
      name: '',
      description: '',
      category: '',
      type: '',
      color: 'bg-blue-500',
      icon: 'Car',
      fields: []
    });
  };

  const tabs = [
    { id: 'templates', name: 'Templates', icon: FileText },
    { id: 'generated', name: 'Documents générés', icon: Download },
    { id: 'settings', name: 'Configuration', icon: Settings }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Générateur PDF</h1>
          <p className="text-gray-600">Créez et gérez vos documents PDF</p>
        </div>
        <button className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors">
          <Plus className="h-4 w-4 mr-2" />
          Nouveau Template
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === tab.id
                    ? 'border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        {selectedTab === 'templates' && (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun template</h3>
            <p className="text-gray-500 mb-4">Créez votre premier template PDF</p>
            <button 
              onClick={() => showToast('Fonctionnalité en développement')}
              className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Créer un template
            </button>
          </div>
        )}

        {selectedTab === 'generated' && (
          <div className="space-y-6">
            {generatedDocuments.length === 0 ? (
              <div className="text-center py-12">
                <Download className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun document</h3>
                <p className="text-gray-500">Les documents générés apparaîtront ici</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nom
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Template
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {generatedDocuments.map((doc) => (
                      <tr key={doc.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{doc.templateName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {new Date(doc.createdAt).toLocaleDateString('fr-FR')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            doc.status === 'completed' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {doc.status === 'completed' ? 'Complété' : 'En cours'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                          <button
                            onClick={() => window.open(doc.previewUrl, '_blank')}
                            className="text-axa-blue hover:text-blue-700"
                          >
                            <Eye className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => PDFService.downloadDocument(doc.id)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <Download className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => deleteDocument(doc.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {selectedTab === 'settings' && (
          <div className="text-center py-12">
            <Settings className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration</h3>
            <p className="text-gray-500">Paramètres du générateur PDF</p>
          </div>
        )}
      </div>

      {/* Modal d'édition de template */}
      {showTemplateEditor && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedTemplate ? 'Modifier le template' : 'Nouveau template'}
              </h3>
              <button
                onClick={() => {
                  setShowTemplateEditor(false);
                  setSelectedTemplate(null);
                  setTemplateEditorState({
                    name: '',
                    description: '',
                    category: '',
                    type: '',
                    color: 'bg-blue-500',
                    icon: 'Car',
                    fields: []
                  });
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <form onSubmit={handleTemplateEditorSubmit} className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nom du template *</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.name || ''}
                    onChange={(e) => setTemplateEditorState(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.category || ''}
                    onChange={(e) => setTemplateEditorState(prev => ({ ...prev, category: e.target.value }))}
                    required
                  >
                    <option value="">Sélectionner une catégorie</option>
                    <option value="Devis">Devis</option>
                    <option value="Contrat">Contrat</option>
                    <option value="Attestation">Attestation</option>
                    <option value="Facture">Facture</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <textarea
                  rows={3}
                  className="w-full border border-gray-300 rounded p-2"
                  value={templateEditorState.description || ''}
                  onChange={(e) => setTemplateEditorState(prev => ({ ...prev, description: e.target.value }))}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type *</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.type || ''}
                    onChange={(e) => setTemplateEditorState(prev => ({ ...prev, type: e.target.value }))}
                    required
                  >
                    <option value="">Sélectionner un type</option>
                    <option value="auto">Auto</option>
                    <option value="habitation">Habitation</option>
                    <option value="sante">Santé</option>
                    <option value="general">Général</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Icône</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.icon || 'Car'}
                    onChange={(e) => setTemplateEditorState(prev => ({ ...prev, icon: e.target.value }))}
                  >
                    {Object.keys(iconMap).map((iconName) => (
                      <option key={iconName} value={iconName}>{iconName}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowTemplateEditor(false);
                    setSelectedTemplate(null);
                    setTemplateEditorState({
                      name: '',
                      description: '',
                      category: '',
                      type: '',
                      color: 'bg-blue-500',
                      icon: 'Car',
                      fields: []
                    });
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {selectedTemplate ? 'Enregistrer' : 'Créer'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Modal de génération de PDF */}
      {showGenerateModal && selectedTemplate && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                Générer un document PDF
              </h3>
              <button
                onClick={() => {
                  setShowGenerateModal(false);
                  setSelectedTemplate(null);
                  setDocumentFormData({});
                  setPreviewUrl(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">
                    Informations du document
                  </h4>
                  <div className="space-y-4">
                    {selectedTemplate.fields.map((field) => (
                      <div key={field}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {field.charAt(0).toUpperCase() + field.slice(1)}
                        </label>
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded-lg p-2"
                          value={documentFormData[field] || ''}
                          onChange={(e) => 
                            setDocumentFormData(prev => ({
                              ...prev,
                              [field]: e.target.value
                            }))
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={() => previewPDF(selectedTemplate.id, documentFormData)}
                    className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader className="h-5 w-5 animate-spin" />
                    ) : (
                      <>
                        <Eye className="h-5 w-5 mr-2" />
                        Prévisualiser
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => generatePDF(selectedTemplate.id, documentFormData)}
                    className="flex-1 flex items-center justify-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader className="h-5 w-5 animate-spin" />
                    ) : (
                      <>
                        <Download className="h-5 w-5 mr-2" />
                        Générer
                      </>
                    )}
                  </button>
                </div>
              </div>

              <div className="border-l border-gray-200 pl-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Prévisualisation
                </h4>
                {previewUrl ? (
                  <iframe
                    src={previewUrl}
                    className="w-full h-[600px] border border-gray-200 rounded-lg"
                    title="Prévisualisation PDF"
                  />
                ) : (
                  <div className="flex items-center justify-center h-[600px] bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-gray-500">
                      Cliquez sur "Prévisualiser" pour voir le document
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Indicateur de chargement global */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-4">
            <Loader className="h-8 w-8 animate-spin text-axa-blue" />
          </div>
        </div>
      )}

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export { PDFGenerator as default };
