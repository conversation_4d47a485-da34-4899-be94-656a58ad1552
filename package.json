{
  "name": "moumen-technique-prevoyance",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "server:dev": "cd server && npm run dev",
    "server:build": "cd server && npm run build",
    "server:start": "cd server && npm start",
    "server:install": "cd server && npm install",
    "db:migrate": "cd server && npm run db:migrate",
    "db:generate": "cd server && npm run db:generate",
    "db:seed": "cd server && npm run db:seed",
    "db:studio": "cd server && npm run db:studio",
    "fullstack:dev": "concurrently \"npm run server:dev\" \"npm run dev\"",
    "fullstack:install": "npm install && npm run server:install",
    "setup": "npm run fullstack:install && npm run db:generate && npm run db:migrate && npm run db:seed"
  },
  "dependencies": {
    "@hookform/resolvers": "^5.2.1",
// ...existing code...
    "@types/pdfkit": "^0.17.2",
    "all": "^0.0.0",
    "axios": "^1.11.0",
    "date-fns": "^2.30.0",
    "framer-motion": "^10.16.16",
    "lucide-react": "^0.344.0",
    "pdfkit": "^0.17.1",
    "react": "^18.3.1",
    "react-beautiful-dnd": "^13.1.1",
    "react-dom": "^18.3.1",
    "react-helmet-async": "^2.0.5",
    "react-hook-form": "^7.48.2",
    "react-router-dom": "^6.20.1",
    "recharts": "^2.8.0",
    "zod": "^4.0.15"
  },
  "devDependencies": {
    "@eslint/js": "^9.9.1",
    "@testing-library/jest-dom": "^6.6.4",
    "@testing-library/react": "^16.3.0",
    "@testing-library/user-event": "^14.6.1",
    "@types/react": "^18.3.5",
    "@types/react-dom": "^18.3.0",
    "@types/react-helmet-async": "^1.0.1",
    "@vitejs/plugin-react": "^4.3.1",
    "autoprefixer": "^10.4.18",
    "concurrently": "^9.2.0",
    "eslint": "^9.9.1",
    "eslint-plugin-react-hooks": "^5.1.0-rc.0",
    "eslint-plugin-react-refresh": "^0.4.11",
    "globals": "^15.9.0",
    "jsdom": "^26.1.0",
    "postcss": "^8.4.35",
    "tailwindcss": "^3.4.1",
    "terser": "^5.43.1",
    "typescript": "^5.5.3",
    "typescript-eslint": "^8.3.0",
    "vite": "^7.0.6",
    "vitest": "^3.2.4"
  }
}
