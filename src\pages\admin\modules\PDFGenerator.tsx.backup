import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Download,
  Eye,
  Settings,
  Plus,
  Edit,
  Copy,
  Trash2,
  Upload,
  Image,
  Type,
  Layout,
  Palette,
  Save,
  Send,
  Mail,
  Printer,
  Calendar,
  User,
  Building,
  Car,
  Home as HomeIcon,
  Heart,
  Shield,
  X,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Toast  } from '../../../components/common/Toast';
// import { Template } from '../../../types/admin';
 

// Mapping sécurisé nom d'icône -> composant React (whitelist stricte)
const iconMap = {
  Car,
  HomeIcon,
  Heart,
  Shield,
  FileText,
  Building,
  User,
  Calendar,
  Mail,
  Settings
};

// Fonction utilitaire pour valider un template
// function validateTemplate(template: Template): string[] {
//   const errors: string[] = [];
//
//   if (!template.name || template.name.trim().length === 0) {
//     errors.push('Le nom du template est requis');
//   }
//
//   if (!template.description || template.description.trim().length === 0) {
//     errors.push('La description du template est requise');
//   }
//
//   if (!template.category || template.category.trim().length === 0) {
//     errors.push('La catégorie du template est requise');
//   }
//
//   if (!template.type || template.type.trim().length === 0) {
//     errors.push('Le type du template est requis');
//   }
//
//   if (!iconMap[template.icon]) {
//     errors.push('L\'icône sélectionnée est invalide');
//   }
//
//   return errors;
// }

export const PDFGenerator: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('templates');
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [documentFormData, setDocumentFormData] = useState({});
  const [templateEditorState, setTemplateEditorState] = useState({});
  const [toast, setToast] = useState(null);

  // Fonctions utilitaires
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const downloadDocument = (document) => {
    // Simulation du téléchargement
    showToast('Document téléchargé avec succès !');
  };

  // Chargement sécurisé des templates depuis localStorage
  // const [templatesState, setTemplatesState] = useState(() => {
  //   try {
  //     const saved = localStorage.getItem('pdfTemplates');
  //     if (saved) {
  //       const parsed = JSON.parse(saved);
  //       if (parsed && Array.isArray(parsed)) {
  //         return parsed;
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Erreur lors du chargement des templates:', error);
  //   }
  //   return [
  //     {
  //       id: 1,
  //       name: 'Devis Assurance Auto',
  //       description: 'Template pour les devis d\'assurance automobile',
  //       category: 'Devis',
  //       type: 'auto',
  //       icon: 'Car',
  //       color: 'bg-blue-500',
  //       fields: ['client', 'vehicule', 'garanties', 'tarification'],
  //       lastModified: '2024-01-15',
  //       usage: 156,
  //       status: 'active'
  //     },
  //     {
  //       id: 2,
  //       name: 'Devis Assurance Habitation',
  //       description: 'Template pour les devis d\'assurance habitation',
  //       category: 'Devis',
  //       type: 'habitation',
  //       icon: 'HomeIcon',
  //       color: 'bg-green-500',
  //       fields: ['client', 'logement', 'garanties', 'tarification'],
  //       lastModified: '2024-01-14',
  //       usage: 89,
  //       status: 'active'
  //     },
  //     {
  //       id: 3,
  //       name: 'Contrat Assurance Santé',
  //       description: 'Template pour les contrats d\'assurance santé',
  //       category: 'Contrat',
  //       type: 'sante',
  //       icon: 'Heart',
  //       color: 'bg-red-500',
  //       fields: ['client', 'beneficiaires', 'garanties', 'conditions'],
  //       lastModified: '2024-01-12',
  //       usage: 67,
  //       status: 'active'
  //     },
  //     {
  //       id: 4,
  //       name: 'Attestation d\'Assurance',
  //       description: 'Template pour les attestations d\'assurance',
  //       category: 'Attestation',
  //       type: 'general',
  //       icon: 'Shield',
  //       color: 'bg-purple-500',
  //       fields: ['client', 'police', 'periode', 'garanties'],
  //       lastModified: '2024-01-10',
  //       usage: 234,
  //       status: 'active'
  //     },
  //     {
  //       id: 5,
  //       name: 'Facture Prime',
  //       description: 'Template pour les factures de prime d\'assurance',
  //       category: 'Facture',
  //       type: 'billing',
  //       icon: 'FileText',
  //       color: 'bg-yellow-500',
  //       fields: ['client', 'police', 'montants', 'echeances'],
  //       lastModified: '2024-01-08',
  //       usage: 178,
  //       status: 'active'
  //     }
  //   ];
  // });

  // Chargement sécurisé des documents générés depuis localStorage
  const [generatedDocuments, setGeneratedDocuments] = useState(() => {
    try {
      const saved = localStorage.getItem('generatedPDFDocuments');
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed && Array.isArray(parsed)) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
    }
    return [
      {
        id: 1,
        name: 'Devis_Auto_Ahmed_Benali_2024001.pdf',
        template: 'Devis Assurance Auto',
        client: 'Ahmed Benali',
        generatedDate: '2024-01-15 14:30',
        generatedBy: 'Mohammed MOUMEN',
        status: 'sent',
        size: '245 KB',
        downloads: 3,
        templateId: 1,
        data: {
          nom_complet: 'Ahmed Benali',
          adresse: '123 Rue Hassan II, Casablanca',
          telephone: '+212 6XX-XXXXXX',
          email: '<EMAIL>',
          marque: 'Renault',
          modele: 'Clio',
          annee: '2020',
          immatriculation: '12345-A-6'
        }
      },
      {
        id: 2,
        name: 'Contrat_Habitation_Fatima_Zahra_2024002.pdf',
        template: 'Devis Assurance Habitation',
        client: 'Fatima Zahra',
        generatedDate: '2024-01-14 16:45',
        generatedBy: 'Fatima ALAMI',
        status: 'signed',
        size: '312 KB',
        downloads: 5,
        templateId: 2,
        data: {
          nom_complet: 'Fatima Zahra',
          adresse: '456 Avenue Mohammed VI, Rabat',
          telephone: '+212 6XX-XXXXXX',
          email: '<EMAIL>',
          type_logement: 'Appartement',
          surface: '80m²',
          adresse_logement: '456 Avenue Mohammed VI, Rabat'
        }
      }
    ];
  });

  // États pour les modals
  const [showGenerateModal, setShowGenerateModal] = useState(false);

  // State pour l'édition/création de template
  // const [templateEditorState, setTemplateEditorState] = useState({
  //   name: '',
  //   description: '',
  //   category: '',
  //   type: '',
  //   color: 'bg-blue-500',
  //   icon: 'Car',
  //   fields: [],
  // });

  // Fonction pour afficher une notification
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // Sauvegarde sécurisée des templates
  useEffect(() => {
    try {
//       localStorage.setItem('pdfTemplates', JSON.stringify(templatesState));
 
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des templates:', error);
      showToast('Erreur lors de la sauvegarde des templates', 'error');
    }
  }, [templatesState]);

  // Sauvegarde sécurisée des documents générés
  useEffect(() => {
    try {
      localStorage.setItem('generatedPDFDocuments', JSON.stringify(generatedDocuments));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des documents:', error);
      showToast('Erreur lors de la sauvegarde des documents', 'error');
    }
  }, [generatedDocuments]);

//   const addTemplate = (template) => {

//     setTemplatesState(prev => [...prev, { ...template, id: Date.now() }]);

//     showToast('Template ajouté avec succès !');

//   };

//   const editTemplate = (template) => {

//     setTemplatesState(prev => prev.map(t => t.id === template.id ? { ...t, ...template } : t));

//     showToast('Template modifié avec succès !');

//   };

//   const deleteTemplate = (id) => {

//     if (confirm('Supprimer ce template ?')) {
//       setTemplatesState(prev => prev.filter(t => t.id !== id));

//       showToast('Template supprimé avec succès !');

//     }
//   };

  // Ouvre la modal pour ajout ou édition
//   const openTemplateEditor = (template = null) => {

//     setSelectedTemplate(template);

//     setTemplateEditorState(template ? {
//       name: template.name || '',
//       description: template.description || '',
//       category: template.category || '',
//       type: template.type || '',
//       color: template.color || 'bg-blue-500',
//       icon: template.icon || 'Car',
//       fields: template.fields || [],
//       id: template.id
//     } : {
//       name: '', description: '', category: '', type: '', color: 'bg-blue-500', icon: 'Car', fields: []
//     });
//     setShowTemplateEditor(true);

//   };

  // Handler de soumission avec validation
//   const handleTemplateEditorSubmit = (e) => {
//     e.preventDefault();
//     const errors = validateTemplate(templateEditorState);
//     if (errors.length > 0) {
//       showToast(errors.join(', '), 'error');
//       return;
//     }
//     if (selectedTemplate) {
//       editTemplate({ ...templateEditorState });
//     } else {
//       addTemplate({ ...templateEditorState });
//     }
//     setShowTemplateEditor(false);
//     setSelectedTemplate(null);
//   };

  const tabs = [
//     { id: 'templates', name: 'Templates', icon: Layout },
 
    { id: 'generated', name: 'Documents générés', icon: FileText },
    { id: 'settings', name: 'Configuration', icon: Settings }
  ];

  const templateFields = {
    client: [
      { name: 'nom_complet', label: 'Nom complet', type: 'text', required: true },
      { name: 'adresse', label: 'Adresse', type: 'textarea', required: true },
      { name: 'telephone', label: 'Téléphone', type: 'tel', required: true },
      { name: 'email', label: 'Email', type: 'email', required: true },
      { name: 'date_naissance', label: 'Date de naissance', type: 'date', required: false }
    ],
    vehicule: [
      { name: 'marque', label: 'Marque', type: 'text', required: true },
      { name: 'modele', label: 'Modèle', type: 'text', required: true },
      { name: 'annee', label: 'Année', type: 'number', required: true },
      { name: 'immatriculation', label: 'Immatriculation', type: 'text', required: true },
      { name: 'puissance', label: 'Puissance fiscale', type: 'number', required: true }
    ],
    garanties: [
      { name: 'responsabilite_civile', label: 'Responsabilité civile', type: 'checkbox', required: true },
      { name: 'dommages_collision', label: 'Dommages collision', type: 'checkbox', required: false },
      { name: 'vol_incendie', label: 'Vol et incendie', type: 'checkbox', required: false },
      { name: 'bris_glace', label: 'Bris de glace', type: 'checkbox', required: false },
      { name: 'assistance', label: 'Assistance', type: 'checkbox', required: false }
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'signed': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-purple-100 text-purple-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Types de champs disponibles pour un template PDF
  const pdfFieldTypes = [
    { id: 'text', name: 'Texte' },
    { id: 'number', name: 'Nombre' },
    { id: 'date', name: 'Date' },
    { id: 'email', name: 'Email' },
    { id: 'checkbox', name: 'Case à cocher' },
    { id: 'file', name: 'Fichier' },
  ];

  // Fonction pour générer un document PDF avec feedback
  const generatePDF = (templateId, formData) => {
    try {
      const template = templatesState.find(t => t.id === templateId);
      if (!template) {
//         showToast('Template introuvable', 'error');
 
        return;
      }

      const newDocument = {
        id: Date.now(),
        name: `${template.name.replace(/\s+/g, '_')}_${formData.nom_complet || 'Client'}_${Date.now()}.pdf`,
        template: template.name,
        client: formData.nom_complet || 'Client',
        generatedDate: new Date().toLocaleString('fr-FR'),
        generatedBy: 'Utilisateur actuel',
        status: 'draft',
        size: `${Math.floor(Math.random() * 200) + 150} KB`,
        downloads: 0,
        templateId: templateId,
        data: formData
      };

      setGeneratedDocuments(prev => [newDocument, ...prev]);
      setShowGenerateModal(false);
      setDocumentFormData({});
      showToast('Document PDF généré avec succès !');
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      showToast('Erreur lors de la génération du PDF', 'error');
    }
  };

  // Fonction pour télécharger un document avec feedback
  const downloadDocument = (document) => {
    try {
      // Simulation de téléchargement
      const link = document.createElement('a');
      link.href = `data:application/pdf;base64,${btoa('Contenu PDF simulé')}`;
      link.download = document.name;
      link.click();
      
      // Mettre à jour le compteur de téléchargements
      setGeneratedDocuments(prev => prev.map(doc => 
        doc.id === document.id 
          ? { ...doc, downloads: doc.downloads + 1 }
          : doc
      ));
      showToast('Document téléchargé avec succès !');
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      showToast('Erreur lors du téléchargement', 'error');
    }
  };

  // Fonction pour supprimer un document avec feedback
  const deleteDocument = (documentId) => {
    if (confirm('Supprimer ce document ?')) {
      setGeneratedDocuments(prev => prev.filter(doc => doc.id !== documentId));
      showToast('Document supprimé avec succès !');
    }
  };

  // Fonction pour ouvrir la modal de génération
  const openGenerateModal = (template) => {
//     setSelectedTemplate(template);
 
    setDocumentFormData({});
    setShowGenerateModal(true);
  };

  // Fonction pour ouvrir la modal de prévisualisation
  const openPreviewModal = (document) => {
    setSelectedDocument(document);
    setShowPreviewModal(true);
  };

  // Validation du formulaire d'édition de template
//   const isTemplateFormValid = () => {
 
    return templateEditorState.name.trim() && 
           templateEditorState.description.trim() && 
           templateEditorState.category.trim() && 
           templateEditorState.type.trim();
  };

  // Récupération sécurisée d'une icône
  const getIconComponent = (iconName) => {
    return iconMap[iconName] || FileText;
  };

  return (
    <div className="space-y-8">
      {/* Notifications Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>

      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Générateur PDF</h1>
          <p className="text-gray-600 mt-2">Créez et gérez vos templates et documents PDF</p>
        </div>
        <div className="flex space-x-4">
          <button
//             onClick={() => openTemplateEditor()}
 
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau template
          </button>
        </div>
      </motion.div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <div className="flex space-x-4 border-b border-gray-200 pb-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedTab === tab.id
                    ? 'bg-axa-blue text-white'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </div>

//         {/* Templates Tab */}
 
        {selectedTab === 'templates' && (
          <div className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templatesState.map((template) => {
                const IconComponent = getIconComponent(template.icon);
                return (
                  <div key={template.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`${template.color} w-10 h-10 rounded-lg flex items-center justify-center`}>
                          <IconComponent className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{template.name}</h3>
                          <p className="text-sm text-gray-600">{template.category}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        template.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {template.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Utilisations:</span>
                        <span className="font-medium">{template.usage}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Dernière modification:</span>
                        <span className="font-medium">{template.lastModified}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openGenerateModal(template)}
                        className="flex-1 px-3 py-2 text-sm bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                      >
                        Générer
                      </button>
                      <button
//                         onClick={() => openTemplateEditor(template)}
 
                        className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
//                         onClick={() => deleteTemplate(template.id)}
 
                        className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-red-50 hover:border-red-300 transition-colors"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Generated Documents Tab */}
        {selectedTab === 'generated' && (
          <div className="mt-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Document</th>
//                     <th className="text-left py-3 px-4 font-semibold text-gray-900">Template</th>
 
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Client</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Statut</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Téléchargements</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {generatedDocuments.map((document) => (
                    <tr key={document.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">{document.name}</div>
                        <div className="text-sm text-gray-500">{document.size}</div>
                      </td>
                      <td className="py-4 px-4">{document.template}</td>
                      <td className="py-4 px-4">{document.client}</td>
                      <td className="py-4 px-4">{document.generatedDate}</td>
                      <td className="py-4 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                          {document.status}
                        </span>
                      </td>
                      <td className="py-4 px-4">{document.downloads}</td>
                      <td className="py-4 px-4">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => openPreviewModal(document)}
                            className="p-1 text-gray-400 hover:text-blue-600"
                            title="Prévisualiser"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => downloadDocument(document)}
                            className="p-1 text-gray-400 hover:text-green-600"
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteDocument(document.id)}
                            className="p-1 text-gray-400 hover:text-red-600"
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {selectedTab === 'settings' && (
          <div className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration PDF</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Format par défaut</label>
                    <select className="w-full border border-gray-300 rounded-lg p-2">
                      <option value="A4">A4</option>
                      <option value="Letter">Letter</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Orientation</label>
                    <select className="w-full border border-gray-300 rounded-lg p-2">
                      <option value="portrait">Portrait</option>
                      <option value="landscape">Paysage</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Notifications</h3>
                <div className="space-y-4">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-axa-blue focus:ring-axa-blue" defaultChecked />
                    <span className="ml-2 text-sm text-gray-700">Notifications par email</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-axa-blue focus:ring-axa-blue" defaultChecked />
                    <span className="ml-2 text-sm text-gray-700">Notifications push</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Modal d'édition/création de template */}
//       {showTemplateEditor && (
 
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
//                 {selectedTemplate ? 'Modifier le template' : 'Nouveau template'}
 
              </h3>
              <button
//                 onClick={() => { setShowTemplateEditor(false); setSelectedTemplate(null); }}
 
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
//             <form onSubmit={handleTemplateEditorSubmit} className="p-6 space-y-6">
 
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nom du template *</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.name}
//                     onChange={(e) => setTemplateEditorState({ ...templateEditorState, name: e.target.value })}
 
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.category}
//                     onChange={(e) => setTemplateEditorState({ ...templateEditorState, category: e.target.value })}
 
                    required
                  >
                    <option value="">Sélectionner une catégorie</option>
                    <option value="Devis">Devis</option>
                    <option value="Contrat">Contrat</option>
                    <option value="Attestation">Attestation</option>
                    <option value="Facture">Facture</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type *</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.type}
//                     onChange={(e) => setTemplateEditorState({ ...templateEditorState, type: e.target.value })}
 
                    required
                  >
                    <option value="">Sélectionner un type</option>
                    <option value="auto">Auto</option>
                    <option value="habitation">Habitation</option>
                    <option value="sante">Santé</option>
                    <option value="general">Général</option>
                    <option value="billing">Facturation</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Couleur</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.color}
//                     onChange={(e) => setTemplateEditorState({ ...templateEditorState, color: e.target.value })}
 
                  >
                    <option value="bg-blue-500">Bleu</option>
                    <option value="bg-green-500">Vert</option>
                    <option value="bg-red-500">Rouge</option>
                    <option value="bg-purple-500">Violet</option>
                    <option value="bg-yellow-500">Jaune</option>
                    <option value="bg-indigo-500">Indigo</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Icône</label>
                  <select
                    className="w-full border border-gray-300 rounded p-2"
                    value={templateEditorState.icon}
//                     onChange={(e) => setTemplateEditorState({ ...templateEditorState, icon: e.target.value })}
 
                  >
                    {Object.keys(iconMap).map((iconName) => {
                      const IconComponent = iconMap[iconName];
                      return (
                        <option key={iconName} value={iconName}>
                          {iconName}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <textarea
                  rows={3}
                  className="w-full border border-gray-300 rounded p-2"
                  value={templateEditorState.description}
//                   onChange={(e) => setTemplateEditorState({ ...templateEditorState, description: e.target.value })}
 
                  required
                />
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
//                   onClick={() => { setShowTemplateEditor(false); setSelectedTemplate(null); }}
 
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Annuler
                </button>
                <button
                  type="submit"
//                   disabled={!isTemplateFormValid()}
                  className="px-4 py-2 rounded-lg transition-colors bg-axa-blue text-white hover:bg-blue-800"
                >
                  Créer
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Modal de génération de document PDF */}
//       {showGenerateModal && selectedTemplate && (
 
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">Générer un document PDF</h3>
              <button
//                 onClick={() => { setShowGenerateModal(false); setSelectedTemplate(null); setDocumentFormData({}); }}
 
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
//             <form onSubmit={(e) => { e.preventDefault(); generatePDF(selectedTemplate.id, documentFormData); }} className="p-6">
 
              <div className="mb-4">
//                 <h4 className="font-semibold text-gray-900 mb-2">Template: {selectedTemplate.name}</h4>
 
//                 <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
 
              </div>
              
              <div className="space-y-4">
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Formulaire de génération de document</p>
                </div>
              </div>
              
              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
//                   onClick={() => { setShowGenerateModal(false); setSelectedTemplate(null); setDocumentFormData({}); }}
 
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                >
                  Générer le PDF
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Modal de prévisualisation de document */}
      {showPreviewModal && selectedDocument && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">Prévisualisation du document</h3>
              <button
                onClick={() => { setShowPreviewModal(false); setSelectedDocument(null); }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{selectedDocument.name}</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
//                     <span className="text-gray-600">Template:</span>
 
                    <div className="font-medium">{selectedDocument.template}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Client:</span>
                    <div className="font-medium">{selectedDocument.client}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Date:</span>
                    <div className="font-medium">{selectedDocument.generatedDate}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Statut:</span>
                    <div className="font-medium">{selectedDocument.status}</div>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                <h5 className="font-semibold text-gray-900 mb-4">Contenu du document</h5>
                <div className="space-y-2">
                  {Object.entries(selectedDocument.data || {}).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600">{key}:</span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-4 mt-6">
                <button
                  onClick={() => downloadDocument(selectedDocument)}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger
                </button>
                <button
                  onClick={() => { setShowPreviewModal(false); setSelectedDocument(null); }}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Fermer
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};
export { PDFGenerator as default };