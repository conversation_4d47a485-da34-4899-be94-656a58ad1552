import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { 
  Lock, 
  User, 
  FileText, 
  CreditCard, 
  AlertCircle,
  Download,
  Eye,
  Calendar,
  Phone,
  Mail,
  Shield
} from 'lucide-react';

interface LoginForm {
  email: string;
  password: string;
}

export const ClientSpace: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const { register, handleSubmit, formState: { errors } } = useForm<LoginForm>();

  const onLogin = (data: LoginForm) => {
    console.log('Login attempt:', data);
    // Simulate login
    setIsLoggedIn(true);
  };

  const contracts = [
    {
      id: 'AUTO001',
      type: 'Assurance Auto',
      vehicle: 'Renault Clio IV',
      status: 'Actif',
      expiry: '2024-12-15',
      premium: '2,400 DH'
    },
    {
      id: 'HAB001',
      type: 'Assurance Habitation',
      property: 'Appartement - Casablanca',
      status: 'Actif',
      expiry: '2024-08-20',
      premium: '1,800 DH'
    }
  ];

  const documents = [
    { name: 'Attestation Auto 2024', type: 'PDF', date: '2024-01-15' },
    { name: 'Contrat Habitation', type: 'PDF', date: '2023-08-20' },
    { name: 'Facture Prime Auto', type: 'PDF', date: '2024-01-01' },
    { name: 'Conditions Générales', type: 'PDF', date: '2023-08-20' }
  ];

  const claims = [
    {
      id: 'SIN001',
      type: 'Bris de glace',
      date: '2024-01-10',
      status: 'Réglé',
      amount: '800 DH'
    },
    {
      id: 'SIN002',
      type: 'Dégât des eaux',
      date: '2023-11-15',
      status: 'En cours',
      amount: '2,500 DH'
    }
  ];

  if (!isLoggedIn) {
    return (
      <div className="py-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-axa-blue to-blue-700 text-white py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <h1 className="text-4xl lg:text-6xl font-bold mb-6">
                Espace Client
              </h1>
              <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
                Accédez à vos contrats, documents et gérez vos assurances en ligne 24h/24.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Login Form */}
        <section className="py-20">
          <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl shadow-xl p-8"
            >
              <div className="text-center mb-8">
                <div className="bg-axa-blue w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lock className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-axa-blue mb-2">
                  Connexion Sécurisée
                </h2>
                <p className="text-gray-600">
                  Connectez-vous pour accéder à votre espace personnel
                </p>
              </div>

              <form onSubmit={handleSubmit(onLogin)} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    {...register('email', { 
                      required: 'L\'email est requis',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Email invalide'
                      }
                    })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent transition-colors"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mot de passe *
                  </label>
                  <input
                    type="password"
                    {...register('password', { required: 'Le mot de passe est requis' })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent transition-colors"
                    placeholder="••••••••"
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-axa-blue focus:ring-axa-blue" />
                    <span className="ml-2 text-sm text-gray-600">Se souvenir de moi</span>
                  </label>
                  <a href="#" className="text-sm text-axa-blue hover:text-axa-red transition-colors">
                    Mot de passe oublié ?
                  </a>
                </div>

                <button
                  type="submit"
                  className="w-full bg-axa-red hover:bg-red-600 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center"
                >
                  <User className="mr-2 h-5 w-5" />
                  Se connecter
                </button>
              </form>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-center text-sm text-gray-600">
                  Pas encore client ?{' '}
                  <a href="/devis" className="text-axa-blue hover:text-axa-red font-medium transition-colors">
                    Demandez votre devis
                  </a>
                </p>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-blue-800 font-medium">Connexion sécurisée</p>
                    <p className="text-xs text-blue-600 mt-1">
                      Vos données sont protégées par un chiffrement SSL 256 bits
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="py-20">
      {/* Welcome Section */}
      <section className="bg-gradient-to-br from-axa-blue to-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="flex justify-between items-center"
          >
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold mb-2">
                Bonjour, Ahmed BENALI
              </h1>
              <p className="text-blue-100">
                Bienvenue dans votre espace client personnel
              </p>
            </div>
            <button
              onClick={() => setIsLoggedIn(false)}
              className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
            >
              Déconnexion
            </button>
          </motion.div>
        </div>
      </section>

      {/* Dashboard */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Contrats actifs</p>
                  <p className="text-2xl font-bold text-axa-blue">2</p>
                </div>
                <FileText className="h-8 w-8 text-axa-blue" />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Prime annuelle</p>
                  <p className="text-2xl font-bold text-green-500">4,200 DH</p>
                </div>
                <CreditCard className="h-8 w-8 text-green-500" />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Sinistres en cours</p>
                  <p className="text-2xl font-bold text-orange-500">1</p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-500" />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Prochaine échéance</p>
                  <p className="text-2xl font-bold text-axa-red">15 Août</p>
                </div>
                <Calendar className="h-8 w-8 text-axa-red" />
              </div>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contracts */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h2 className="text-xl font-bold text-axa-blue mb-6">Mes Contrats</h2>
              <div className="space-y-4">
                {contracts.map((contract) => (
                  <div key={contract.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-900">{contract.type}</h3>
                        <p className="text-sm text-gray-600">{contract.vehicle || contract.property}</p>
                        <p className="text-xs text-gray-500">N° {contract.id}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        contract.status === 'Actif' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {contract.status}
                      </span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Échéance: {contract.expiry}</span>
                      <span className="font-semibold text-axa-blue">{contract.premium}/an</span>
                    </div>
                    <div className="flex space-x-2 mt-3">
                      <button className="flex items-center text-xs bg-axa-blue text-white px-3 py-1 rounded hover:bg-blue-800 transition-colors">
                        <Eye className="h-3 w-3 mr-1" />
                        Voir
                      </button>
                      <button className="flex items-center text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded hover:bg-gray-200 transition-colors">
                        <Download className="h-3 w-3 mr-1" />
                        Attestation
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Documents */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h2 className="text-xl font-bold text-axa-blue mb-6">Mes Documents</h2>
              <div className="space-y-3">
                {documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-axa-red" />
                      <div>
                        <p className="font-medium text-gray-900">{doc.name}</p>
                        <p className="text-xs text-gray-500">{doc.date}</p>
                      </div>
                    </div>
                    <button className="text-axa-blue hover:text-axa-red transition-colors">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Claims */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-white rounded-xl shadow-lg p-6 mt-8"
          >
            <h2 className="text-xl font-bold text-axa-blue mb-6">Mes Sinistres</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">N° Sinistre</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Statut</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Montant</th>
                  </tr>
                </thead>
                <tbody>
                  {claims.map((claim) => (
                    <tr key={claim.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 text-sm">{claim.id}</td>
                      <td className="py-3 px-4 text-sm">{claim.type}</td>
                      <td className="py-3 px-4 text-sm">{claim.date}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          claim.status === 'Réglé' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {claim.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm font-semibold">{claim.amount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="bg-gradient-to-r from-axa-blue to-blue-700 rounded-xl p-8 text-white mt-8"
          >
            <h2 className="text-2xl font-bold mb-4">Besoin d'aide ?</h2>
            <p className="text-blue-100 mb-6">
              Notre équipe est à votre disposition pour répondre à toutes vos questions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg transition-colors flex items-center justify-center">
                <Phone className="mr-2 h-5 w-5" />
                +212 5XX-XXXXXX
              </button>
              <button className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg transition-colors flex items-center justify-center">
                <Mail className="mr-2 h-5 w-5" />
                <EMAIL>
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};