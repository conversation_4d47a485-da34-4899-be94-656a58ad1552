import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Middleware de gestion d'erreurs global
 */
export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = error.statusCode || 500;
  let message = error.message || 'Erreur interne du serveur';
  let details: any = undefined;

  // Log de l'erreur
  logger.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Gestion des erreurs Prisma
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        statusCode = 409;
        message = 'Cette ressource existe déjà';
        details = { field: error.meta?.target };
        break;
      case 'P2025':
        statusCode = 404;
        message = 'Ressource non trouvée';
        break;
      case 'P2003':
        statusCode = 400;
        message = 'Violation de contrainte de clé étrangère';
        break;
      default:
        statusCode = 400;
        message = 'Erreur de base de données';
    }
  }

  // Gestion des erreurs de validation Prisma
  if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400;
    message = 'Données invalides';
  }

  // Gestion des erreurs de connexion Prisma
  if (error instanceof Prisma.PrismaClientInitializationError) {
    statusCode = 503;
    message = 'Service temporairement indisponible';
  }

  // En développement, inclure la stack trace
  if (process.env.NODE_ENV === 'development') {
    details = {
      ...details,
      stack: error.stack,
    };
  }

  // Réponse d'erreur
  res.status(statusCode).json({
    success: false,
    message,
    ...(details && { details }),
    timestamp: new Date().toISOString(),
    path: req.path,
  });
};

/**
 * Middleware pour capturer les erreurs asynchrones
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Créer une erreur personnalisée
 */
export const createError = (message: string, statusCode: number = 500): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};

/**
 * Middleware pour les routes non trouvées
 */
export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = createError(`Route ${req.originalUrl} non trouvée`, 404);
  next(error);
};
