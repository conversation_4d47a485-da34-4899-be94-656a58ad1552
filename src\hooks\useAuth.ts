import { useState, useEffect, createContext, useContext } from 'react';

// Types pour l'authentification
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (user: User) => void;
}

// Contexte d'authentification
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook useAuth
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    // Si pas de contexte, retourner un état par défaut
    return useAuthState();
  }
  return context;
};

// Hook d'état d'authentification standalone
export const useAuthState = (): AuthContextType => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true
  });

  useEffect(() => {
    // Vérifier si l'utilisateur est déjà connecté
    const checkAuth = () => {
      try {
        const savedUser = localStorage.getItem('currentUser');
        const authToken = localStorage.getItem('authToken');
        
        if (savedUser && authToken) {
          const user = JSON.parse(savedUser);
          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false
          });
        } else {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'authentification:', error);
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false
        });
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true }));

    try {
      // Simulation d'un appel API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Vérification des identifiants (simulation)
      if (email === '<EMAIL>' && password === 'Admin123!') {
        const user: User = {
          id: '1',
          name: 'Admin Moumen AXA',
          email: '<EMAIL>',
          role: 'admin',
          avatar: undefined
        };

        // Sauvegarder dans localStorage
        localStorage.setItem('currentUser', JSON.stringify(user));
        localStorage.setItem('authToken', 'mock-jwt-token');

        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false
        });

        return true;
      } else {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false
        });
        return false;
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false
      });
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false
    });
  };

  const updateUser = (user: User) => {
    localStorage.setItem('currentUser', JSON.stringify(user));
    setAuthState(prev => ({
      ...prev,
      user
    }));
  };

  return {
    ...authState,
    login,
    logout,
    updateUser
  };
};

// Provider d'authentification
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const auth = useAuthState();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook pour vérifier les permissions
export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin a toutes les permissions
    if (user.role === 'admin') return true;
    
    // Logique de permissions basée sur le rôle
    const rolePermissions: Record<string, string[]> = {
      manager: ['read', 'write', 'manage_users'],
      agent: ['read', 'write'],
      viewer: ['read']
    };

    return rolePermissions[user.role]?.includes(permission) || false;
  };

  const canAccess = (module: string): boolean => {
    if (!user) return false;
    
    // Admin peut accéder à tout
    if (user.role === 'admin') return true;
    
    // Logique d'accès aux modules
    const moduleAccess: Record<string, string[]> = {
      dashboard: ['admin', 'manager', 'agent', 'viewer'],
      clients: ['admin', 'manager', 'agent'],
      leads: ['admin', 'manager', 'agent'],
      claims: ['admin', 'manager', 'agent'],
      analytics: ['admin', 'manager'],
      administration: ['admin'],
      security: ['admin'],
      settings: ['admin', 'manager']
    };

    return moduleAccess[module]?.includes(user.role) || false;
  };

  return {
    hasPermission,
    canAccess,
    isAdmin: user?.role === 'admin',
    isManager: user?.role === 'manager',
    isAgent: user?.role === 'agent',
    isViewer: user?.role === 'viewer'
  };
};
