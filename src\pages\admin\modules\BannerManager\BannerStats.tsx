import React from 'react';
import { Banner } from './types';
import {
  <PERSON>,
  Clock,
  <PERSON>,
  <PERSON>,
  <PERSON>Pointer,
  BarChart2,
  Target,
} from 'lucide-react';

interface BannerStatsProps {
  banner: Banner;
}

export const BannerStats: React.FC<BannerStatsProps> = ({ banner }) => {
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const calculateCTR = () => {
    if (banner.views === 0) return 0;
    return ((banner.clicks / banner.views) * 100).toFixed(2);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center mb-2">
          <Calendar className="h-5 w-5 text-gray-400 mr-2" />
          <h4 className="text-sm font-medium text-gray-700">Période</h4>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-600">
            Du {formatDate(banner.startDate)}
          </p>
          <p className="text-sm text-gray-600">
            Au {formatDate(banner.endDate)}
          </p>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center mb-2">
          <Target className="h-5 w-5 text-gray-400 mr-2" />
          <h4 className="text-sm font-medium text-gray-700">Pages cibles</h4>
        </div>
        <div className="space-y-1">
          {banner.targetPages.map((page) => (
            <p key={page} className="text-sm text-gray-600">
              {page}
            </p>
          ))}
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center mb-2">
          <Eye className="h-5 w-5 text-gray-400 mr-2" />
          <h4 className="text-sm font-medium text-gray-700">Performances</h4>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-600">
            {banner.views.toLocaleString()} vues
          </p>
          <p className="text-sm text-gray-600">
            {banner.clicks.toLocaleString()} clics
          </p>
          <p className="text-sm text-gray-600">
            CTR: {calculateCTR()}%
          </p>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center mb-2">
          <BarChart2 className="h-5 w-5 text-gray-400 mr-2" />
          <h4 className="text-sm font-medium text-gray-700">Détails</h4>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-600">
            Priorité: {banner.priority}
          </p>
          <p className="text-sm text-gray-600">
            Contact: {banner.showContact ? 'Oui' : 'Non'}
          </p>
          <p className="text-sm text-gray-600">
            Dernière modification: {formatTime(banner.updatedAt)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default BannerStats;
