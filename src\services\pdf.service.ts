import axios from 'axios';
import { Template } from '../types/admin';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export class PDFService {
  static async getTemplates(): Promise<Template[]> {
    try {
      const response = await axios.get(`${API_URL}/templates`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des templates:', error);
      throw error;
    }
  }

  static async createTemplate(template: Partial<Template>): Promise<Template> {
    try {
      const response = await axios.post(`${API_URL}/templates`, template);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du template:', error);
      throw error;
    }
  }

  static async updateTemplate(id: number, template: Partial<Template>): Promise<Template> {
    try {
      const response = await axios.put(`${API_URL}/templates/${id}`, template);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du template:', error);
      throw error;
    }
  }

  static async deleteTemplate(id: number): Promise<void> {
    try {
      await axios.delete(`${API_URL}/templates/${id}`);
    } catch (error) {
      console.error('Erreur lors de la suppression du template:', error);
      throw error;
    }
  }

  static async generatePDF(templateId: number, data: any): Promise<Blob> {
    try {
      const response = await axios.post(
        `${API_URL}/templates/${templateId}/generate`,
        data,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      throw error;
    }
  }

  static async previewPDF(templateId: number, data: any): Promise<string> {
    try {
      const response = await axios.post(
        `${API_URL}/templates/${templateId}/preview`,
        data
      );
      return response.data.previewUrl;
    } catch (error) {
      console.error('Erreur lors de la prévisualisation du PDF:', error);
      throw error;
    }
  }

  static async getGeneratedDocuments(): Promise<any[]> {
    try {
      const response = await axios.get(`${API_URL}/documents`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des documents:', error);
      throw error;
    }
  }

  static async downloadDocument(documentId: number): Promise<Blob> {
    try {
      const response = await axios.get(
        `${API_URL}/documents/${documentId}/download`,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors du téléchargement du document:', error);
      throw error;
    }
  }
}
