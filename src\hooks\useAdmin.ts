import { useState, useEffect, useCallback } from 'react';
import { AdminUser, AdminStats, Toast } from '../types/admin';

// Hook principal pour l'administration
export const useAdmin = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [stats, setStats] = useState<AdminStats | null>(null);

  useEffect(() => {
    // Charger les données utilisateur depuis localStorage ou API
    const userData = localStorage.getItem('adminUser');
    if (userData) {
      setCurrentUser(JSON.parse(userData));
    }

    // Charger les statistiques
    loadStats();
  }, []);

  const loadStats = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockStats: AdminStats = {
        totalUsers: 156,
        totalClients: 1247,
        totalLeads: 89,
        totalPolicies: 2341,
        totalClaims: 45,
        monthlyRevenue: 2450000,
        conversionRate: 23.5,
        activeUsers: 12
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateUser = useCallback((user: AdminUser) => {
    setCurrentUser(user);
    localStorage.setItem('adminUser', JSON.stringify(user));
  }, []);

  return {
    isLoading,
    setIsLoading,
    currentUser,
    stats,
    updateUser,
    loadStats
  };
};
