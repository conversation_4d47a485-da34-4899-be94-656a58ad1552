import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Shield,
  FileText,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Upload,
  Send,
  Eye,
  Edit,
  Calendar,
  Bell,
  Settings,
  BarChart3,
  <PERSON><PERSON>hart,
  <PERSON>rendingUp,
  TrendingDown,
  Activity,
  Target,
  Award,
  Gavel,
  BookOpen,
  Users,
  DollarSign,
  Calculator,
  RefreshCw,
  Filter,
  Search,
  Plus,
  MoreHorizontal,
  AlertCircle,
  Info,
  ExternalLink
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface ACAPSReport {
  id: string;
  reportType: 'QUARTERLY' | 'ANNUAL' | 'SOLVENCY' | 'CLAIMS' | 'FINANCIAL' | 'GOVERNANCE' | 'RISK_MANAGEMENT';
  reportName: string;
  period: {
    year: number;
    quarter?: number;
    startDate: string;
    endDate: string;
  };
  status: 'DRAFT' | 'IN_REVIEW' | 'APPROVED' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  dueDate: string;
  submissionDate?: string;
  approvalDate?: string;
  data: {
    [key: string]: any;
  };
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uploadDate: string;
  }>;
  validationErrors: Array<{
    field: string;
    message: string;
    severity: 'ERROR' | 'WARNING' | 'INFO';
  }>;
  assignedTo: {
    id: string;
    name: string;
    role: string;
  };
  reviewHistory: Array<{
    date: string;
    action: string;
    user: string;
    comments?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

interface ComplianceMetric {
  id: string;
  category: 'SOLVENCY' | 'RESERVES' | 'CAPITAL' | 'GOVERNANCE' | 'RISK' | 'CONDUCT';
  name: string;
  description: string;
  currentValue: number;
  targetValue: number;
  minimumValue: number;
  unit: string;
  status: 'COMPLIANT' | 'WARNING' | 'NON_COMPLIANT';
  trend: 'IMPROVING' | 'STABLE' | 'DETERIORATING';
  lastUpdated: string;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
}

interface RegulatoryAlert {
  id: string;
  type: 'DEADLINE' | 'THRESHOLD' | 'REGULATORY_CHANGE' | 'AUDIT_FINDING' | 'SANCTION';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  dueDate?: string;
  actionRequired: string;
  assignedTo: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'OVERDUE';
  createdAt: string;
  resolvedAt?: string;
}

export const Compliance_ACAPS: React.FC = () => {
  const [reports, setReports] = useState<ACAPSReport[]>([]);
  const [metrics, setMetrics] = useState<ComplianceMetric[]>([]);
  const [alerts, setAlerts] = useState<RegulatoryAlert[]>([]);
  const [selectedReport, setSelectedReport] = useState<ACAPSReport | null>(null);
  const [showReportDetails, setShowReportDetails] = useState(false);
  const [showNewReportModal, setShowNewReportModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [toast, setToast] = useState<any>(null);

  useEffect(() => {
    loadComplianceData();
  }, []);

  const loadComplianceData = async () => {
    try {
      setIsLoading(true);
      const [reportsResponse, metricsResponse, alertsResponse] = await Promise.all([
        api.getACAPSReports(),
        api.getComplianceMetrics(),
        api.getRegulatoryAlerts()
      ]);

      if (reportsResponse.success) {
        setReports(reportsResponse.data?.reports || []);
      } else {
        // Données mock pour les rapports ACAPS
        setReports([
          {
            id: 'RPT-2024-Q1-001',
            reportType: 'QUARTERLY',
            reportName: 'Rapport Trimestriel Q1 2024',
            period: {
              year: 2024,
              quarter: 1,
              startDate: '2024-01-01',
              endDate: '2024-03-31'
            },
            status: 'SUBMITTED',
            dueDate: '2024-04-30',
            submissionDate: '2024-04-25',
            approvalDate: '2024-04-24',
            data: {
              solvencyRatio: 1.45,
              totalAssets: 125000000,
              totalLiabilities: 95000000,
              ownFunds: 30000000,
              premiumsWritten: 12500000,
              claimsPaid: 8100000
            },
            attachments: [
              {
                id: 'ATT-001',
                name: 'Rapport_Q1_2024.pdf',
                type: 'application/pdf',
                size: 2048576,
                uploadDate: '2024-04-24'
              }
            ],
            validationErrors: [],
            assignedTo: {
              id: 'USER-001',
              name: 'Fatima MOUMEN',
              role: 'Responsable Conformité'
            },
            reviewHistory: [
              {
                date: '2024-04-24T10:00:00Z',
                action: 'APPROVED',
                user: 'Hassan IDRISSI',
                comments: 'Rapport conforme aux exigences ACAPS'
              },
              {
                date: '2024-04-25T14:30:00Z',
                action: 'SUBMITTED',
                user: 'Fatima MOUMEN',
                comments: 'Soumission à l\'ACAPS via portail électronique'
              }
            ],
            createdAt: '2024-04-20T09:00:00Z',
            updatedAt: '2024-04-25T14:30:00Z'
          },
          {
            id: 'RPT-2024-SOL-001',
            reportType: 'SOLVENCY',
            reportName: 'Rapport de Solvabilité 2024',
            period: {
              year: 2024,
              startDate: '2024-01-01',
              endDate: '2024-12-31'
            },
            status: 'IN_REVIEW',
            dueDate: '2024-03-31',
            data: {
              solvencyRatio: 1.45,
              minimumCapitalRequirement: 15000000,
              availableCapital: 25000000,
              riskProfile: 'MODERATE'
            },
            attachments: [],
            validationErrors: [
              {
                field: 'technical_provisions',
                message: 'Provisions techniques à réviser selon nouvelle méthodologie',
                severity: 'WARNING'
              }
            ],
            assignedTo: {
              id: 'USER-002',
              name: 'Mohammed ALAMI',
              role: 'Actuaire'
            },
            reviewHistory: [
              {
                date: '2024-01-15T09:00:00Z',
                action: 'CREATED',
                user: 'Mohammed ALAMI'
              }
            ],
            createdAt: '2024-01-15T09:00:00Z',
            updatedAt: '2024-01-20T16:45:00Z'
          }
        ]);
      }

      if (metricsResponse.success) {
        setMetrics(metricsResponse.data?.metrics || []);
      } else {
        // Données mock pour les métriques de conformité
        setMetrics([
          {
            id: 'MET-001',
            category: 'SOLVENCY',
            name: 'Ratio de Solvabilité',
            description: 'Ratio entre les fonds propres et le capital de solvabilité requis',
            currentValue: 1.45,
            targetValue: 1.50,
            minimumValue: 1.00,
            unit: 'ratio',
            status: 'COMPLIANT',
            trend: 'STABLE',
            lastUpdated: '2024-01-15',
            frequency: 'QUARTERLY'
          },
          {
            id: 'MET-002',
            category: 'RESERVES',
            name: 'Adéquation des Réserves',
            description: 'Pourcentage de couverture des engagements par les réserves techniques',
            currentValue: 0.95,
            targetValue: 1.00,
            minimumValue: 0.90,
            unit: 'percentage',
            status: 'WARNING',
            trend: 'IMPROVING',
            lastUpdated: '2024-01-15',
            frequency: 'MONTHLY'
          },
          {
            id: 'MET-003',
            category: 'CAPITAL',
            name: 'Ratio de Fonds Propres',
            description: 'Ratio entre les fonds propres et le total du bilan',
            currentValue: 0.24,
            targetValue: 0.25,
            minimumValue: 0.15,
            unit: 'percentage',
            status: 'COMPLIANT',
            trend: 'STABLE',
            lastUpdated: '2024-01-15',
            frequency: 'QUARTERLY'
          },
          {
            id: 'MET-004',
            category: 'GOVERNANCE',
            name: 'Score de Gouvernance',
            description: 'Évaluation globale de la gouvernance d\'entreprise',
            currentValue: 0.88,
            targetValue: 0.90,
            minimumValue: 0.70,
            unit: 'score',
            status: 'COMPLIANT',
            trend: 'IMPROVING',
            lastUpdated: '2024-01-15',
            frequency: 'ANNUALLY'
          }
        ]);
      }

      if (alertsResponse.success) {
        setAlerts(alertsResponse.data?.alerts || []);
      } else {
        // Données mock pour les alertes réglementaires
        setAlerts([
          {
            id: 'ALT-001',
            type: 'DEADLINE',
            severity: 'HIGH',
            title: 'Rapport Q2 2024 à soumettre',
            description: 'Le rapport trimestriel Q2 2024 doit être soumis à l\'ACAPS avant le 31 juillet',
            dueDate: '2024-07-31',
            actionRequired: 'Finaliser et soumettre le rapport trimestriel',
            assignedTo: 'Fatima MOUMEN',
            status: 'OPEN',
            createdAt: '2024-07-01T09:00:00Z'
          },
          {
            id: 'ALT-002',
            type: 'THRESHOLD',
            severity: 'MEDIUM',
            title: 'Ratio de réserves sous la cible',
            description: 'Le ratio d\'adéquation des réserves est passé sous la valeur cible de 100%',
            actionRequired: 'Réviser le calcul des provisions techniques',
            assignedTo: 'Mohammed ALAMI',
            status: 'IN_PROGRESS',
            createdAt: '2024-01-10T14:30:00Z'
          },
          {
            id: 'ALT-003',
            type: 'REGULATORY_CHANGE',
            severity: 'MEDIUM',
            title: 'Nouvelle circulaire ACAPS n°15/2024',
            description: 'Nouvelle réglementation sur le calcul des provisions techniques en vigueur au 1er janvier 2025',
            actionRequired: 'Adapter les processus de calcul des provisions',
            assignedTo: 'Hassan IDRISSI',
            status: 'OPEN',
            createdAt: '2024-01-05T11:00:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données de conformité:', error);
      showToast('Erreur lors du chargement des données', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLIANT': case 'APPROVED': case 'SUBMITTED': case 'ACCEPTED': return 'bg-green-100 text-green-800';
      case 'WARNING': case 'IN_REVIEW': case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800';
      case 'NON_COMPLIANT': case 'REJECTED': case 'OVERDUE': return 'bg-red-100 text-red-800';
      case 'DRAFT': case 'OPEN': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-500';
      case 'HIGH': return 'bg-orange-500';
      case 'MEDIUM': return 'bg-yellow-500';
      case 'LOW': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'IMPROVING': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'DETERIORATING': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'STABLE': return <Activity className="h-4 w-4 text-blue-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatValue = (value: number, unit: string) => {
    switch (unit) {
      case 'percentage':
        return `${(value * 100).toFixed(1)}%`;
      case 'ratio':
        return value.toFixed(2);
      case 'score':
        return `${(value * 100).toFixed(0)}/100`;
      case 'currency':
        return new Intl.NumberFormat('fr-MA', {
          style: 'currency',
          currency: 'MAD',
          minimumFractionDigits: 0
        }).format(value);
      default:
        return value.toString();
    }
  };

  const complianceStats = {
    totalReports: reports.length,
    pendingReports: reports.filter(r => ['DRAFT', 'IN_REVIEW'].includes(r.status)).length,
    overdueReports: reports.filter(r => new Date(r.dueDate) < new Date() && !['SUBMITTED', 'ACCEPTED'].includes(r.status)).length,
    compliantMetrics: metrics.filter(m => m.status === 'COMPLIANT').length,
    totalMetrics: metrics.length,
    criticalAlerts: alerts.filter(a => a.severity === 'CRITICAL' && a.status === 'OPEN').length,
    openAlerts: alerts.filter(a => a.status === 'OPEN').length
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Conformité ACAPS</h1>
          <p className="text-gray-600">Gestion de la conformité réglementaire marocaine</p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowNewReportModal(true)}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Rapport
          </button>
          <button
            onClick={loadComplianceData}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'dashboard', name: 'Tableau de Bord', icon: BarChart3 },
            { id: 'reports', name: 'Rapports', icon: FileText },
            { id: 'metrics', name: 'Métriques', icon: Target },
            { id: 'alerts', name: 'Alertes', icon: Bell }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Contenu selon l'onglet actif */}
      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          {/* KPIs de conformité */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rapports en Attente</p>
                  <p className="text-2xl font-bold text-gray-900">{complianceStats.pendingReports}</p>
                  <p className="text-xs text-gray-500">sur {complianceStats.totalReports} total</p>
                </div>
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rapports en Retard</p>
                  <p className="text-2xl font-bold text-gray-900">{complianceStats.overdueReports}</p>
                  <p className="text-xs text-gray-500">Action requise</p>
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conformité</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.round((complianceStats.compliantMetrics / complianceStats.totalMetrics) * 100)}%
                  </p>
                  <p className="text-xs text-gray-500">{complianceStats.compliantMetrics}/{complianceStats.totalMetrics} métriques</p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Alertes Critiques</p>
                  <p className="text-2xl font-bold text-gray-900">{complianceStats.criticalAlerts}</p>
                  <p className="text-xs text-gray-500">{complianceStats.openAlerts} alertes ouvertes</p>
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <Bell className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Métriques de conformité principales */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques Clés de Conformité</h3>
              <div className="space-y-4">
                {metrics.slice(0, 4).map((metric) => (
                  <div key={metric.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getTrendIcon(metric.trend)}
                      <div>
                        <p className="text-sm font-medium text-gray-900">{metric.name}</p>
                        <p className="text-xs text-gray-500">{metric.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-bold text-gray-900">
                        {formatValue(metric.currentValue, metric.unit)}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(metric.status)}`}>
                        {metric.status === 'COMPLIANT' ? 'Conforme' :
                         metric.status === 'WARNING' ? 'Attention' : 'Non conforme'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-xl shadow-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Récentes</h3>
              <div className="space-y-3">
                {alerts.slice(0, 5).map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div className={`w-2 h-2 rounded-full mt-2 ${getSeverityColor(alert.severity)}`}></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                      <p className="text-xs text-gray-500">{alert.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                          {alert.status === 'OPEN' ? 'Ouvert' :
                           alert.status === 'IN_PROGRESS' ? 'En cours' :
                           alert.status === 'RESOLVED' ? 'Résolu' : 'En retard'}
                        </span>
                        {alert.dueDate && (
                          <span className="text-xs text-gray-500">
                            Échéance: {new Date(alert.dueDate).toLocaleDateString('fr-FR')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      )}

      {activeTab === 'reports' && (
        <div className="space-y-6">
          {/* Filtres pour les rapports */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Rechercher un rapport..."
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>

              <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent">
                <option value="">Tous les types</option>
                <option value="QUARTERLY">Trimestriel</option>
                <option value="ANNUAL">Annuel</option>
                <option value="SOLVENCY">Solvabilité</option>
                <option value="FINANCIAL">Financier</option>
              </select>

              <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent">
                <option value="">Tous les statuts</option>
                <option value="DRAFT">Brouillon</option>
                <option value="IN_REVIEW">En révision</option>
                <option value="APPROVED">Approuvé</option>
                <option value="SUBMITTED">Soumis</option>
              </select>

              <button className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <Filter className="h-4 w-4 mr-2" />
                Filtres avancés
              </button>
            </div>
          </div>

          {/* Liste des rapports */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rapport
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Période
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Échéance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Assigné à
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reports.map((report) => (
                    <motion.tr
                      key={report.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{report.reportName}</div>
                            <div className="text-sm text-gray-500">{report.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {report.period.quarter ? `Q${report.period.quarter} ` : ''}{report.period.year}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(report.period.startDate).toLocaleDateString('fr-FR')} - {new Date(report.period.endDate).toLocaleDateString('fr-FR')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                          {report.status === 'DRAFT' ? 'Brouillon' :
                           report.status === 'IN_REVIEW' ? 'En révision' :
                           report.status === 'APPROVED' ? 'Approuvé' :
                           report.status === 'SUBMITTED' ? 'Soumis' :
                           report.status === 'ACCEPTED' ? 'Accepté' : 'Rejeté'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(report.dueDate).toLocaleDateString('fr-FR')}
                        </div>
                        {new Date(report.dueDate) < new Date() && !['SUBMITTED', 'ACCEPTED'].includes(report.status) && (
                          <div className="text-xs text-red-600">En retard</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{report.assignedTo.name}</div>
                        <div className="text-sm text-gray-500">{report.assignedTo.role}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => {
                              setSelectedReport(report);
                              setShowReportDetails(true);
                            }}
                            className="text-axa-blue hover:text-blue-800"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-800">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-800">
                            <Download className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-800">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export { Compliance_ACAPS as default };
