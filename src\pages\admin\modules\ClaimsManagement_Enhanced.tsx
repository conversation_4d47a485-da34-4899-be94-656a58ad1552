import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle, 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  Trash2, 
  Plus,
  Phone,
  Mail,
  MapPin,
  Calendar,
  User,
  Building,
  FileText,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Upload,
  Download,
  MessageSquare,
  Camera,
  Shield,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  Bell,
  Send,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Paperclip,
  Image,
  Video,
  FileImage,
  Star,
  Flag
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface Claim {
  id: string;
  claimNumber: string;
  clientId: string;
  client: {
    id: string;
    name: string;
    email: string;
    phone: string;
    type: 'INDIVIDUAL' | 'BUSINESS';
  };
  contractId?: string;
  contract?: {
    id: string;
    product: string;
    premium: number;
    policyNumber: string;
  };
  type: 'AUTO' | 'HOME' | 'HEALTH' | 'LIFE' | 'BUSINESS' | 'OTHER';
  category: string;
  title: string;
  description: string;
  incidentDate: string;
  reportedDate: string;
  location?: string;
  estimatedAmount?: number;
  approvedAmount?: number;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'INVESTIGATING' | 'APPROVED' | 'REJECTED' | 'PAID' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  documents: ClaimDocument[];
  timeline: ClaimTimelineEvent[];
  notes: ClaimNote[];
  createdAt: string;
  updatedAt: string;
}

interface ClaimDocument {
  id: string;
  name: string;
  type: 'PHOTO' | 'VIDEO' | 'DOCUMENT' | 'REPORT' | 'INVOICE' | 'OTHER';
  url: string;
  size: number;
  uploadedAt: string;
  uploadedBy: string;
}

interface ClaimTimelineEvent {
  id: string;
  type: 'STATUS_CHANGE' | 'DOCUMENT_ADDED' | 'NOTE_ADDED' | 'ASSIGNMENT' | 'PAYMENT' | 'COMMUNICATION';
  title: string;
  description: string;
  userId: string;
  userName: string;
  timestamp: string;
  metadata?: any;
}

interface ClaimNote {
  id: string;
  content: string;
  isInternal: boolean;
  authorId: string;
  authorName: string;
  createdAt: string;
}

export const ClaimsManagementEnhanced: React.FC = () => {
  const [claims, setClaims] = useState<Claim[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showNewClaimModal, setShowNewClaimModal] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  useEffect(() => {
    loadClaims();
  }, [searchTerm, statusFilter, typeFilter, priorityFilter]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadClaims = async () => {
    try {
      setIsLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (statusFilter !== 'all') params.status = statusFilter;
      if (typeFilter !== 'all') params.type = typeFilter;
      if (priorityFilter !== 'all') params.priority = priorityFilter;

      const response = await api.getClaims(params);
      if (response.success) {
        setClaims(response.data?.claims || []);
      } else {
        showToast('Erreur lors du chargement des sinistres', 'error');
      }
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des sinistres', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (claimId: string, newStatus: string) => {
    try {
      const response = await api.updateClaim(claimId, { status: newStatus });
      if (response.success) {
        showToast('Statut mis à jour avec succès', 'success');
        loadClaims();
      } else {
        showToast('Erreur lors de la mise à jour du statut', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      showToast('Erreur lors de la mise à jour du statut', 'error');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUBMITTED': return 'bg-blue-100 text-blue-800';
      case 'UNDER_REVIEW': return 'bg-yellow-100 text-yellow-800';
      case 'INVESTIGATING': return 'bg-orange-100 text-orange-800';
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'PAID': return 'bg-purple-100 text-purple-800';
      case 'CLOSED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'AUTO': return '🚗';
      case 'HOME': return '🏠';
      case 'HEALTH': return '🏥';
      case 'LIFE': return '❤️';
      case 'BUSINESS': return '🏢';
      default: return '📋';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUBMITTED': return <Clock className="h-4 w-4" />;
      case 'UNDER_REVIEW': return <Eye className="h-4 w-4" />;
      case 'INVESTIGATING': return <Search className="h-4 w-4" />;
      case 'APPROVED': return <CheckCircle className="h-4 w-4" />;
      case 'REJECTED': return <XCircle className="h-4 w-4" />;
      case 'PAID': return <DollarSign className="h-4 w-4" />;
      case 'CLOSED': return <Shield className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const claimTypes = [
    { value: 'AUTO', label: 'Automobile', icon: '🚗' },
    { value: 'HOME', label: 'Habitation', icon: '🏠' },
    { value: 'HEALTH', label: 'Santé', icon: '🏥' },
    { value: 'LIFE', label: 'Vie', icon: '❤️' },
    { value: 'BUSINESS', label: 'Entreprise', icon: '🏢' },
    { value: 'OTHER', label: 'Autre', icon: '📋' }
  ];

  const claimStatuses = [
    { value: 'SUBMITTED', label: 'Soumis', color: 'blue' },
    { value: 'UNDER_REVIEW', label: 'En révision', color: 'yellow' },
    { value: 'INVESTIGATING', label: 'Enquête', color: 'orange' },
    { value: 'APPROVED', label: 'Approuvé', color: 'green' },
    { value: 'REJECTED', label: 'Rejeté', color: 'red' },
    { value: 'PAID', label: 'Payé', color: 'purple' },
    { value: 'CLOSED', label: 'Fermé', color: 'gray' }
  ];

  const priorities = [
    { value: 'LOW', label: 'Faible', color: 'green' },
    { value: 'MEDIUM', label: 'Moyenne', color: 'yellow' },
    { value: 'HIGH', label: 'Élevée', color: 'orange' },
    { value: 'URGENT', label: 'Urgente', color: 'red' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <AlertTriangle className="h-8 w-8 mr-3 text-orange-600" />
            Gestion des Sinistres
          </h1>
          <p className="text-gray-600 mt-2">Gérez et suivez tous les sinistres déclarés</p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
          <button
            onClick={() => setShowNewClaimModal(true)}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Sinistre
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Sinistres</p>
              <p className="text-2xl font-bold text-gray-900">{claims.length}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600">+12%</span>
            <span className="text-gray-500 ml-1">ce mois</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">En Cours</p>
              <p className="text-2xl font-bold text-gray-900">
                {claims.filter(c => ['SUBMITTED', 'UNDER_REVIEW', 'INVESTIGATING'].includes(c.status)).length}
              </p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <Activity className="h-4 w-4 text-blue-500 mr-1" />
            <span className="text-blue-600">En traitement</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approuvés</p>
              <p className="text-2xl font-bold text-gray-900">
                {claims.filter(c => c.status === 'APPROVED').length}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600">+8%</span>
            <span className="text-gray-500 ml-1">ce mois</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Montant Total</p>
              <p className="text-2xl font-bold text-gray-900">
                {claims.reduce((sum, c) => sum + (c.approvedAmount || c.estimatedAmount || 0), 0).toLocaleString('fr-FR')} €
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <BarChart3 className="h-4 w-4 text-purple-500 mr-1" />
            <span className="text-purple-600">Indemnisations</span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un sinistre..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            {claimStatuses.map(status => (
              <option key={status.value} value={status.value}>{status.label}</option>
            ))}
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Tous les types</option>
            {claimTypes.map(type => (
              <option key={type.value} value={type.value}>{type.icon} {type.label}</option>
            ))}
          </select>

          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Toutes les priorités</option>
            {priorities.map(priority => (
              <option key={priority.value} value={priority.value}>{priority.label}</option>
            ))}
          </select>

          <button
            onClick={loadClaims}
            className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Filter className="h-4 w-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Claims List */}
      <div className="bg-white rounded-xl shadow-lg">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sinistre
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Montant
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priorité
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {claims.map((claim) => (
                  <tr key={claim.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {claim.claimNumber}
                        </div>
                        <div className="text-sm text-gray-500">{claim.title}</div>
                        <div className="text-xs text-gray-400">
                          {new Date(claim.reportedDate).toLocaleDateString('fr-FR')}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-axa-blue flex items-center justify-center">
                            <span className="text-white font-medium text-sm">
                              {claim.client.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{claim.client.name}</div>
                          <div className="text-sm text-gray-500">{claim.client.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{getTypeIcon(claim.type)}</span>
                        <span className="text-sm text-gray-900">{claim.category}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {(claim.approvedAmount || claim.estimatedAmount || 0).toLocaleString('fr-FR')} €
                      </div>
                      {claim.approvedAmount && claim.estimatedAmount && claim.approvedAmount !== claim.estimatedAmount && (
                        <div className="text-xs text-gray-500">
                          Est. {claim.estimatedAmount.toLocaleString('fr-FR')} €
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(claim.status)}`}>
                        {getStatusIcon(claim.status)}
                        <span className="ml-1">{claim.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(claim.priority)}`}>
                        {claim.priority === 'URGENT' && <Flag className="h-3 w-3 mr-1" />}
                        {claim.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedClaim(claim);
                            setShowDetails(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-purple-600 hover:text-purple-900">
                          <MessageSquare className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {claims.length === 0 && (
              <div className="text-center py-12">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Aucun sinistre trouvé</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};
