import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '@/middleware/auth';
import { sendEmail } from '@/utils/email';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createQuoteSchema = Joi.object({
  clientId: Joi.string().required(),
  product: Joi.string().valid('Assurance Auto', 'Assurance Habitation', 'Assurance Santé', 'Prévoyance', 'Épargne Retraite').required(),
  amount: Joi.number().positive().required(),
  validUntil: Joi.date().greater('now').required(),
  details: Joi.object(),
});

const updateQuoteSchema = Joi.object({
  product: Joi.string().valid('Assurance Auto', 'Assurance Habitation', 'Assurance Santé', 'Prévoyance', 'Épargne Retraite'),
  amount: Joi.number().positive(),
  status: Joi.string().valid('DRAFT', 'SENT', 'ACCEPTED', 'REJECTED', 'EXPIRED'),
  validUntil: Joi.date(),
  details: Joi.object(),
});

/**
 * GET /api/quotes
 * Récupérer tous les devis
 */
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const clientId = req.query.clientId as string;
  const product = req.query.product as string;

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  if (status && status !== 'all') where.status = status;
  if (clientId) where.clientId = clientId;
  if (product) where.product = product;

  // Si l'utilisateur n'est pas admin, ne voir que ses devis
  if (req.user?.role === 'USER') {
    where.createdBy = req.user.id;
  }

  const [quotes, total] = await Promise.all([
    prisma.quote.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        client: {
          select: { id: true, name: true, email: true, phone: true },
        },
        createdUser: {
          select: { id: true, name: true, email: true },
        },
      },
    }),
    prisma.quote.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      quotes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/quotes/:id
 * Récupérer un devis spécifique
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const quote = await prisma.quote.findUnique({
    where: { id: req.params.id },
    include: {
      client: true,
      createdUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  if (!quote) {
    throw createError('Devis non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && quote.createdBy !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  res.json({
    success: true,
    data: { quote },
  });
}));

/**
 * POST /api/quotes
 * Créer un nouveau devis
 */
router.post('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createQuoteSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le client existe
  const client = await prisma.client.findUnique({
    where: { id: value.clientId },
  });

  if (!client) {
    throw createError('Client non trouvé', 404);
  }

  // Créer le devis
  const quote = await prisma.quote.create({
    data: {
      ...value,
      createdBy: req.user!.id,
    },
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
      createdUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Quote created: ${quote.id} for client ${client.name} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Devis créé avec succès',
    data: { quote },
  });
}));

/**
 * PUT /api/quotes/:id
 * Mettre à jour un devis
 */
router.put('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updateQuoteSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le devis existe
  const existingQuote = await prisma.quote.findUnique({
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!existingQuote) {
    throw createError('Devis non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && existingQuote.createdBy !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  // Mettre à jour le devis
  const quote = await prisma.quote.update({
    where: { id: req.params.id },
    data: value,
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
      createdUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Quote updated: ${quote.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Devis mis à jour avec succès',
    data: { quote },
  });
}));

/**
 * POST /api/quotes/:id/send
 * Envoyer un devis par email
 */
router.post('/:id/send', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const quote = await prisma.quote.findUnique({
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!quote) {
    throw createError('Devis non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && quote.createdBy !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  try {
    // Envoyer l'email
    await sendEmail({
      to: quote.client.email,
      subject: `Votre devis ${quote.product} - MOUMEN TECHNIQUE ET PREVOYANCE`,
      template: 'quote-sent',
      data: {
        clientName: quote.client.name,
        product: quote.product,
        amount: quote.amount,
        validUntil: quote.validUntil.toLocaleDateString('fr-FR'),
      },
    });

    // Mettre à jour le statut du devis
    await prisma.quote.update({
      where: { id: req.params.id },
      data: { status: 'SENT' },
    });

    logger.info(`Quote sent: ${quote.id} to ${quote.client.email} by ${req.user?.email}`);

    res.json({
      success: true,
      message: 'Devis envoyé avec succès',
    });
  } catch (error) {
    logger.error('Failed to send quote email:', error);
    throw createError('Erreur lors de l\'envoi du devis', 500);
  }
}));

/**
 * POST /api/quotes/:id/accept
 * Accepter un devis (public avec token)
 */
router.post('/:id/accept', asyncHandler(async (req, res) => {
  const quote = await prisma.quote.findUnique({
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!quote) {
    throw createError('Devis non trouvé', 404);
  }

  if (quote.status !== 'SENT') {
    throw createError('Ce devis ne peut pas être accepté', 400);
  }

  if (quote.validUntil < new Date()) {
    throw createError('Ce devis a expiré', 400);
  }

  // Mettre à jour le statut
  const updatedQuote = await prisma.quote.update({
    where: { id: req.params.id },
    data: { status: 'ACCEPTED' },
  });

  // Créer un contrat automatiquement
  await prisma.contract.create({
    data: {
      clientId: quote.clientId,
      product: quote.product,
      premium: quote.amount,
      status: 'ACTIVE',
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 an
      details: quote.details,
    },
  });

  logger.info(`Quote accepted: ${quote.id} by client ${quote.client.email}`);

  res.json({
    success: true,
    message: 'Devis accepté avec succès',
    data: { quote: updatedQuote },
  });
}));

/**
 * POST /api/quotes/:id/reject
 * Rejeter un devis (public avec token)
 */
router.post('/:id/reject', asyncHandler(async (req, res) => {
  const { reason } = req.body;

  const quote = await prisma.quote.findUnique({
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!quote) {
    throw createError('Devis non trouvé', 404);
  }

  if (quote.status !== 'SENT') {
    throw createError('Ce devis ne peut pas être rejeté', 400);
  }

  // Mettre à jour le statut
  const updatedQuote = await prisma.quote.update({
    where: { id: req.params.id },
    data: { 
      status: 'REJECTED',
      details: {
        ...(quote.details as any || {}),
        rejectionReason: reason,
        rejectedAt: new Date(),
      },
    },
  });

  logger.info(`Quote rejected: ${quote.id} by client ${quote.client.email}, reason: ${reason}`);

  res.json({
    success: true,
    message: 'Devis rejeté',
    data: { quote: updatedQuote },
  });
}));

/**
 * DELETE /api/quotes/:id
 * Supprimer un devis
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const quote = await prisma.quote.findUnique({
    where: { id: req.params.id },
  });

  if (!quote) {
    throw createError('Devis non trouvé', 404);
  }

  await prisma.quote.delete({
    where: { id: req.params.id },
  });

  logger.info(`Quote deleted: ${quote.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Devis supprimé avec succès',
  });
}));

export default router;
