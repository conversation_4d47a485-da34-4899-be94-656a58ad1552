import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Mail, MapPin, Award, Clock } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { TopBanner } from './TopBanner';
import { SkipToContent, useKeyboardNavigation } from '../common/SkipToContent';

export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showBanner, setShowBanner] = useState(true);
  const location = useLocation();

  // Hook pour la navigation au clavier
  useKeyboardNavigation();

  const navigation = [
    { name: 'Accueil', href: '/' },
    { name: 'Nos Produits', href: '/produits' },
    { name: 'À Propos', href: '/a-propos' },
    { name: 'Contact', href: '/contact' },
    { name: 'Actualités', href: '/actualites' },
  ];

  const bannerContent = {
    text: "🎉 Offre spéciale : -20% sur votre première assurance auto ! Valable jusqu'au 31 janvier 2024",
    type: 'promotion' as const,
    backgroundColor: '',
    textColor: '',
    showContact: true
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      {/* Skip to content for accessibility */}
      <SkipToContent />

      {/* Top Banner */}
      <TopBanner
        isVisible={showBanner}
        onClose={() => setShowBanner(false)}
        content={bannerContent}
      />

      {/* Contact Bar */}
      <div className="bg-axa-blue text-white py-2 hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>+212 5XX-XXXXXX</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Casablanca, Maroc</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
// ...existing code...
// ...existing code...
              <Link to="/devis" className="bg-axa-red px-4 py-1 rounded hover:bg-red-600 transition-colors">
                Devis Gratuit
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-4 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-axa-blue to-blue-700 rounded-2xl transform rotate-3 group-hover:rotate-6 transition-transform duration-300 opacity-20"></div>
                <div className="relative bg-white p-3 rounded-2xl shadow-lg border-2 border-axa-blue/20 group-hover:border-axa-blue/40 transition-colors duration-300">
                  <img
                    src="/logo-mtp-official.png"
                    alt="MOUMEN TECHNIQUE ET PREVOYANCE"
                    className="h-16 w-auto"
                    style={{ width: '201px', height: '60px', objectFit: 'contain' }}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/logo-mtp-1.png";
                    }}
                  />
                </div>
              </div>
              <div className="hidden sm:block">
                <div className="text-axa-blue font-bold text-xl tracking-tight">MOUMEN</div>
                <div className="text-axa-blue text-sm font-medium">TECHNIQUE & PRÉVOYANCE</div>
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <span>Agent Général</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-green-500" />
                    <span className="text-green-600 font-medium">15+ ans</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Award className="h-3 w-3 text-yellow-500" />
                    <span className="text-yellow-600 font-medium">Certifié</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`font-medium transition-colors relative group ${
                    isActive(item.href)
                      ? 'text-axa-blue'
                      : 'text-gray-700 hover:text-axa-blue'
                  }`}
                >
                  {item.name}
                  {isActive(item.href) && (
                    <motion.div
                      className="absolute -bottom-1 left-0 right-0 h-0.5 bg-axa-red"
                      layoutId="activeTab"
                    />
                  )}
                  <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-axa-red scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </Link>
              ))}
            </nav>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-700 hover:text-axa-blue"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-white border-t"
            >
              <div className="px-4 py-2 space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setIsMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md font-medium ${
                      isActive(item.href)
                        ? 'text-axa-blue bg-blue-50'
                        : 'text-gray-700 hover:text-axa-blue hover:bg-gray-50'
                    }`}
                  >
                    {item.name}
                  </Link>
                ))}
                <div className="pt-4 pb-2 border-t">
                  <Link
// ...existing code...
                    onClick={() => setIsMenuOpen(false)}
                    className="block px-3 py-2 text-gray-700 hover:text-axa-blue"
                  >
                    Espace Client
                  </Link>
                  <Link
                    to="/devis"
                    onClick={() => setIsMenuOpen(false)}
                    className="block mx-3 mt-2 px-4 py-2 bg-axa-red text-white rounded-md text-center hover:bg-red-600"
                  >
                    Devis Gratuit
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>
    </>
  );
};