import nodemailer from 'nodemailer';
import { logger } from './logger';

interface EmailOptions {
  to: string;
  subject: string;
  template: string;
  data: any;
  attachments?: Array<{
    filename: string;
    path?: string;
    content?: Buffer;
  }>;
}

// Configuration du transporteur email
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true pour 465, false pour autres ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// Templates d'emails
const emailTemplates = {
  'new-lead': (data: any) => ({
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1e40af;">Nouveau Lead Reçu</h2>
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px;">
          <h3>Informations du prospect :</h3>
          <p><strong>Nom :</strong> ${data.lead.name}</p>
          <p><strong>Email :</strong> ${data.lead.email}</p>
          <p><strong>Téléphone :</strong> ${data.lead.phone}</p>
          <p><strong>Produit :</strong> ${data.lead.product}</p>
          <p><strong>Source :</strong> ${data.lead.source}</p>
          ${data.lead.city ? `<p><strong>Ville :</strong> ${data.lead.city}</p>` : ''}
          ${data.lead.message ? `<p><strong>Message :</strong><br>${data.lead.message}</p>` : ''}
        </div>
        <p style="margin-top: 20px;">
          <a href="${data.adminUrl}" style="background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Voir dans l'admin
          </a>
        </p>
      </div>
    `,
    text: `
      Nouveau Lead Reçu
      
      Nom: ${data.lead.name}
      Email: ${data.lead.email}
      Téléphone: ${data.lead.phone}
      Produit: ${data.lead.product}
      Source: ${data.lead.source}
      ${data.lead.city ? `Ville: ${data.lead.city}` : ''}
      ${data.lead.message ? `Message: ${data.lead.message}` : ''}
      
      Voir dans l'admin: ${data.adminUrl}
    `,
  }),

  'lead-confirmation': (data: any) => ({
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${process.env.FRONTEND_URL}/logo-mtp-official.png" alt="MOUMEN TECHNIQUE ET PREVOYANCE" style="height: 60px;">
        </div>
        
        <h2 style="color: #1e40af;">Demande de devis reçue</h2>
        
        <p>Bonjour ${data.name},</p>
        
        <p>Nous avons bien reçu votre demande de devis pour <strong>${data.product}</strong>.</p>
        
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Que se passe-t-il maintenant ?</strong></p>
          <ul>
            <li>Un de nos conseillers va étudier votre demande</li>
            <li>Nous vous contacterons sous 24h pour discuter de vos besoins</li>
            <li>Nous vous proposerons une solution personnalisée</li>
          </ul>
        </div>
        
        <p>En attendant, n'hésitez pas à nous contacter :</p>
        <ul>
          <li><strong>Téléphone :</strong> +212 5XX-XXXXXX</li>
          <li><strong>Email :</strong> <EMAIL></li>
        </ul>
        
        <p>Cordialement,<br>
        L'équipe MOUMEN TECHNIQUE ET PREVOYANCE</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 12px; color: #6b7280;">
          MOUMEN TECHNIQUE ET PREVOYANCE - Agent Général AXA<br>
          123 Boulevard Mohammed V, Casablanca<br>
          Tél: +212 5XX-XXXXXX | Email: <EMAIL>
        </p>
      </div>
    `,
    text: `
      Demande de devis reçue
      
      Bonjour ${data.name},
      
      Nous avons bien reçu votre demande de devis pour ${data.product}.
      
      Que se passe-t-il maintenant ?
      - Un de nos conseillers va étudier votre demande
      - Nous vous contacterons sous 24h pour discuter de vos besoins
      - Nous vous proposerons une solution personnalisée
      
      En attendant, n'hésitez pas à nous contacter :
      Téléphone : +212 5XX-XXXXXX
      Email : <EMAIL>
      
      Cordialement,
      L'équipe MOUMEN TECHNIQUE ET PREVOYANCE
      
      ---
      MOUMEN TECHNIQUE ET PREVOYANCE - Agent Général AXA
      123 Boulevard Mohammed V, Casablanca
      Tél: +212 5XX-XXXXXX | Email: <EMAIL>
    `,
  }),

  'quote-sent': (data: any) => ({
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${process.env.FRONTEND_URL}/logo-mtp-official.png" alt="MOUMEN TECHNIQUE ET PREVOYANCE" style="height: 60px;">
        </div>
        
        <h2 style="color: #1e40af;">Votre devis est prêt !</h2>
        
        <p>Bonjour ${data.clientName},</p>
        
        <p>Nous avons le plaisir de vous transmettre votre devis personnalisé pour <strong>${data.product}</strong>.</p>
        
        <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Détails du devis :</h3>
          <p><strong>Produit :</strong> ${data.product}</p>
          <p><strong>Montant :</strong> ${data.amount} MAD</p>
          <p><strong>Validité :</strong> ${data.validUntil}</p>
        </div>
        
        <p>Pour accepter ce devis ou poser des questions, contactez-nous :</p>
        <ul>
          <li><strong>Téléphone :</strong> +212 5XX-XXXXXX</li>
          <li><strong>Email :</strong> <EMAIL></li>
        </ul>
        
        <p>Cordialement,<br>
        L'équipe MOUMEN TECHNIQUE ET PREVOYANCE</p>
      </div>
    `,
    text: `
      Votre devis est prêt !
      
      Bonjour ${data.clientName},
      
      Nous avons le plaisir de vous transmettre votre devis personnalisé pour ${data.product}.
      
      Détails du devis :
      Produit : ${data.product}
      Montant : ${data.amount} MAD
      Validité : ${data.validUntil}
      
      Pour accepter ce devis ou poser des questions, contactez-nous :
      Téléphone : +212 5XX-XXXXXX
      Email : <EMAIL>
      
      Cordialement,
      L'équipe MOUMEN TECHNIQUE ET PREVOYANCE
    `,
  }),
};

/**
 * Envoyer un email
 */
export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    const transporter = createTransporter();
    
    // Vérifier la configuration
    await transporter.verify();
    
    // Générer le contenu de l'email
    const template = emailTemplates[options.template as keyof typeof emailTemplates];
    if (!template) {
      throw new Error(`Template email '${options.template}' non trouvé`);
    }
    
    const { html, text } = template(options.data);
    
    // Envoyer l'email
    const info = await transporter.sendMail({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: options.to,
      subject: options.subject,
      html,
      text,
      attachments: options.attachments,
    });
    
    logger.info(`Email sent successfully to ${options.to}: ${info.messageId}`);
  } catch (error) {
    logger.error('Failed to send email:', error);
    throw error;
  }
};

/**
 * Envoyer un email de test
 */
export const sendTestEmail = async (to: string): Promise<void> => {
  await sendEmail({
    to,
    subject: 'Test Email - MOUMEN TECHNIQUE ET PREVOYANCE',
    template: 'lead-confirmation',
    data: {
      name: 'Test User',
      product: 'Assurance Auto',
    },
  });
};
