import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Target,
  TrendingUp,
  Phone,
  Mail,
  Calendar,
  MapPin,
  User,
  Building,
  Car,
  Home,
  Heart,
  Shield,
  DollarSign,
  Clock,
  Star,
  Filter,
  Search,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  Briefcase,
  Calculator,
  FileText,
  Award,
  Zap,
  Activity,
  ArrowUp,
  ArrowDown,
  TrendingDown
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface InsuranceLead {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: string;
  address: {
    street: string;
    city: string;
    postalCode: string;
    region: string;
  };
  source: 'WEBSITE' | 'PHONE' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'ADVERTISING' | 'PARTNER' | 'WALK_IN';
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL_SENT' | 'NEGOTIATING' | 'WON' | 'LOST' | 'FOLLOW_UP';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  score: number; // Score de qualification 0-100
  interestedProducts: Array<{
    type: 'AUTO' | 'HOME' | 'HEALTH' | 'LIFE' | 'BUSINESS' | 'TRAVEL';
    priority: number;
    budget?: number;
    timeline?: string;
    details?: string;
  }>;
  currentInsurance?: {
    company: string;
    expiryDate: string;
    premium: number;
    satisfied: boolean;
    reasonForChange?: string;
  };
  demographics: {
    age: number;
    profession: string;
    income?: number;
    familyStatus: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED';
    children: number;
    homeOwner: boolean;
    vehicleOwner: boolean;
  };
  interactions: Array<{
    id: string;
    date: string;
    type: 'CALL' | 'EMAIL' | 'MEETING' | 'SMS' | 'QUOTE_SENT' | 'DOCUMENT_SENT';
    description: string;
    outcome: string;
    nextAction?: string;
    nextActionDate?: string;
    agent: string;
  }>;
  assignedAgent?: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  tags: string[];
  notes: string;
  estimatedValue: number; // Valeur potentielle annuelle
  conversionProbability: number; // Probabilité de conversion 0-100
  lastContactDate?: string;
  nextFollowUpDate?: string;
  createdAt: string;
  updatedAt: string;
}

export const LeadsManagement_Insurance: React.FC = () => {
  const [leads, setLeads] = useState<InsuranceLead[]>([]);
  const [selectedLead, setSelectedLead] = useState<InsuranceLead | null>(null);
  const [showLeadDetails, setShowLeadDetails] = useState(false);
  const [showNewLeadModal, setShowNewLeadModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [sourceFilter, setSourceFilter] = useState('ALL');
  const [productFilter, setProductFilter] = useState('ALL');
  const [priorityFilter, setPriorityFilter] = useState('ALL');
  const [toast, setToast] = useState<any>(null);

  useEffect(() => {
    loadLeads();
  }, []);

  const loadLeads = async () => {
    try {
      setIsLoading(true);
      const response = await api.getLeads();
      if (response.success) {
        setLeads(response.data?.leads || []);
      } else {
        // Données mock spécifiques à l'assurance
        setLeads([
          {
            id: 'LEAD-2024-001',
            firstName: 'Youssef',
            lastName: 'ALAMI',
            email: '<EMAIL>',
            phone: '+212 6XX-XXXXXX',
            dateOfBirth: '1985-03-15',
            address: {
              street: '123 Avenue Hassan II',
              city: 'Casablanca',
              postalCode: '20000',
              region: 'Grand Casablanca'
            },
            source: 'WEBSITE',
            status: 'QUALIFIED',
            priority: 'HIGH',
            score: 85,
            interestedProducts: [
              {
                type: 'AUTO',
                priority: 1,
                budget: 3000,
                timeline: '1 mois',
                details: 'Véhicule neuf, recherche tous risques'
              },
              {
                type: 'HOME',
                priority: 2,
                budget: 1500,
                timeline: '3 mois',
                details: 'Appartement 120m², Casablanca'
              }
            ],
            currentInsurance: {
              company: 'Concurrent A',
              expiryDate: '2024-03-15',
              premium: 4500,
              satisfied: false,
              reasonForChange: 'Service client insuffisant'
            },
            demographics: {
              age: 39,
              profession: 'Ingénieur',
              income: 15000,
              familyStatus: 'MARRIED',
              children: 2,
              homeOwner: true,
              vehicleOwner: true
            },
            interactions: [
              {
                id: 'INT-001',
                date: '2024-01-10T10:00:00Z',
                type: 'CALL',
                description: 'Premier contact téléphonique',
                outcome: 'Intéressé par devis auto et habitation',
                nextAction: 'Envoyer devis personnalisé',
                nextActionDate: '2024-01-12',
                agent: 'Fatima MOUMEN'
              },
              {
                id: 'INT-002',
                date: '2024-01-12T14:30:00Z',
                type: 'QUOTE_SENT',
                description: 'Envoi devis auto et habitation',
                outcome: 'Devis envoyé par email',
                nextAction: 'Relance téléphonique',
                nextActionDate: '2024-01-15',
                agent: 'Fatima MOUMEN'
              }
            ],
            assignedAgent: {
              id: 'AGENT-001',
              name: 'Fatima MOUMEN',
              email: '<EMAIL>',
              phone: '+212 5XX-XXXXXX'
            },
            tags: ['Prospect chaud', 'Multi-produits', 'Famille'],
            notes: 'Client très intéressé, budget confirmé. Décision attendue avant fin janvier.',
            estimatedValue: 4500,
            conversionProbability: 85,
            lastContactDate: '2024-01-12T14:30:00Z',
            nextFollowUpDate: '2024-01-15T10:00:00Z',
            createdAt: '2024-01-10T09:00:00Z',
            updatedAt: '2024-01-12T14:30:00Z'
          },
          {
            id: 'LEAD-2024-002',
            firstName: 'Aicha',
            lastName: 'BENNANI',
            email: '<EMAIL>',
            phone: '+212 6XX-XXXXXX',
            dateOfBirth: '1990-07-22',
            address: {
              street: '45 Rue Allal Ben Abdellah',
              city: 'Rabat',
              postalCode: '10000',
              region: 'Rabat-Salé-Kénitra'
            },
            source: 'REFERRAL',
            status: 'PROPOSAL_SENT',
            priority: 'MEDIUM',
            score: 70,
            interestedProducts: [
              {
                type: 'HEALTH',
                priority: 1,
                budget: 2000,
                timeline: '2 semaines',
                details: 'Couverture familiale complète'
              }
            ],
            demographics: {
              age: 34,
              profession: 'Médecin',
              income: 25000,
              familyStatus: 'MARRIED',
              children: 1,
              homeOwner: false,
              vehicleOwner: true
            },
            interactions: [
              {
                id: 'INT-003',
                date: '2024-01-08T15:00:00Z',
                type: 'MEETING',
                description: 'Rendez-vous en agence',
                outcome: 'Analyse des besoins santé famille',
                nextAction: 'Préparer proposition santé',
                nextActionDate: '2024-01-10',
                agent: 'Hassan IDRISSI'
              }
            ],
            assignedAgent: {
              id: 'AGENT-002',
              name: 'Hassan IDRISSI',
              email: '<EMAIL>',
              phone: '+212 5XX-XXXXXX'
            },
            tags: ['Santé', 'Médecin', 'Recommandation'],
            notes: 'Recommandée par client existant. Très intéressée par couverture santé premium.',
            estimatedValue: 2000,
            conversionProbability: 70,
            lastContactDate: '2024-01-10T16:00:00Z',
            nextFollowUpDate: '2024-01-16T14:00:00Z',
            createdAt: '2024-01-08T14:00:00Z',
            updatedAt: '2024-01-10T16:00:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des leads:', error);
      showToast('Erreur lors du chargement des leads', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'bg-blue-100 text-blue-800';
      case 'CONTACTED': return 'bg-yellow-100 text-yellow-800';
      case 'QUALIFIED': return 'bg-purple-100 text-purple-800';
      case 'PROPOSAL_SENT': return 'bg-indigo-100 text-indigo-800';
      case 'NEGOTIATING': return 'bg-orange-100 text-orange-800';
      case 'WON': return 'bg-green-100 text-green-800';
      case 'LOST': return 'bg-red-100 text-red-800';
      case 'FOLLOW_UP': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-500';
      case 'HIGH': return 'bg-orange-500';
      case 'MEDIUM': return 'bg-yellow-500';
      case 'LOW': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    if (score >= 40) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getProductIcon = (productType: string) => {
    switch (productType) {
      case 'AUTO': return <Car className="h-4 w-4" />;
      case 'HOME': return <Home className="h-4 w-4" />;
      case 'HEALTH': return <Heart className="h-4 w-4" />;
      case 'LIFE': return <Shield className="h-4 w-4" />;
      case 'BUSINESS': return <Building className="h-4 w-4" />;
      case 'TRAVEL': return <MapPin className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'NEW': 'Nouveau',
      'CONTACTED': 'Contacté',
      'QUALIFIED': 'Qualifié',
      'PROPOSAL_SENT': 'Devis envoyé',
      'NEGOTIATING': 'Négociation',
      'WON': 'Gagné',
      'LOST': 'Perdu',
      'FOLLOW_UP': 'Suivi'
    };
    return statusMap[status] || status;
  };

  const getSourceText = (source: string) => {
    const sourceMap = {
      'WEBSITE': 'Site web',
      'PHONE': 'Téléphone',
      'REFERRAL': 'Recommandation',
      'SOCIAL_MEDIA': 'Réseaux sociaux',
      'ADVERTISING': 'Publicité',
      'PARTNER': 'Partenaire',
      'WALK_IN': 'Visite directe'
    };
    return sourceMap[source] || source;
  };

  const filteredLeads = Array.isArray(leads) ? leads.filter(lead => {
    const matchesSearch = lead.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'ALL' || lead.status === statusFilter;
    const matchesSource = sourceFilter === 'ALL' || lead.source === sourceFilter;
    const matchesPriority = priorityFilter === 'ALL' || lead.priority === priorityFilter;
    const matchesProduct = productFilter === 'ALL' || 
                          lead.interestedProducts.some(p => p.type === productFilter);
    
    return matchesSearch && matchesStatus && matchesSource && matchesPriority && matchesProduct;
  }) : [];

  const leadsStats = {
    total: leads.length,
    new: leads.filter(l => l.status === 'NEW').length,
    qualified: leads.filter(l => l.status === 'QUALIFIED').length,
    proposals: leads.filter(l => l.status === 'PROPOSAL_SENT').length,
    won: leads.filter(l => l.status === 'WON').length,
    avgScore: leads.length > 0 ? Math.round(leads.reduce((sum, l) => sum + l.score, 0) / leads.length) : 0,
    totalValue: leads.reduce((sum, l) => sum + l.estimatedValue, 0),
    conversionRate: leads.length > 0 ? Math.round((leads.filter(l => l.status === 'WON').length / leads.length) * 100) : 0
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Prospects</h1>
          <p className="text-gray-600">Pipeline commercial et qualification des leads</p>
        </div>
        
        <button
          onClick={() => setShowNewLeadModal(true)}
          className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau Prospect
        </button>
      </div>

      {/* Statistiques du pipeline */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Target className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Zap className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Nouveaux</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.new}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Qualifiés</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.qualified}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-indigo-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Devis</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.proposals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Gagnés</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.won}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Star className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Score moy.</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.avgScore}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Valeur</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(leadsStats.totalValue)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Conversion</p>
              <p className="text-lg font-bold text-gray-900">{leadsStats.conversionRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les statuts</option>
            <option value="NEW">Nouveau</option>
            <option value="CONTACTED">Contacté</option>
            <option value="QUALIFIED">Qualifié</option>
            <option value="PROPOSAL_SENT">Devis envoyé</option>
            <option value="NEGOTIATING">Négociation</option>
            <option value="WON">Gagné</option>
            <option value="LOST">Perdu</option>
            <option value="FOLLOW_UP">Suivi</option>
          </select>

          <select
            value={sourceFilter}
            onChange={(e) => setSourceFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Toutes sources</option>
            <option value="WEBSITE">Site web</option>
            <option value="PHONE">Téléphone</option>
            <option value="REFERRAL">Recommandation</option>
            <option value="SOCIAL_MEDIA">Réseaux sociaux</option>
            <option value="ADVERTISING">Publicité</option>
            <option value="PARTNER">Partenaire</option>
            <option value="WALK_IN">Visite directe</option>
          </select>

          <select
            value={productFilter}
            onChange={(e) => setProductFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous produits</option>
            <option value="AUTO">Automobile</option>
            <option value="HOME">Habitation</option>
            <option value="HEALTH">Santé</option>
            <option value="LIFE">Vie</option>
            <option value="BUSINESS">Entreprise</option>
            <option value="TRAVEL">Voyage</option>
          </select>

          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Toutes priorités</option>
            <option value="URGENT">Urgent</option>
            <option value="HIGH">Élevée</option>
            <option value="MEDIUM">Moyenne</option>
            <option value="LOW">Faible</option>
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
            <Filter className="h-4 w-4 mr-2" />
            Filtres avancés
          </button>
        </div>
      </div>

      {/* Liste des prospects avec tableau avancé */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Prospect
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Produits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLeads.map((lead) => (
                <motion.tr
                  key={lead.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedLead(lead);
                    setShowLeadDetails(true);
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-3 ${getPriorityColor(lead.priority)}`}></div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {lead.firstName} {lead.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{lead.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{lead.email}</div>
                    <div className="text-sm text-gray-500">{lead.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-1">
                      {lead.interestedProducts.slice(0, 3).map((product, index) => (
                        <div key={index} className="flex items-center space-x-1">
                          {getProductIcon(product.type)}
                          <span className="text-xs text-gray-600">{product.type}</span>
                        </div>
                      ))}
                      {lead.interestedProducts.length > 3 && (
                        <span className="text-xs text-gray-500">+{lead.interestedProducts.length - 3}</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(lead.score)}`}>
                      <Star className="h-3 w-3 mr-1" />
                      {lead.score}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(lead.status)}`}>
                      {getStatusText(lead.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{formatCurrency(lead.estimatedValue)}</div>
                      <div className="text-xs text-gray-500">{lead.conversionProbability}% prob.</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {lead.assignedAgent ? (
                      <div className="text-sm text-gray-900">
                        <div className="font-medium">{lead.assignedAgent.name}</div>
                        <div className="text-xs text-gray-500">{lead.assignedAgent.email}</div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Non assigné</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedLead(lead);
                          setShowLeadDetails(true);
                        }}
                        className="text-axa-blue hover:text-blue-800"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Logique d'édition
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Menu d'actions
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredLeads.length === 0 && (
          <div className="text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Aucun prospect trouvé</p>
          </div>
        )}
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export { LeadsManagement_Insurance as default };
