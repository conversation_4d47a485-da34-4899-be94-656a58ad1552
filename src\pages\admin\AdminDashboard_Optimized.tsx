import React, { useState, Suspense, lazy } from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3,
  Users,
  FileText,
  Settings,
  Bell,
  Search,
  Menu,
  X,
  UserPlus,
  LogOut,
  Target,
  Calculator,
  AlertTriangle,
  TrendingUp,
  Shield,
  CheckSquare,
  PieChart,
  Building,
  BookOpen,
  Database,
  Briefcase,
  Layout,
  ChevronDown,
  ChevronRight,
  Home,
  User,
  Moon,
  Sun,
  Globe
} from 'lucide-react';
import { ErrorBoundary, AdminModuleWrapper } from '../../components/admin/ErrorBoundary';
import { useAdmin } from '../../contexts/AdminContext';
import { NotificationCenter } from '../../components/admin/NotificationCenter';

// Lazy loading des modules pour optimiser les performances
const DashboardOverview_Insurance = lazy(() => import('./modules/DashboardOverview_Insurance').then(m => ({ default: m.DashboardOverview_Insurance })));
const LeadsManagement_Insurance = lazy(() => import('./modules/LeadsManagement_Insurance').then(m => ({ default: m.LeadsManagement_Insurance })));
const ClientsManagement_Insurance = lazy(() => import('./modules/ClientsManagement_Insurance').then(m => ({ default: m.ClientsManagement_Insurance })));
const QuotesContracts_Professional = lazy(() => import('./modules/QuotesContracts_Professional').then(m => ({ default: m.QuotesContracts_Professional })));
const ClaimsManagement_Professional = lazy(() => import('./modules/ClaimsManagement_Professional').then(m => ({ default: m.ClaimsManagement_Professional })));
const Analytics_Insurance = lazy(() => import('./modules/Analytics_Insurance').then(m => ({ default: m.Analytics_Insurance })));
const Compliance_ACAPS = lazy(() => import('./modules/Compliance_ACAPS').then(m => ({ default: m.Compliance_ACAPS })));

// Modules secondaires (lazy loaded)
const TasksReminders = lazy(() => import('./modules/TasksReminders').then(m => ({ default: m.TasksReminders })));
const DocumentsManagement = lazy(() => import('./modules/DocumentsManagement').then(m => ({ default: m.DocumentsManagement })));
const UserManagement = lazy(() => import('./modules/UserManagement').then(m => ({ default: m.UserManagement })));
const SettingsModule = lazy(() => import('./modules/Settings').then(m => ({ default: m.SettingsModule })));

// Composant de chargement
const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
  </div>
);

// Interface pour les éléments de navigation
interface NavigationItem {
  id: string;
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  color: string;
  badge?: number;
  description?: string;
}

interface NavigationSection {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  items: NavigationItem[];
  collapsed?: boolean;
}

export const AdminDashboard_Optimized: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<string[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const location = useLocation();
  const { state, logout, setTheme, setLanguage } = useAdmin();

  // Navigation organisée par sections logiques
  const navigationSections: NavigationSection[] = [
    {
      id: 'core',
      name: 'Modules Principaux',
      icon: Home,
      items: [
        { 
          id: 'dashboard', 
          name: 'Dashboard Assurance', 
          href: '/admin/dashboard-insurance', 
          icon: BarChart3, 
          color: 'text-blue-600',
          description: 'Vue d\'ensemble et KPIs'
        },
        { 
          id: 'leads', 
          name: 'Prospects', 
          href: '/admin/leads-insurance', 
          icon: Target, 
          color: 'text-green-600',
          description: 'Gestion des prospects'
        },
        { 
          id: 'clients', 
          name: 'Clients', 
          href: '/admin/clients-insurance', 
          icon: Users, 
          color: 'text-purple-600',
          description: 'Portefeuille clients'
        },
        { 
          id: 'quotes', 
          name: 'Devis', 
          href: '/admin/quotes-pro', 
          icon: Calculator, 
          color: 'text-orange-600',
          description: 'Devis et tarification'
        },
        { 
          id: 'claims', 
          name: 'Sinistres', 
          href: '/admin/claims-pro', 
          icon: AlertTriangle, 
          color: 'text-red-600',
          description: 'Gestion des sinistres'
        }
      ]
    },
    {
      id: 'analytics',
      name: 'Analytics & Conformité',
      icon: TrendingUp,
      items: [
        { 
          id: 'analytics', 
          name: 'Analytics', 
          href: '/admin/analytics-insurance', 
          icon: PieChart, 
          color: 'text-indigo-600',
          description: 'Analyses et métriques'
        },
        { 
          id: 'compliance', 
          name: 'Conformité ACAPS', 
          href: '/admin/compliance-acaps', 
          icon: Shield, 
          color: 'text-emerald-600',
          description: 'Conformité réglementaire'
        }
      ]
    },
    {
      id: 'tools',
      name: 'Outils & Administration',
      icon: Briefcase,
      items: [
        { 
          id: 'tasks', 
          name: 'Tâches', 
          href: '/admin/tasks', 
          icon: CheckSquare, 
          color: 'text-blue-500',
          description: 'Tâches et rappels'
        },
        { 
          id: 'documents', 
          name: 'Documents', 
          href: '/admin/documents', 
          icon: FileText, 
          color: 'text-gray-600',
          description: 'Gestion documentaire'
        },
        { 
          id: 'users', 
          name: 'Utilisateurs', 
          href: '/admin/users', 
          icon: Users, 
          color: 'text-cyan-500',
          description: 'Gestion des utilisateurs'
        },
        { 
          id: 'settings', 
          name: 'Paramètres', 
          href: '/admin/settings', 
          icon: Settings, 
          color: 'text-gray-600',
          description: 'Configuration système'
        }
      ]
    }
  ];

  const isActive = (path: string) => location.pathname === path;

  const toggleSection = (sectionId: string) => {
    setCollapsedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const isSectionCollapsed = (sectionId: string) => collapsedSections.includes(sectionId);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Header */}
        <div className="flex items-center justify-between h-20 px-6 border-b border-gray-200 bg-gradient-to-r from-axa-blue to-blue-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-axa-blue" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">Moumen AXA</h1>
              <p className="text-xs text-blue-100">Console Admin</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-white hover:text-blue-200 p-2"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 overflow-y-auto">
          <div className="space-y-6">
            {navigationSections.map((section) => (
              <div key={section.id} className="space-y-2">
                {/* Section Header */}
                <button
                  onClick={() => toggleSection(section.id)}
                  className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <section.icon className="h-4 w-4" />
                    <span>{section.name}</span>
                  </div>
                  {isSectionCollapsed(section.id) ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </button>

                {/* Section Items */}
                <AnimatePresence>
                  {!isSectionCollapsed(section.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="space-y-1 ml-6"
                    >
                      {section.items.map((item) => (
                        <motion.div
                          key={item.id}
                          whileHover={{ x: 4 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <a
                            href={item.href}
                            className={`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group ${
                              isActive(item.href)
                                ? 'bg-axa-blue text-white shadow-lg transform scale-105'
                                : 'text-gray-700 hover:bg-gray-100 hover:text-axa-blue'
                            }`}
                            title={item.description}
                          >
                            <item.icon className={`mr-3 h-5 w-5 ${
                              isActive(item.href) ? 'text-white' : item.color
                            } group-hover:scale-110 transition-transform`} />
                            <div className="flex-1">
                              <span className="truncate">{item.name}</span>
                              {item.badge && (
                                <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                          </a>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-axa-blue rounded-full flex items-center justify-center">
              <span className="text-white text-lg font-medium">A</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate">Admin User</div>
              <div className="text-xs text-gray-500 truncate"><EMAIL></div>
            </div>
          </div>
          <button className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
            <LogOut className="mr-3 h-4 w-4" />
            Déconnexion
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Top Bar */}
        <div className="bg-white shadow-sm border-b border-gray-200 h-20 flex items-center px-6">
          <div className="flex items-center space-x-4 flex-1">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700 p-2"
            >
              <Menu className="h-6 w-6" />
            </button>
            <h1 className="text-2xl font-semibold text-gray-900">
              Console d'Administration Assurance
            </h1>
          </div>

          <div className="flex items-center space-x-4">
            {/* Barre de recherche */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher clients, devis, sinistres..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent w-80"
              />
            </div>

            {/* Contrôles de thème et langue */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setTheme(state.theme === 'light' ? 'dark' : 'light')}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                title="Changer le thème"
              >
                {state.theme === 'light' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
              </button>

              <div className="relative">
                <button
                  onClick={() => setLanguage(state.language === 'fr' ? 'ar' : 'fr')}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Changer la langue"
                >
                  <Globe className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Notifications */}
            <button
              onClick={() => setShowNotifications(true)}
              className="relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              title="Notifications"
            >
              <Bell className="h-6 w-6" />
              {state.unreadNotifications > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {state.unreadNotifications > 9 ? '9+' : state.unreadNotifications}
                </span>
              )}
            </button>

            {/* Menu utilisateur */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="w-10 h-10 bg-axa-blue rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {state.user?.name?.charAt(0) || 'A'}
                  </span>
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-gray-900">
                    {state.user?.name || 'Admin User'}
                  </div>
                  <div className="text-xs text-gray-500">
                    {state.user?.email || '<EMAIL>'}
                  </div>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </button>

              {/* Menu déroulant utilisateur */}
              <AnimatePresence>
                {showUserMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                  >
                    <div className="px-4 py-3 border-b border-gray-200">
                      <p className="text-sm font-medium text-gray-900">
                        {state.user?.name || 'Admin User'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {state.user?.email || '<EMAIL>'}
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                        {state.user?.role || 'Admin'}
                      </span>
                    </div>

                    <a
                      href="/admin/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Settings className="h-4 w-4 mr-3" />
                      Paramètres
                    </a>

                    <button
                      onClick={logout}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      Déconnexion
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="flex-1 p-6 overflow-auto">
          <ErrorBoundary>
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                {/* Routes principales */}
                <Route path="/dashboard-insurance" element={<AdminModuleWrapper moduleName="Dashboard Assurance"><DashboardOverview_Insurance /></AdminModuleWrapper>} />
                <Route path="/leads-insurance" element={<AdminModuleWrapper moduleName="Prospects Assurance"><LeadsManagement_Insurance /></AdminModuleWrapper>} />
                <Route path="/clients-insurance" element={<AdminModuleWrapper moduleName="Clients Assurance"><ClientsManagement_Insurance /></AdminModuleWrapper>} />
                <Route path="/quotes-pro" element={<AdminModuleWrapper moduleName="Devis Professionnels"><QuotesContracts_Professional /></AdminModuleWrapper>} />
                <Route path="/claims-pro" element={<AdminModuleWrapper moduleName="Sinistres Pro"><ClaimsManagement_Professional /></AdminModuleWrapper>} />
                <Route path="/analytics-insurance" element={<AdminModuleWrapper moduleName="Analytics Assurance"><Analytics_Insurance /></AdminModuleWrapper>} />
                <Route path="/compliance-acaps" element={<AdminModuleWrapper moduleName="Conformité ACAPS"><Compliance_ACAPS /></AdminModuleWrapper>} />
                
                {/* Routes secondaires */}
                <Route path="/tasks" element={<AdminModuleWrapper moduleName="Tâches"><TasksReminders /></AdminModuleWrapper>} />
                <Route path="/documents" element={<AdminModuleWrapper moduleName="Documents"><DocumentsManagement /></AdminModuleWrapper>} />
                <Route path="/users" element={<AdminModuleWrapper moduleName="Utilisateurs"><UserManagement /></AdminModuleWrapper>} />
                <Route path="/settings" element={<AdminModuleWrapper moduleName="Paramètres"><SettingsModule /></AdminModuleWrapper>} />
                
                {/* Redirection par défaut */}
                <Route path="/" element={<Navigate to="/admin/dashboard-insurance" replace />} />
                <Route path="*" element={<Navigate to="/admin/dashboard-insurance" replace />} />
              </Routes>
            </Suspense>
          </ErrorBoundary>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />

      {/* Click outside to close user menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
};
