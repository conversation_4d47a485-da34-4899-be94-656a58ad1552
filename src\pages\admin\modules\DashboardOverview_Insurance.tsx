import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Users,
  FileText,
  Clock,
  Target,
  Activity,
  Bell,
  Calculator,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Calendar,
  Zap,
  Eye,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { api } from '../../../services/api';
import { SystemDiagnostic  } from '../../../components/admin/SystemDiagnostic';
import { APIStatus  } from '../../../components/admin/APIStatus';
import { ModuleTester  } from '../../../components/admin/ModuleTester';

interface InsuranceDashboardStats {
  // KPIs Généraux
  totalPolicies: number;
  activePolicies: number;
  totalPremiums: number;
  monthlyPremiums: number;
  
  // KPIs Sinistres
  totalClaims: number;
  openClaims: number;
  claimsRatio: number; // Ratio sinistres/primes
  averageClaimAmount: number;
  
  // KPIs Commerciaux
  newPoliciesThisMonth: number;
  renewalsThisMonth: number;
  cancellationsThisMonth: number;
  conversionRate: number;
  
  // KPIs Financiers
  grossPremiums: number;
  netPremiums: number;
  commissionsEarned: number;
  profitMargin: number;
  
  // Données pour graphiques
  premiumsByProduct: Array<{ product: string; amount: number; count: number }>;
  claimsByMonth: Array<{ month: string; claims: number; amount: number }>;
  policyDistribution: Array<{ type: string; count: number; percentage: number }>;
  recentActivity: Array<{
    id: string;
    type: 'policy' | 'claim' | 'renewal' | 'payment';
    message: string;
    timestamp: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  
  // Alertes Réglementaires
  regulatoryAlerts: Array<{
    id: string;
    type: 'acaps' | 'tax' | 'compliance';
    message: string;
    severity: 'info' | 'warning' | 'critical';
    dueDate?: string;
  }>;
}

export const DashboardOverview_Insurance: React.FC = () => {
  const [stats, setStats] = useState<InsuranceDashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  useEffect(() => {
    loadInsuranceDashboard();
  }, [selectedPeriod]);

  const loadInsuranceDashboard = async () => {
    try {
      setIsLoading(true);
      const response = await api.getInsuranceDashboard(selectedPeriod);
      if (response.success) {
        setStats(response.data);
      } else {
        // Données mock spécifiques à l'assurance
        setStats({
          totalPolicies: 1247,
          activePolicies: 1156,
          totalPremiums: 2850000,
          monthlyPremiums: 285000,
          
          totalClaims: 89,
          openClaims: 23,
          claimsRatio: 0.65, // 65%
          averageClaimAmount: 15500,
          
          newPoliciesThisMonth: 34,
          renewalsThisMonth: 156,
          cancellationsThisMonth: 12,
          conversionRate: 0.78,
          
          grossPremiums: 2850000,
          netPremiums: 2565000,
          commissionsEarned: 285000,
          profitMargin: 0.22,
          
          premiumsByProduct: [
            { product: 'Auto', amount: 1425000, count: 623 },
            { product: 'Habitation', amount: 570000, count: 289 },
            { product: 'Santé', amount: 456000, count: 178 },
            { product: 'Prévoyance', amount: 285000, count: 134 },
            { product: 'Épargne', amount: 114000, count: 23 }
          ],
          
          claimsByMonth: [
            { month: 'Jan', claims: 15, amount: 185000 },
            { month: 'Fév', claims: 12, amount: 156000 },
            { month: 'Mar', claims: 18, amount: 234000 },
            { month: 'Avr', claims: 14, amount: 178000 },
            { month: 'Mai', claims: 16, amount: 198000 },
            { month: 'Juin', claims: 14, amount: 167000 }
          ],
          
          policyDistribution: [
            { type: 'Particuliers', count: 934, percentage: 75 },
            { type: 'Professionnels', count: 222, percentage: 18 },
            { type: 'Entreprises', count: 91, percentage: 7 }
          ],
          
          recentActivity: [
            { 
              id: '1', 
              type: 'claim', 
              message: 'Nouveau sinistre auto - Ahmed Benali', 
              timestamp: '2024-01-15T10:30:00Z',
              priority: 'high'
            },
            { 
              id: '2', 
              type: 'policy', 
              message: 'Police habitation souscrite - Fatima Zahra', 
              timestamp: '2024-01-15T09:15:00Z',
              priority: 'medium'
            },
            { 
              id: '3', 
              type: 'renewal', 
              message: 'Renouvellement auto - Hassan Idrissi', 
              timestamp: '2024-01-15T08:45:00Z',
              priority: 'low'
            },
            { 
              id: '4', 
              type: 'payment', 
              message: 'Paiement prime reçu - 2,400 DH', 
              timestamp: '2024-01-15T08:20:00Z',
              priority: 'low'
            }
          ],
          
          regulatoryAlerts: [
            {
              id: '1',
              type: 'acaps',
              message: 'Rapport trimestriel ACAPS à soumettre',
              severity: 'warning',
              dueDate: '2024-01-31'
            },
            {
              id: '2',
              type: 'tax',
              message: 'Déclaration TVA mensuelle',
              severity: 'info',
              dueDate: '2024-01-20'
            }
          ]
        });
      }
    } catch (error) {
      console.error('Erreur dashboard assurance:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'policy': return <Shield className="h-4 w-4" />;
      case 'claim': return <AlertTriangle className="h-4 w-4" />;
      case 'renewal': return <Calendar className="h-4 w-4" />;
      case 'payment': return <DollarSign className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-50 text-red-700';
      case 'warning': return 'border-yellow-500 bg-yellow-50 text-yellow-700';
      case 'info': return 'border-blue-500 bg-blue-50 text-blue-700';
      default: return 'border-gray-500 bg-gray-50 text-gray-700';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">Erreur lors du chargement du dashboard</p>
      </div>
    );
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* En-tête avec sélecteur de période */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard Assurance</h1>
          <p className="text-gray-600">Vue d'ensemble de votre activité d'assurance</p>
        </div>
        
        <div className="flex space-x-2">
          {['day', 'week', 'month', 'quarter', 'year'].map((period) => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                selectedPeriod === period
                  ? 'bg-axa-blue text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {period === 'day' ? 'Jour' : 
               period === 'week' ? 'Semaine' :
               period === 'month' ? 'Mois' :
               period === 'quarter' ? 'Trimestre' : 'Année'}
            </button>
          ))}
        </div>
      </div>

      {/* Alertes Réglementaires */}
      {stats.regulatoryAlerts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center mb-4">
            <Bell className="h-5 w-5 text-red-500 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Alertes Réglementaires</h2>
          </div>
          <div className="space-y-3">
            {stats.regulatoryAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border-l-4 ${getSeverityColor(alert.severity)}`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{alert.message}</p>
                    <p className="text-sm opacity-75">Type: {alert.type.toUpperCase()}</p>
                  </div>
                  {alert.dueDate && (
                    <span className="text-xs bg-white px-2 py-1 rounded">
                      Échéance: {new Date(alert.dueDate).toLocaleDateString('fr-FR')}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* KPIs Principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Polices Actives */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Polices Actives</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activePolicies.toLocaleString()}</p>
              <p className="text-xs text-gray-500">sur {stats.totalPolicies.toLocaleString()} total</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">+{stats.newPoliciesThisMonth} ce mois</span>
          </div>
        </motion.div>

        {/* Primes Collectées */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Primes Mensuelles</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyPremiums)}</p>
              <p className="text-xs text-gray-500">Total: {formatCurrency(stats.totalPremiums)}</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">Marge: {formatPercentage(stats.profitMargin)}</span>
          </div>
        </motion.div>

        {/* Ratio Sinistres */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ratio S/P</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(stats.claimsRatio)}</p>
              <p className="text-xs text-gray-500">{stats.openClaims} sinistres ouverts</p>
            </div>
            <div className={`p-3 rounded-full ${stats.claimsRatio > 0.7 ? 'bg-red-100' : 'bg-yellow-100'}`}>
              <AlertTriangle className={`h-6 w-6 ${stats.claimsRatio > 0.7 ? 'text-red-600' : 'text-yellow-600'}`} />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <Calculator className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600 ml-1">Moy: {formatCurrency(stats.averageClaimAmount)}</span>
          </div>
        </motion.div>

        {/* Taux de Conversion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taux Conversion</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(stats.conversionRate)}</p>
              <p className="text-xs text-gray-500">{stats.renewalsThisMonth} renouvellements</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowDown className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-600 ml-1">{stats.cancellationsThisMonth} résiliations</span>
          </div>
        </motion.div>
      </div>

      {/* Graphiques Principaux */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition des Primes par Produit */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Primes par Produit</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={stats.premiumsByProduct}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="product" />
              <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
              <Tooltip
                formatter={(value: number) => [formatCurrency(value), 'Primes']}
                labelFormatter={(label) => `Produit: ${label}`}
              />
              <Bar dataKey="amount" fill="#0088FE" />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Évolution des Sinistres */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution des Sinistres</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={stats.claimsByMonth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
              <Tooltip
                formatter={(value: number, name: string) => [
                  name === 'claims' ? value : formatCurrency(value),
                  name === 'claims' ? 'Nombre' : 'Montant'
                ]}
              />
              <Bar yAxisId="right" dataKey="amount" fill="#FF8042" />
              <Line yAxisId="left" type="monotone" dataKey="claims" stroke="#00C49F" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Répartition des Polices et Activité Récente */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Répartition des Polices */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition Clientèle</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={stats.policyDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percentage }) => `${name} ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {stats.policyDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value: number) => [value, 'Polices']} />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Activité Récente */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="lg:col-span-2 bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Activité Récente</h3>
            <button className="text-axa-blue hover:text-blue-800 text-sm font-medium">
              Voir tout
            </button>
          </div>
          <div className="space-y-3">
            {stats.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                <div className={`p-2 rounded-full ${getPriorityColor(activity.priority)}`}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(activity.timestamp).toLocaleString('fr-FR')}
                  </p>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(activity.priority)}`}>
                  {activity.priority === 'high' ? 'Urgent' :
                   activity.priority === 'medium' ? 'Moyen' : 'Faible'}
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Composants de Diagnostic Système */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* API Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <APIStatus />
        </motion.div>

        {/* Module Tester */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <ModuleTester />
        </motion.div>

        {/* System Diagnostic */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1 }}
        >
          <SystemDiagnostic />
        </motion.div>
      </div>

      {/* Métriques Avancées */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Métriques Avancées</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Rentabilité par Produit */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-700">Rentabilité par Produit</h4>
            {stats.premiumsByProduct.map((product, index) => {
              const profitability = Math.random() * 0.4 + 0.1; // Mock data
              return (
                <div key={product.product} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{product.product}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${profitability * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{formatPercentage(profitability)}</span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Indicateurs de Performance */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-700">Indicateurs Clés</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Taux de rétention</span>
                <span className="text-sm font-medium text-green-600">94.2%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Délai moyen règlement</span>
                <span className="text-sm font-medium text-blue-600">12 jours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Satisfaction client</span>
                <span className="text-sm font-medium text-green-600">4.7/5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Coût d'acquisition</span>
                <span className="text-sm font-medium text-orange-600">245 DH</span>
              </div>
            </div>
          </div>

          {/* Objectifs du Mois */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-700">Objectifs du Mois</h4>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Nouvelles polices</span>
                  <span className="text-sm font-medium">34/40</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Primes collectées</span>
                  <span className="text-sm font-medium">285K/300K</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Renouvellements</span>
                  <span className="text-sm font-medium">156/180</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export { DashboardOverview_Insurance as default };
