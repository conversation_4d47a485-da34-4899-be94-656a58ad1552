import type { AdminModule, AdminUser } from '../types/admin';

export type LogLevel = 'info' | 'warning' | 'error' | 'security';
export type LogAction = 'create' | 'read' | 'update' | 'delete' | 'login' | 'logout' | 'access' | 'error';

export interface AdminLog {
  id: string;
  timestamp: string;
  level: LogLevel;
  action: LogAction;
  module: AdminModule;
  message: string;
  userId: string;
  userEmail: string;
  userRole: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

class AdminLogger {
  private static instance: AdminLogger;
  private logQueue: AdminLog[] = [];
  private isProcessing = false;
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds

  private constructor() {
    this.startAutoFlush();
  }

  public static getInstance(): AdminLogger {
    if (!AdminLogger.instance) {
      AdminLogger.instance = new AdminLogger();
    }
    return AdminLogger.instance;
  }

  private getCurrentUser(): AdminUser | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  }

  private async getClientInfo() {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const { ip } = await response.json();
      return {
        ipAddress: ip,
        userAgent: navigator.userAgent
      };
    } catch {
      return {
        ipAddress: 'unknown',
        userAgent: navigator.userAgent
      };
    }
  }

  private createLogEntry(
    level: LogLevel,
    action: LogAction,
    module: AdminModule,
    message: string,
    metadata?: Record<string, any>
  ): AdminLog {
    const user = this.getCurrentUser();
    
    return {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      level,
      action,
      module,
      message,
      userId: user?.id || 'anonymous',
      userEmail: user?.email || 'anonymous',
      userRole: user?.role || 'anonymous',
      metadata: {
        ...metadata,
        url: window.location.href,
        sessionId: localStorage.getItem('sessionId')
      }
    };
  }

  private async flushLogs() {
    if (this.isProcessing || this.logQueue.length === 0) return;

    this.isProcessing = true;
    const batch = this.logQueue.splice(0, this.batchSize);

    try {
      const clientInfo = await this.getClientInfo();
      const logsWithClientInfo = batch.map(log => ({
        ...log,
        ...clientInfo
      }));

      const response = await fetch('/api/admin/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(logsWithClientInfo)
      });

      if (!response.ok) {
        // En cas d'échec, remettre les logs dans la queue
        this.logQueue.unshift(...batch);
        throw new Error('Échec de l\'envoi des logs');
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi des logs:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private startAutoFlush() {
    setInterval(() => {
      this.flushLogs();
    }, this.flushInterval);
  }

  public async log(
    level: LogLevel,
    action: LogAction,
    module: AdminModule,
    message: string,
    metadata?: Record<string, any>
  ) {
    const logEntry = this.createLogEntry(level, action, module, message, metadata);
    this.logQueue.push(logEntry);

    if (this.logQueue.length >= this.batchSize) {
      await this.flushLogs();
    }

    // Pour les logs critiques, forcer l'envoi immédiat
    if (level === 'security' || level === 'error') {
      await this.flushLogs();
    }
  }

  public info(module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) {
    return this.log('info', action, module, message, metadata);
  }

  public warning(module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) {
    return this.log('warning', action, module, message, metadata);
  }

  public error(module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) {
    return this.log('error', action, module, message, metadata);
  }

  public security(module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) {
    return this.log('security', action, module, message, metadata);
  }
}

export const adminLogger = AdminLogger.getInstance();
export default adminLogger;
