# MOUMEN TECHNIQUE ET PREVOYANCE - Application Fullstack

Application web fullstack moderne pour l'agence d'assurance MOUMEN TECHNIQUE ET PREVOYANCE, agent général AXA au Maroc.

## 🏗️ Architecture

- **Frontend** : React 18 + TypeScript + Vite + Tailwind CSS
- **Backend** : Node.js + Express + TypeScript
- **Base de données** : PostgreSQL + Prisma ORM
- **Authentification** : JWT + Sessions sécurisées
- **Email** : Nodemailer avec templates HTML
- **Upload** : Multer pour la gestion des fichiers
- **Logs** : Winston avec rotation automatique
- **Tests** : Vitest + React Testing Library

## 🚀 Fonctionnalités

### Frontend Public
- **Page d'accueil** avec présentation des services
- **Catalogue de produits** d'assurance (Auto, Habitation, Santé, Prévoyance, Épargne)
- **Formulaire de devis** en ligne
- **Page de contact** avec informations complètes
- **Blog/Actualités** pour les nouvelles du secteur
- **Espace client** pour la gestion des contrats

### Interface d'Administration
- **Dashboard** avec analytics et KPIs
- **Gestion des leads** et CRM intégré
- **Gestion des clients** et contrats
- **Gestion des devis** et propositions
- **Gestion des sinistres**
- **Analytics** et rapports détaillés
- **Marketing digital** et campagnes
- **E-Learning** et formation
- **Gestion de contenu** (pages, blog)
- **Gestion des bandeaux** d'information
- **Générateur PDF** pour documents
- **Calculateurs** d'assurance
- **Intégrations** avec systèmes tiers
- **Sécurité** et conformité
- **Gestion des utilisateurs**

## 🛠️ Technologies Utilisées

### Frontend
- **React 18** avec TypeScript
- **Vite** pour le build et le développement
- **Tailwind CSS** pour le styling
- **Framer Motion** pour les animations
- **React Router** pour la navigation
- **React Hook Form** pour les formulaires
- **Recharts** pour les graphiques
- **Lucide React** pour les icônes

### Outils de Développement
- **TypeScript** pour le typage statique
- **ESLint** pour la qualité du code
- **Vitest** pour les tests unitaires
- **React Testing Library** pour les tests de composants

### Optimisations
- **Lazy Loading** des modules admin
- **Code Splitting** automatique
- **Error Boundaries** pour la gestion d'erreurs
- **SEO optimisé** avec meta tags dynamiques
- **Accessibilité** (ARIA, navigation clavier)

## 📦 Installation

### Prérequis
- Node.js 18+
- PostgreSQL 14+
- npm ou yarn

### Installation Rapide (Fullstack)
```bash
# Installation complète (frontend + backend + base de données)
npm run setup
```

### Installation Manuelle

#### 1. Frontend
```bash
npm install
```

#### 2. Backend
```bash
cd server
npm install
```

#### 3. Base de Données
```bash
# Créer la base PostgreSQL
createdb mtp_database

# Configurer l'URL dans server/.env
DATABASE_URL="postgresql://username:password@localhost:5432/mtp_database"

# Générer le client Prisma et migrer
cd server
npm run db:generate
npm run db:migrate
npm run db:seed
```

## 🚀 Développement

### Démarrer l'application complète (Fullstack)
```bash
npm run fullstack:dev
```
- Frontend : `http://localhost:5173`
- Backend API : `http://localhost:3001`

### Démarrer séparément

#### Frontend seulement
```bash
npm run dev
```

#### Backend seulement
```bash
npm run server:dev
```

### Build de production
```bash
npm run build
```

### Prévisualisation du build
```bash
npm run preview
```

### Lancer les tests
```bash
# Tests en mode watch
npm run test

# Tests avec interface UI
npm run test:ui

# Tests avec couverture
npm run test:coverage
```

### Linting
```bash
npm run lint
```

## 📁 Structure du Projet

```
src/
├── components/           # Composants réutilisables
│   ├── common/          # Composants communs (Toast, ErrorBoundary, etc.)
│   └── layout/          # Composants de layout (Header, Footer, etc.)
├── pages/               # Pages de l'application
│   ├── admin/           # Interface d'administration
│   │   ├── modules/     # Modules admin spécialisés
│   │   └── layout/      # Layout admin
│   ├── Home.tsx         # Page d'accueil
│   ├── Products.tsx     # Catalogue produits
│   ├── Contact.tsx      # Page de contact
│   └── ...
├── hooks/               # Hooks personnalisés
├── types/               # Types TypeScript
├── utils/               # Utilitaires et helpers
├── test/                # Tests unitaires
└── App.tsx              # Composant racine
```

## 🔧 Configuration

### Variables d'Environnement
Créez un fichier `.env.local` à la racine du projet :

```env
# Configuration de base
VITE_APP_TITLE="MOUMEN TECHNIQUE ET PREVOYANCE"
VITE_APP_DESCRIPTION="Agent Général AXA Maroc"

# URLs de l'API (à configurer selon l'environnement)
VITE_API_BASE_URL="https://api.moumen-axa.ma"

# Configuration Google Analytics (optionnel)
VITE_GA_TRACKING_ID="GA_TRACKING_ID"

# Configuration des emails (optionnel)
VITE_CONTACT_EMAIL="<EMAIL>"
VITE_SUPPORT_EMAIL="<EMAIL>"
```

### Tailwind CSS
Le projet utilise Tailwind CSS avec une configuration personnalisée incluant :
- Couleurs de la marque AXA
- Polices personnalisées
- Animations et transitions

### TypeScript
Configuration stricte avec :
- Types stricts activés
- Vérification des imports
- Support des chemins absolus

## 🎨 Guide de Style

### Composants
- Utilisez des composants fonctionnels avec TypeScript
- Préférez les hooks aux classes
- Documentez les props avec JSDoc
- Utilisez des noms explicites

### CSS/Styling
- Utilisez Tailwind CSS en priorité
- Évitez les styles inline sauf exceptions
- Utilisez les classes utilitaires Tailwind
- Respectez le design system AXA

### État et Données
- Utilisez useState pour l'état local
- Utilisez useEffect avec précaution
- Préférez les hooks personnalisés pour la logique complexe
- Validez toujours les données utilisateur

## 🧪 Tests

### Structure des Tests
```
src/test/
├── components/          # Tests des composants
├── utils/              # Tests des utilitaires
├── hooks/              # Tests des hooks
└── setup.ts            # Configuration des tests
```

### Conventions de Test
- Un fichier de test par composant/utilitaire
- Nommage : `ComponentName.test.tsx`
- Tests des cas normaux et des cas d'erreur
- Mocking des dépendances externes

## 🚀 Déploiement

### Build de Production
```bash
npm run build
```

### Optimisations Automatiques
- Minification du code
- Tree shaking
- Code splitting
- Compression des assets
- Optimisation des images

### Serveur Web
Le build génère des fichiers statiques dans le dossier `dist/` qui peuvent être servis par :
- Nginx
- Apache
- Netlify
- Vercel
- AWS S3 + CloudFront

## 📊 Performance

### Métriques Cibles
- **First Contentful Paint** : < 1.5s
- **Largest Contentful Paint** : < 2.5s
- **Cumulative Layout Shift** : < 0.1
- **First Input Delay** : < 100ms

### Optimisations Implémentées
- Lazy loading des modules admin
- Preload des ressources critiques
- Code splitting automatique
- Compression des images
- Mise en cache des assets

## 🔒 Sécurité

### Mesures Implémentées
- Validation côté client et serveur
- Sanitisation des inputs
- Protection CSRF
- Headers de sécurité
- Audit régulier des dépendances

### Bonnes Pratiques
- Validation stricte des formulaires
- Gestion sécurisée des erreurs
- Logs d'audit
- Chiffrement des données sensibles

## 🌐 Accessibilité

### Standards Respectés
- **WCAG 2.1 AA** compliance
- Navigation au clavier
- Support des lecteurs d'écran
- Contrastes suffisants
- Textes alternatifs

### Fonctionnalités
- Skip links
- ARIA labels
- Focus management
- Annonces aux lecteurs d'écran

## 📱 Responsive Design

### Breakpoints
- **Mobile** : < 768px
- **Tablet** : 768px - 1024px
- **Desktop** : > 1024px

### Approche
- Mobile-first design
- Grille flexible
- Images responsives
- Touch-friendly interfaces

## 🤝 Contribution

### Workflow Git
1. Fork du repository
2. Création d'une branche feature
3. Développement et tests
4. Pull request avec description détaillée

### Standards de Code
- Respect des conventions TypeScript
- Tests unitaires obligatoires
- Documentation des nouvelles fonctionnalités
- Review de code avant merge

## 📞 Support

### Contact Technique
- **Email** : <EMAIL>
- **Documentation** : Voir le wiki du projet
- **Issues** : Utiliser le système d'issues GitHub

### Contact Commercial
- **Téléphone** : +212 5XX-XXXXXX
- **Email** : <EMAIL>
- **Adresse** : 123 Boulevard Mohammed V, Casablanca

## 📄 Licence

Ce projet est la propriété de MOUMEN TECHNIQUE ET PREVOYANCE. Tous droits réservés.

---

**Développé avec ❤️ pour MOUMEN TECHNIQUE ET PREVOYANCE - Agent Général AXA Maroc**
