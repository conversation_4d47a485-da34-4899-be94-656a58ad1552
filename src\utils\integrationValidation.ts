export interface Integration {
  name: string;
  type: string;
  endpoint: string;
  status?: string;
  description?: string;
}

export function validateIntegration(integration: Integration): string[] {
  const errors: string[] = [];

  if (!integration.name || integration.name.trim().length === 0) {
    errors.push('Le nom de l\'intégration est requis');
  }

  if (!integration.type || integration.type.trim().length === 0) {
    errors.push('Le type d\'intégration est requis');
  }

  if (!integration.endpoint || integration.endpoint.trim().length === 0) {
    errors.push('L\'endpoint de l\'intégration est requis');
  }

  return errors;
}