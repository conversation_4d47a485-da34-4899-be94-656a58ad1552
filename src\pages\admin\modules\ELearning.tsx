import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Play, 
  Pause, 
  CheckCircle, 
  Clock, 
  Users,
  Award,
  TrendingUp,
  Download,
  Upload,
  Plus,
  Edit,
  Eye,
  Star,
  BarChart3,
  Calendar,
  Target,
  Zap,
  Shield,
  FileText,
  Video,
  Headphones,
  Monitor
} from 'lucide-react';
 // Ligne corrigée automatiquement

export const ELearning: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('courses');
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [newCourse, setNewCourse] = useState({
    title: '',
    description: '',
    category: '',
    level: 'Débutant',
    duration: '',
    modules: 1,
    instructor: '',
    thumbnail: '',
    type: 'video',
  });

  const tabs = [
    { id: 'courses', name: 'Formations', icon: BookOpen },
    { id: 'progress', name: 'Progression', icon: TrendingUp },
    { id: 'certifications', name: 'Certifications', icon: Award },
    { id: 'library', name: 'Bibliothèque', icon: FileText }
  ];

  const learningStats = [
    { title: 'Formations actives', value: '12', change: '+3', icon: BookOpen, color: 'bg-blue-500' },
    { title: 'Collaborateurs formés', value: '8/12', change: '67%', icon: Users, color: 'bg-green-500' },
    { title: 'Heures de formation', value: '156h', change: '+24h', icon: Clock, color: 'bg-purple-500' },
    { title: 'Certifications obtenues', value: '23', change: '+5', icon: Award, color: 'bg-yellow-500' }
  ];

  const courses = [
    {
      id: 1,
      title: 'Nouveaux Produits AXA 2024',
      description: 'Découvrez les dernières innovations produits AXA et leurs avantages concurrentiels',
      category: 'Produits',
      level: 'Intermédiaire',
      duration: '2h 30min',
      modules: 8,
      enrolled: 12,
      completed: 8,
      rating: 4.8,
      instructor: 'Expert AXA Formation',
      thumbnail: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      progress: 75,
      type: 'video'
    },
    {
      id: 2,
      title: 'Techniques de Vente Consultative',
      description: 'Maîtrisez l\'art de la vente consultative pour mieux conseiller vos clients',
      category: 'Commercial',
      level: 'Avancé',
      duration: '3h 15min',
      modules: 12,
      enrolled: 10,
      completed: 6,
      rating: 4.9,
      instructor: 'Mohammed MOUMEN',
      thumbnail: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      progress: 60,
      type: 'interactive'
    },
    {
      id: 3,
      title: 'Gestion des Sinistres Complexes',
      description: 'Apprenez à traiter efficacement les sinistres complexes et sensibles',
      category: 'Sinistres',
      level: 'Expert',
      duration: '4h 00min',
      modules: 15,
      enrolled: 6,
      completed: 4,
      rating: 4.7,
      instructor: 'Ahmed BENALI',
      thumbnail: 'https://images.pexels.com/photos/5849579/pexels-photo-5849579.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      progress: 67,
      type: 'document'
    },
    {
      id: 4,
      title: 'Réglementation Assurance Maroc',
      description: 'Mise à jour sur la réglementation marocaine en matière d\'assurance',
      category: 'Réglementation',
      level: 'Débutant',
      duration: '1h 45min',
      modules: 6,
      enrolled: 12,
      completed: 12,
      rating: 4.6,
      instructor: 'Expert Juridique',
      thumbnail: 'https://images.pexels.com/photos/5668858/pexels-photo-5668858.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'completed',
      progress: 100,
      type: 'audio'
    }
  ];

  const userProgress = [
    {
      id: 1,
      name: 'Mohammed MOUMEN',
      role: 'Directeur Général',
      coursesCompleted: 8,
      totalCourses: 12,
      hoursSpent: 45,
      certifications: 6,
      lastActivity: '2024-01-15',
      avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150'
    },
    {
      id: 2,
      name: 'Fatima ALAMI',
      role: 'Manager Commercial',
      coursesCompleted: 6,
      totalCourses: 10,
      hoursSpent: 32,
      certifications: 4,
      lastActivity: '2024-01-14',
      avatar: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=150'
    },
    {
      id: 3,
      name: 'Ahmed BENALI',
      role: 'Conseiller Sinistres',
      coursesCompleted: 7,
      totalCourses: 9,
      hoursSpent: 38,
      certifications: 5,
      lastActivity: '2024-01-13',
      avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150'
    }
  ];

  const certifications = [
    {
      id: 1,
      name: 'Expert Produits AXA',
      description: 'Certification complète sur tous les produits AXA',
      requirements: ['Nouveaux Produits AXA 2024', 'Techniques de Vente', 'Réglementation'],
      duration: '40h',
      validity: '2 ans',
      holders: 3,
      badge: 'https://images.pexels.com/photos/1205651/pexels-photo-1205651.jpeg?auto=compress&cs=tinysrgb&w=150'
    },
    {
      id: 2,
      name: 'Spécialiste Sinistres',
      description: 'Expertise avancée en gestion des sinistres',
      requirements: ['Gestion Sinistres Complexes', 'Réglementation', 'Communication Client'],
      duration: '30h',
      validity: '3 ans',
      holders: 2,
      badge: 'https://images.pexels.com/photos/1205651/pexels-photo-1205651.jpeg?auto=compress&cs=tinysrgb&w=150'
    }
  ];

  const library = [
    {
      id: 1,
      title: 'Guide Complet Assurance Auto',
      type: 'PDF',
      category: 'Produits',
      size: '2.4 MB',
      downloads: 45,
      uploadDate: '2024-01-10'
    },
    {
      id: 2,
      title: 'Procédures Sinistres 2024',
      type: 'PDF',
      category: 'Procédures',
      size: '1.8 MB',
      downloads: 32,
      uploadDate: '2024-01-08'
    },
    {
      id: 3,
      title: 'Webinaire AXA Innovation',
      type: 'Video',
      category: 'Innovation',
      size: '156 MB',
      downloads: 28,
      uploadDate: '2024-01-05'
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Débutant': return 'bg-green-100 text-green-800';
      case 'Intermédiaire': return 'bg-yellow-100 text-yellow-800';
      case 'Avancé': return 'bg-orange-100 text-orange-800';
      case 'Expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return Video;
      case 'audio': return Headphones;
      case 'interactive': return Monitor;
      case 'document': return FileText;
      default: return BookOpen;
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">E-Learning & Formation</h1>
          <p className="text-gray-600 mt-2">Plateforme de formation continue pour vos collaborateurs</p>
        </div>
        <div className="flex space-x-4">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Upload className="h-4 w-4 mr-2" />
            Importer contenu
          </button>
          <button className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors" onClick={() => setOpenAddModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle formation
          </button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {learningStats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-green-500">{stat.change}</p>
              </div>
              <div className={`${stat.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <div className="flex space-x-4 border-b border-gray-200 pb-4">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedTab === tab.id
                  ? 'bg-axa-blue text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Courses Tab */}
        {selectedTab === 'courses' && (
          <div className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => {
                const TypeIcon = getTypeIcon(course.type);
                return (
                  <div key={course.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <img 
                        src={course.thumbnail} 
                        alt={course.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
                          {course.level}
                        </span>
                      </div>
                      <div className="absolute top-4 right-4">
                        <TypeIcon className="h-6 w-6 text-white bg-black/50 rounded p-1" />
                      </div>
                      {course.status === 'completed' && (
                        <div className="absolute bottom-4 right-4">
                          <CheckCircle className="h-6 w-6 text-green-500 bg-white rounded-full" />
                        </div>
                      )}
                    </div>
                    
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-axa-blue font-medium">{course.category}</span>
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm text-gray-600">{course.rating}</span>
                        </div>
                      </div>
                      
                      <h3 className="font-semibold text-gray-900 mb-2">{course.title}</h3>
                      <p className="text-sm text-gray-600 mb-4">{course.description}</p>
                      
                      <div className="space-y-2 text-xs text-gray-500 mb-4">
                        <div className="flex justify-between">
                          <span>Durée: {course.duration}</span>
                          <span>{course.modules} modules</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Inscrits: {course.enrolled}</span>
                          <span>Terminé: {course.completed}</span>
                        </div>
                      </div>

                      {course.progress > 0 && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Progression</span>
                            <span>{course.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-axa-blue h-2 rounded-full transition-all duration-300"
                              style={{ width: `${course.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      <div className="flex space-x-2">
                        <button className="flex-1 px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors text-sm">
                          {course.progress > 0 ? 'Continuer' : 'Commencer'}
                        </button>
                        <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                          <Eye className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Progress Tab */}
        {selectedTab === 'progress' && (
          <div className="mt-6">
            <div className="space-y-6">
              {userProgress.map((user) => (
                <div key={user.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <img 
                      src={user.avatar} 
                      alt={user.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{user.name}</h3>
                      <p className="text-sm text-gray-600">{user.role}</p>
                      <p className="text-xs text-gray-500">Dernière activité: {user.lastActivity}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-axa-blue">{user.coursesCompleted}/{user.totalCourses}</div>
                      <div className="text-sm text-gray-600">Formations</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-500">{user.hoursSpent}h</div>
                      <div className="text-sm text-gray-600">Temps passé</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-500">{user.certifications}</div>
                      <div className="text-sm text-gray-600">Certifications</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-500">
                        {Math.round((user.coursesCompleted / user.totalCourses) * 100)}%
                      </div>
                      <div className="text-sm text-gray-600">Progression</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Certifications Tab */}
        {selectedTab === 'certifications' && (
          <div className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {certifications.map((cert) => (
                <div key={cert.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <img 
                      src={cert.badge} 
                      alt={cert.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{cert.name}</h3>
                      <p className="text-sm text-gray-600">{cert.description}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Prérequis:</h4>
                      <div className="flex flex-wrap gap-1">
                        {cert.requirements.map((req, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                            {req}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-gray-600">Durée</div>
                        <div className="font-semibold">{cert.duration}</div>
                      </div>
                      <div>
                        <div className="text-gray-600">Validité</div>
                        <div className="font-semibold">{cert.validity}</div>
                      </div>
                      <div>
                        <div className="text-gray-600">Certifiés</div>
                        <div className="font-semibold text-green-600">{cert.holders}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Library Tab */}
        {selectedTab === 'library' && (
          <div className="mt-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Document</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Catégorie</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Taille</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Téléchargements</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {library.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <span className="font-medium text-gray-900">{item.title}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">{item.type}</td>
                      <td className="py-4 px-4">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {item.category}
                        </span>
                      </td>
                      <td className="py-4 px-4">{item.size}</td>
                      <td className="py-4 px-4">{item.downloads}</td>
                      <td className="py-4 px-4">{item.uploadDate}</td>
                      <td className="py-4 px-4">
                        <div className="flex space-x-2">
                          <button className="p-1 text-gray-400 hover:text-blue-600">
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-green-600">
                            <Download className="h-4 w-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-orange-600">
                            <Edit className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </motion.div>

      {/* Modal d'ajout de formation */}
      {openAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-white rounded-2xl shadow-2xl p-4 sm:p-8 w-full max-w-lg relative mx-2 max-h-[95vh] overflow-y-auto">
            <button className="absolute top-3 right-4 text-gray-500 hover:text-gray-700 text-3xl font-bold" onClick={() => setOpenAddModal(false)} aria-label="Fermer">&times;</button>
            <h2 className="text-3xl font-extrabold mb-8 text-center text-axa-blue">Ajouter une formation</h2>
            <form onSubmit={e => { e.preventDefault(); /* Ajout logique ici */ setOpenAddModal(false); }} className="space-y-6">
              <div className="grid grid-cols-1 gap-5">
                <div>
                  <label className="block text-sm font-semibold text-axa-blue mb-1">Titre</label>
                  <input type="text" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="Titre de la formation" value={newCourse.title} onChange={e => setNewCourse({ ...newCourse, title: e.target.value })} required />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-axa-blue mb-1">Description</label>
                  <textarea className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="Description de la formation" value={newCourse.description} onChange={e => setNewCourse({ ...newCourse, description: e.target.value })} required rows={3} />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Catégorie</label>
                    <input type="text" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="Catégorie" value={newCourse.category} onChange={e => setNewCourse({ ...newCourse, category: e.target.value })} required />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Niveau</label>
                    <select className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" value={newCourse.level} onChange={e => setNewCourse({ ...newCourse, level: e.target.value })}>
                      <option value="Débutant">Débutant</option>
                      <option value="Intermédiaire">Intermédiaire</option>
                      <option value="Avancé">Avancé</option>
                      <option value="Expert">Expert</option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Durée</label>
                    <input type="text" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="ex: 2h 30min" value={newCourse.duration} onChange={e => setNewCourse({ ...newCourse, duration: e.target.value })} required />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Nombre de modules</label>
                    <input type="number" min="1" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="Modules" value={newCourse.modules} onChange={e => setNewCourse({ ...newCourse, modules: Number(e.target.value) })} required />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Formateur</label>
                    <input type="text" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="Nom du formateur" value={newCourse.instructor} onChange={e => setNewCourse({ ...newCourse, instructor: e.target.value })} required />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-axa-blue mb-1">Type</label>
                    <select className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" value={newCourse.type} onChange={e => setNewCourse({ ...newCourse, type: e.target.value })}>
                      <option value="video">Vidéo</option>
                      <option value="audio">Audio</option>
                      <option value="interactive">Interactif</option>
                      <option value="document">Document</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-axa-blue mb-1">URL de la miniature</label>
                  <input type="text" className="w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue transition" placeholder="https://..." value={newCourse.thumbnail} onChange={e => setNewCourse({ ...newCourse, thumbnail: e.target.value })} required />
                </div>
              </div>
              <button type="submit" className="w-full bg-axa-blue text-white rounded-lg p-3 font-bold text-lg hover:bg-blue-800 mt-4 transition">Ajouter</button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export { ELearning as default };