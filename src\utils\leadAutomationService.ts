import { Lead } from '../types/admin';
import { NotificationManager } from './notificationManager';
import { LeadScoringService } from './leadScoringService';

interface AutomationRule {
  condition: (lead: Lead, scoreDelta: number) => boolean;
  action: (lead: Lead) => void;
  priority: number;
}

export class LeadAutomationService {
  private static instance: LeadAutomationService;
  private rules: AutomationRule[] = [];

  private constructor() {
    this.initializeDefaultRules();
  }

  public static getInstance(): LeadAutomationService {
    if (!LeadAutomationService.instance) {
      LeadAutomationService.instance = new LeadAutomationService();
    }
    return LeadAutomationService.instance;
  }

  private initializeDefaultRules() {
    // Règle pour les leads à haut potentiel
    this.addRule({
      condition: (lead, scoreDelta) => lead.score >= 80 && scoreDelta > 0,
      action: (lead) => {
        NotificationManager.getInstance().addNotification({
          type: 'LEAD_SCORE_CHANGE',
          title: 'Lead à Haut Potentiel Détecté',
          message: `${lead.name} a atteint un score de ${lead.score}. Action recommandée : Contact prioritaire`,
          date: new Date().toISOString(),
          priority: 'HIGH',
          data: { leadId: lead.id, action: 'CONTACT_PRIORITY' }
        });
      },
      priority: 1
    });

    // Règle pour les leads en perte d'engagement
    this.addRule({
      condition: (lead, scoreDelta) => scoreDelta <= -20,
      action: (lead) => {
        NotificationManager.getInstance().addNotification({
          type: 'LEAD_SCORE_CHANGE',
          title: 'Baisse Significative du Score',
          message: `Le score de ${lead.name} a baissé significativement. Action recommandée : Vérification du suivi`,
          date: new Date().toISOString(),
          priority: 'HIGH',
          data: { leadId: lead.id, action: 'CHECK_ENGAGEMENT' }
        });
      },
      priority: 2
    });

    // Règle pour les leads qualifiés
    this.addRule({
      condition: (lead) => lead.score >= 60 && lead.status === 'NEW',
      action: (lead) => {
        NotificationManager.getInstance().addNotification({
          type: 'LEAD_SCORE_CHANGE',
          title: 'Lead Qualifié',
          message: `${lead.name} est maintenant qualifié pour un suivi commercial`,
          date: new Date().toISOString(),
          priority: 'MEDIUM',
          data: { leadId: lead.id, action: 'QUALIFY_LEAD' }
        });
      },
      priority: 3
    });
  }

  public addRule(rule: AutomationRule) {
    this.rules.push(rule);
    this.rules.sort((a, b) => a.priority - b.priority);
  }

  public processLeadUpdate(lead: Lead, scoreDelta: number) {
    this.rules.forEach(rule => {
      if (rule.condition(lead, scoreDelta)) {
        rule.action(lead);
      }
    });
  }
}

export default LeadAutomationService;
