import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Shield, AlertTriangle, RefreshCw } from 'lucide-react';
import { useAuth } from '../../services/api';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'user';
  fallbackPath?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole = 'user',
  fallbackPath = '/admin/login'
}) => {
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const { isAuthenticated: checkAuth } = useAuth();

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        setIsChecking(true);
        setError(null);

        // Vérifier le token dans localStorage
        const token = localStorage.getItem('auth_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (!token) {
          setIsAuthenticated(false);
          setIsChecking(false);
          return;
        }

        // Vérifier si le token est valide
        const authStatus = checkAuth();
        
        if (authStatus) {
          setIsAuthenticated(true);
          
          // Récupérer le rôle utilisateur
          if (userInfo) {
            try {
              const user = JSON.parse(userInfo);
              setUserRole(user.role || 'user');
            } catch (e) {
              // Si pas d'info utilisateur, considérer comme admin pour l'instant
              setUserRole('admin');
            }
          } else {
            // Fallback: considérer comme admin si connecté
            setUserRole('admin');
          }
        } else {
          setIsAuthenticated(false);
          // Nettoyer le localStorage si le token n'est pas valide
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_info');
        }
      } catch (error) {
        console.error('Erreur lors de la vérification d\'authentification:', error);
        setError('Erreur de vérification d\'authentification');
        setIsAuthenticated(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [checkAuth, location.pathname]);

  // Affichage pendant la vérification
  if (isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-axa-blue mx-auto mb-4">
            <RefreshCw className="h-6 w-6" />
          </div>
          <p className="text-gray-600">Vérification des permissions...</p>
        </div>
      </div>
    );
  }

  // Affichage en cas d'erreur
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Erreur d'Authentification
            </h3>
            <p className="text-sm text-gray-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-axa-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Redirection si non authentifié
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Vérification du rôle requis
  if (requiredRole && userRole !== requiredRole) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
              <Shield className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Accès Restreint
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Vous n'avez pas les permissions nécessaires pour accéder à cette section.
            </p>
            <p className="text-xs text-gray-400 mb-4">
              Rôle requis: {requiredRole} | Votre rôle: {userRole}
            </p>
            <div className="space-y-2">
              <button
                onClick={() => window.history.back()}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
              >
                Retour
              </button>
              <button
                onClick={() => {
                  localStorage.removeItem('auth_token');
                  localStorage.removeItem('user_info');
                  window.location.href = '/admin/login';
                }}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-axa-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
              >
                Se reconnecter
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Afficher le contenu protégé
  return <>{children}</>;
};

export default ProtectedRoute;
