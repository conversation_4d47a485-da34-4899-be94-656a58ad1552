import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckSquare, 
  Clock, 
  Calendar, 
  Bell, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  User, 
  Flag, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  Phone,
  Mail,
  MessageSquare,
  FileText,
  Target,
  TrendingUp,
  BarChart3,
  Activity,
  Star,
  Eye,
  Send,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Users,
  Building,
  DollarSign
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface Task {
  id: string;
  title: string;
  description: string;
  type: 'CALL' | 'EMAIL' | 'MEETING' | 'FOLLOW_UP' | 'QUOTE' | 'DOCUMENT' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE';
  dueDate: string;
  assignedTo: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  relatedTo?: {
    type: 'CLIENT' | 'LEAD' | 'QUOTE' | 'CLAIM';
    id: string;
    name: string;
  };
  completedAt?: string;
  completedBy?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface Reminder {
  id: string;
  title: string;
  message: string;
  type: 'TASK_DUE' | 'FOLLOW_UP' | 'RENEWAL' | 'PAYMENT' | 'MEETING' | 'CUSTOM';
  triggerDate: string;
  isRecurring: boolean;
  recurringPattern?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  status: 'ACTIVE' | 'TRIGGERED' | 'DISMISSED' | 'SNOOZED';
  assignedTo: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  relatedTo?: {
    type: 'CLIENT' | 'LEAD' | 'QUOTE' | 'CLAIM' | 'TASK';
    id: string;
    name: string;
  };
  snoozeUntil?: string;
  createdAt: string;
  updatedAt: string;
}

export const TasksReminders: React.FC = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [assigneeFilter, setAssigneeFilter] = useState('all');
  const [showNewTaskModal, setShowNewTaskModal] = useState(false);
  const [showNewReminderModal, setShowNewReminderModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [selectedReminder, setSelectedReminder] = useState<Reminder | null>(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  useEffect(() => {
    if (activeTab === 'tasks') {
      loadTasks();
    } else {
      loadReminders();
    }
  }, [activeTab, searchTerm, statusFilter, priorityFilter, typeFilter, assigneeFilter]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadTasks = async () => {
    try {
      setIsLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (statusFilter !== 'all') params.status = statusFilter;
      if (priorityFilter !== 'all') params.priority = priorityFilter;
      if (typeFilter !== 'all') params.type = typeFilter;
      if (assigneeFilter !== 'all') params.assignedTo = assigneeFilter;

      // Mock data for now
      const mockTasks: Task[] = [
        {
          id: '1',
          title: 'Appeler Hassan Benjelloun',
          description: 'Suivi du devis assurance auto premium',
          type: 'CALL',
          priority: 'HIGH',
          status: 'PENDING',
          dueDate: '2024-01-16T14:00:00Z',
          assignedTo: '2',
          assignedUser: {
            id: '2',
            name: 'Conseiller 1',
            email: '<EMAIL>'
          },
          relatedTo: {
            type: 'CLIENT',
            id: '1',
            name: 'Hassan Benjelloun'
          },
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          title: 'Envoyer devis assurance flotte',
          description: 'Finaliser et envoyer le devis pour Entreprise ABC SARL',
          type: 'QUOTE',
          priority: 'URGENT',
          status: 'IN_PROGRESS',
          dueDate: '2024-01-15T17:00:00Z',
          assignedTo: '3',
          assignedUser: {
            id: '3',
            name: 'Conseiller 2',
            email: '<EMAIL>'
          },
          relatedTo: {
            type: 'QUOTE',
            id: '2',
            name: 'Devis Flotte ABC SARL'
          },
          createdAt: '2024-01-14T15:30:00Z',
          updatedAt: '2024-01-15T09:00:00Z'
        },
        {
          id: '3',
          title: 'Rendez-vous client Fatima Zahra',
          description: 'Présentation des options d\'assurance habitation',
          type: 'MEETING',
          priority: 'MEDIUM',
          status: 'PENDING',
          dueDate: '2024-01-17T10:30:00Z',
          assignedTo: '2',
          assignedUser: {
            id: '2',
            name: 'Conseiller 1',
            email: '<EMAIL>'
          },
          relatedTo: {
            type: 'LEAD',
            id: '2',
            name: 'Fatima Zahra'
          },
          createdAt: '2024-01-15T11:20:00Z',
          updatedAt: '2024-01-15T11:20:00Z'
        }
      ];

      setTasks(mockTasks);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des tâches', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadReminders = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for now
      const mockReminders: Reminder[] = [
        {
          id: '1',
          title: 'Renouvellement contrat Hassan Benjelloun',
          message: 'Le contrat d\'assurance auto arrive à échéance dans 30 jours',
          type: 'RENEWAL',
          triggerDate: '2024-02-01T09:00:00Z',
          isRecurring: false,
          status: 'ACTIVE',
          assignedTo: '2',
          assignedUser: {
            id: '2',
            name: 'Conseiller 1',
            email: '<EMAIL>'
          },
          relatedTo: {
            type: 'CLIENT',
            id: '1',
            name: 'Hassan Benjelloun'
          },
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          title: 'Suivi lead Mohamed Alami',
          message: 'Relancer le prospect pour la proposition d\'assurance santé',
          type: 'FOLLOW_UP',
          triggerDate: '2024-01-16T15:00:00Z',
          isRecurring: false,
          status: 'ACTIVE',
          assignedTo: '3',
          assignedUser: {
            id: '3',
            name: 'Conseiller 2',
            email: '<EMAIL>'
          },
          relatedTo: {
            type: 'LEAD',
            id: '3',
            name: 'Mohamed Alami'
          },
          createdAt: '2024-01-13T16:45:00Z',
          updatedAt: '2024-01-15T11:30:00Z'
        }
      ];

      setReminders(mockReminders);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des rappels', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTaskStatusChange = async (taskId: string, newStatus: string) => {
    try {
      // Update task status
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: newStatus as any, completedAt: newStatus === 'COMPLETED' ? new Date().toISOString() : undefined }
          : task
      ));
      showToast('Statut de la tâche mis à jour', 'success');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      showToast('Erreur lors de la mise à jour du statut', 'error');
    }
  };

  const handleReminderAction = async (reminderId: string, action: 'dismiss' | 'snooze') => {
    try {
      if (action === 'dismiss') {
        setReminders(prev => prev.map(reminder => 
          reminder.id === reminderId 
            ? { ...reminder, status: 'DISMISSED' }
            : reminder
        ));
        showToast('Rappel supprimé', 'success');
      } else if (action === 'snooze') {
        const snoozeUntil = new Date();
        snoozeUntil.setHours(snoozeUntil.getHours() + 1);
        
        setReminders(prev => prev.map(reminder => 
          reminder.id === reminderId 
            ? { ...reminder, status: 'SNOOZED', snoozeUntil: snoozeUntil.toISOString() }
            : reminder
        ));
        showToast('Rappel reporté d\'1 heure', 'success');
      }
    } catch (error) {
      console.error('Erreur lors de l\'action:', error);
      showToast('Erreur lors de l\'action sur le rappel', 'error');
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-gray-100 text-gray-800';
      case 'OVERDUE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTaskTypeIcon = (type: string) => {
    switch (type) {
      case 'CALL': return <Phone className="h-4 w-4" />;
      case 'EMAIL': return <Mail className="h-4 w-4" />;
      case 'MEETING': return <Users className="h-4 w-4" />;
      case 'FOLLOW_UP': return <Target className="h-4 w-4" />;
      case 'QUOTE': return <DollarSign className="h-4 w-4" />;
      case 'DOCUMENT': return <FileText className="h-4 w-4" />;
      default: return <CheckSquare className="h-4 w-4" />;
    }
  };

  const getReminderTypeIcon = (type: string) => {
    switch (type) {
      case 'TASK_DUE': return <Clock className="h-4 w-4" />;
      case 'FOLLOW_UP': return <Target className="h-4 w-4" />;
      case 'RENEWAL': return <Calendar className="h-4 w-4" />;
      case 'PAYMENT': return <DollarSign className="h-4 w-4" />;
      case 'MEETING': return <Users className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const isTaskOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const isReminderDue = (triggerDate: string) => {
    return new Date(triggerDate) <= new Date();
  };

  const taskTypes = [
    { value: 'CALL', label: 'Appel', icon: Phone },
    { value: 'EMAIL', label: 'Email', icon: Mail },
    { value: 'MEETING', label: 'Rendez-vous', icon: Users },
    { value: 'FOLLOW_UP', label: 'Suivi', icon: Target },
    { value: 'QUOTE', label: 'Devis', icon: DollarSign },
    { value: 'DOCUMENT', label: 'Document', icon: FileText },
    { value: 'OTHER', label: 'Autre', icon: CheckSquare }
  ];

  const taskStatuses = [
    { value: 'PENDING', label: 'En attente', color: 'yellow' },
    { value: 'IN_PROGRESS', label: 'En cours', color: 'blue' },
    { value: 'COMPLETED', label: 'Terminé', color: 'green' },
    { value: 'CANCELLED', label: 'Annulé', color: 'gray' },
    { value: 'OVERDUE', label: 'En retard', color: 'red' }
  ];

  const priorities = [
    { value: 'LOW', label: 'Faible', color: 'green' },
    { value: 'MEDIUM', label: 'Moyenne', color: 'yellow' },
    { value: 'HIGH', label: 'Élevée', color: 'orange' },
    { value: 'URGENT', label: 'Urgente', color: 'red' }
  ];

  const tabs = [
    { 
      id: 'tasks', 
      name: 'Tâches', 
      icon: CheckSquare, 
      count: tasks.length,
      description: 'Gérez vos tâches et activités'
    },
    { 
      id: 'reminders', 
      name: 'Rappels', 
      icon: Bell, 
      count: reminders.filter(r => r.status === 'ACTIVE').length,
      description: 'Configurez vos rappels automatiques'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <CheckSquare className="h-8 w-8 mr-3 text-blue-600" />
            Tâches & Rappels
          </h1>
          <p className="text-gray-600 mt-2">Organisez votre travail et ne manquez aucune échéance</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => activeTab === 'tasks' ? setShowNewTaskModal(true) : setShowNewReminderModal(true)}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            {activeTab === 'tasks' ? 'Nouvelle Tâche' : 'Nouveau Rappel'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.name}</span>
                {tab.count > 0 && (
                  <span className="bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-800 text-sm">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {activeTab === 'tasks' ? (
          <>
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Tâches</p>
                  <p className="text-2xl font-bold text-gray-900">{tasks.length}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <CheckSquare className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+15%</span>
                <span className="text-gray-500 ml-1">cette semaine</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Cours</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {tasks.filter(t => ['PENDING', 'IN_PROGRESS'].includes(t.status)).length}
                  </p>
                </div>
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Activity className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-blue-600">Actives</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Terminées</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {tasks.filter(t => t.status === 'COMPLETED').length}
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+8%</span>
                <span className="text-gray-500 ml-1">cette semaine</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">En Retard</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {tasks.filter(t => isTaskOverdue(t.dueDate) && !['COMPLETED', 'CANCELLED'].includes(t.status)).length}
                  </p>
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Flag className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">Attention requise</span>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Rappels</p>
                  <p className="text-2xl font-bold text-gray-900">{reminders.length}</p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Bell className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+5%</span>
                <span className="text-gray-500 ml-1">ce mois</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Actifs</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => r.status === 'ACTIVE').length}
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Activity className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">Programmés</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Déclenchés</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => isReminderDue(r.triggerDate) && r.status === 'ACTIVE').length}
                  </p>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <AlertCircle className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <Bell className="h-4 w-4 text-orange-500 mr-1" />
                <span className="text-orange-600">Nécessitent attention</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Récurrents</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => r.isRecurring).length}
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <BarChart3 className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-blue-600">Automatiques</span>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export { TasksReminders as default };
