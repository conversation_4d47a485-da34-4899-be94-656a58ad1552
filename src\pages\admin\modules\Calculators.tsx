import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calculator,
  Car,
  Home as HomeIcon,
  Heart,
  Shield,
  PiggyBank,
  Building,
  Settings,
  Play,
  BarChart3,
  TrendingUp,
  DollarSign,
  X,
  CheckCircle,
  AlertCircle,
  Save
} from 'lucide-react';
 // Ligne corrigée automatiquement
import { Toast  } from '../../../components/common/Toast';

interface VehicleData {
  brand: string;
  model: string;
  year: number;
  power: number;
  value?: number;
}

interface DriverData {
  age: number;
  experience: number;
  location?: string;
  profession?: string;
}

interface AutoCalculatorData {
  vehicleData: VehicleData;
  driverData: DriverData;
}

// Fonction utilitaire pour valider les données du calculateur auto
function validateAutoCalculator(data: AutoCalculatorData): string[] {
  const errors: string[] = [];
  
  if (!data.vehicleData.brand || data.vehicleData.brand.trim().length === 0) {
    errors.push('La marque du véhicule est requise');
  }
  
  if (!data.vehicleData.model || data.vehicleData.model.trim().length === 0) {
    errors.push('Le modèle du véhicule est requis');
  }
  
  if (!data.vehicleData.year || data.vehicleData.year < 1900 || data.vehicleData.year > new Date().getFullYear()) {
    errors.push('L\'année du véhicule doit être valide');
  }
  
  if (!data.vehicleData.power || data.vehicleData.power <= 0) {
    errors.push('La puissance du véhicule doit être supérieure à 0');
  }
  
  if (!data.driverData.age || data.driverData.age < 18 || data.driverData.age > 100) {
    errors.push('L\'âge du conducteur doit être entre 18 et 100 ans');
  }
  
  if (!data.driverData.experience || data.driverData.experience < 0) {
    errors.push('L\'expérience de conduite ne peut pas être négative');
  }
  
  return errors;
}

// Fonction utilitaire pour valider les données du calculateur habitation
function validateHabitationCalculator(data) {
  const errors = [];
  
  if (!data.propertyData.type || data.propertyData.type.trim().length === 0) {
    errors.push('Le type de bien est requis');
  }
  
  if (!data.propertyData.surface || data.propertyData.surface <= 0) {
    errors.push('La surface du bien doit être supérieure à 0');
  }
  
  if (!data.propertyData.rooms || data.propertyData.rooms <= 0) {
    errors.push('Le nombre de pièces doit être supérieur à 0');
  }
  
  if (!data.propertyData.value || data.propertyData.value <= 0) {
    errors.push('La valeur du bien doit être supérieure à 0');
  }
  
  if (!data.propertyData.location || data.propertyData.location.trim().length === 0) {
    errors.push('La localisation du bien est requise');
  }
  
  return errors;
}

export const Calculators: React.FC = () => {
  const [selectedCalculator, setSelectedCalculator] = useState('auto');
  const [toast, setToast] = useState(null);
  
  // Chargement sécurisé des données des calculateurs depuis localStorage
  const [autoCalculatorData, setAutoCalculatorData] = useState(() => {
    try {
      const saved = localStorage.getItem('autoCalculatorData');
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed && parsed.vehicleData && parsed.driverData) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données auto:', error);
    }
    return {
      vehicleData: {
        brand: 'Renault',
        model: 'Clio',
        year: 2020,
        power: 7,
        value: 180000
      },
      driverData: {
        age: 28,
        experience: 5,
        bonus: 0.5,
        city: 'Casablanca'
      },
      coverage: {
        liability: true,
        collision: true,
        comprehensive: true,
        glass: true,
        assistance: true
      },
      result: {
        basePremium: 2400,
        discounts: 360,
        finalPremium: 2040,
        breakdown: [
          { item: 'Responsabilité civile', amount: 800 },
          { item: 'Dommages collision', amount: 600 },
          { item: 'Vol/Incendie', amount: 400 },
          { item: 'Bris de glace', amount: 120 },
          { item: 'Assistance', amount: 120 }
        ]
      }
    };
  });

  const [habitationCalculatorData, setHabitationCalculatorData] = useState(() => {
    try {
      const saved = localStorage.getItem('habitationCalculatorData');
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed && parsed.propertyData) {
          return parsed;
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données habitation:', error);
    }
    return {
      propertyData: {
        type: 'Appartement',
        surface: 120,
        rooms: 4,
        value: 800000,
        location: 'Casablanca'
      },
      coverage: {
        fire: true,
        water: true,
        theft: true,
        liability: true,
        glass: true
      },
      result: {
        basePremium: 1800,
        discounts: 180,
        finalPremium: 1620,
        breakdown: [
          { item: 'Incendie/Explosion', amount: 600 },
          { item: 'Dégâts des eaux', amount: 400 },
          { item: 'Vol/Vandalisme', amount: 350 },
          { item: 'RC vie privée', amount: 150 },
          { item: 'Bris de glace', amount: 120 }
        ]
      }
    };
  });

  // Fonction pour afficher une notification
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // Sauvegarde sécurisée des données auto
  useEffect(() => {
    try {
      localStorage.setItem('autoCalculatorData', JSON.stringify(autoCalculatorData));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données auto:', error);
      showToast('Erreur lors de la sauvegarde des données auto', 'error');
    }
  }, [autoCalculatorData]);

  // Sauvegarde sécurisée des données habitation
  useEffect(() => {
    try {
      localStorage.setItem('habitationCalculatorData', JSON.stringify(habitationCalculatorData));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données habitation:', error);
      showToast('Erreur lors de la sauvegarde des données habitation', 'error');
    }
  }, [habitationCalculatorData]);

  const calculators = [
    {
      id: 'auto',
      name: 'Simulateur Auto',
      icon: Car,
      description: 'Calcul automatique des primes d\'assurance automobile',
      color: 'bg-blue-500'
    },
    {
      id: 'habitation',
      name: 'Calculateur Habitation',
      icon: HomeIcon,
      description: 'Estimation des primes d\'assurance habitation',
      color: 'bg-green-500'
    },
    {
      id: 'sante',
      name: 'Estimateur Santé',
      icon: Heart,
      description: 'Calcul des cotisations d\'assurance santé',
      color: 'bg-red-500'
    },
    {
      id: 'prevoyance',
      name: 'Besoins Prévoyance',
      icon: Shield,
      description: 'Évaluation des besoins en prévoyance',
      color: 'bg-purple-500'
    },
    {
      id: 'epargne',
      name: 'Simulateur Épargne',
      icon: PiggyBank,
      description: 'Projection d\'épargne retraite',
      color: 'bg-yellow-500'
    },
    {
      id: 'pro',
      name: 'Assurance Pro',
      icon: Building,
      description: 'Calcul pour assurances professionnelles',
      color: 'bg-indigo-500'
    }
  ];

  const handleCalculateAuto = () => {
    try {
      const errors = validateAutoCalculator(autoCalculatorData);
      if (errors.length > 0) {
        showToast(errors.join(', '), 'error');
        return;
      }

      // Simulation du calcul
      const basePremium = 2400;
      const discounts = 360;
      const finalPremium = basePremium - discounts;
      
      const newResult = {
        basePremium,
        discounts,
        finalPremium,
        breakdown: [
          { item: 'Responsabilité civile', amount: 800 },
          { item: 'Dommages collision', amount: 600 },
          { item: 'Vol/Incendie', amount: 400 },
          { item: 'Bris de glace', amount: 120 },
          { item: 'Assistance', amount: 120 }
        ]
      };

      setAutoCalculatorData(prev => ({
        ...prev,
        result: newResult
      }));
      
      showToast('Calcul auto effectué avec succès !');
    } catch (error) {
      console.error('Erreur lors du calcul auto:', error);
      showToast('Erreur lors du calcul auto', 'error');
    }
  };

  const handleCalculateHabitation = () => {
    try {
      const errors = validateHabitationCalculator(habitationCalculatorData);
      if (errors.length > 0) {
        showToast(errors.join(', '), 'error');
        return;
      }

      // Simulation du calcul
      const basePremium = 1800;
      const discounts = 180;
      const finalPremium = basePremium - discounts;
      
      const newResult = {
        basePremium,
        discounts,
        finalPremium,
        breakdown: [
          { item: 'Incendie/Explosion', amount: 600 },
          { item: 'Dégâts des eaux', amount: 400 },
          { item: 'Vol/Vandalisme', amount: 350 },
          { item: 'RC vie privée', amount: 150 },
          { item: 'Bris de glace', amount: 120 }
        ]
      };

      setHabitationCalculatorData(prev => ({
        ...prev,
        result: newResult
      }));
      
      showToast('Calcul habitation effectué avec succès !');
    } catch (error) {
      console.error('Erreur lors du calcul habitation:', error);
      showToast('Erreur lors du calcul habitation', 'error');
    }
  };

  const handleSaveCalculator = (type) => {
    try {
      if (type === 'auto') {
        const errors = validateAutoCalculator(autoCalculatorData);
        if (errors.length > 0) {
          showToast(errors.join(', '), 'error');
          return;
        }
        showToast('Configuration auto sauvegardée avec succès !');
      } else if (type === 'habitation') {
        const errors = validateHabitationCalculator(habitationCalculatorData);
        if (errors.length > 0) {
          showToast(errors.join(', '), 'error');
          return;
        }
        showToast('Configuration habitation sauvegardée avec succès !');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      showToast('Erreur lors de la sauvegarde', 'error');
    }
  };

  const getCurrentCalculatorData = () => {
    switch (selectedCalculator) {
      case 'auto':
        return autoCalculatorData;
      case 'habitation':
        return habitationCalculatorData;
      default:
        return autoCalculatorData;
    }
  };

  const handleUpdateAutoData = (field, value) => {
    setAutoCalculatorData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleUpdateHabitationData = (field, value) => {
    setHabitationCalculatorData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleUpdateVehicleData = (field, value) => {
    setAutoCalculatorData(prev => ({
      ...prev,
      vehicleData: {
        ...prev.vehicleData,
        [field]: value
      }
    }));
  };

  const handleUpdateDriverData = (field, value) => {
    setAutoCalculatorData(prev => ({
      ...prev,
      driverData: {
        ...prev.driverData,
        [field]: value
      }
    }));
  };

  const handleUpdatePropertyData = (field, value) => {
    setHabitationCalculatorData(prev => ({
      ...prev,
      propertyData: {
        ...prev.propertyData,
        [field]: value
      }
    }));
  };

  const renderAutoCalculator = () => (
    <div className="space-y-8">
      {/* Input Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Données du véhicule</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Marque</label>
                <select 
                  value={autoCalculatorData.vehicleData.brand}
                  onChange={(e) => handleUpdateVehicleData('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="Renault">Renault</option>
                  <option value="Peugeot">Peugeot</option>
                  <option value="Citroën">Citroën</option>
                  <option value="Dacia">Dacia</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Modèle</label>
                <input
                  type="text"
                  value={autoCalculatorData.vehicleData.model}
                  onChange={(e) => handleUpdateVehicleData('model', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Année</label>
                <input
                  type="number"
                  value={autoCalculatorData.vehicleData.year}
                  onChange={(e) => handleUpdateVehicleData('year', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Puissance (CV)</label>
                <input
                  type="number"
                  value={autoCalculatorData.vehicleData.power}
                  onChange={(e) => handleUpdateVehicleData('power', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Profil conducteur</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Âge</label>
                <input
                  type="number"
                  value={autoCalculatorData.driverData.age}
                  onChange={(e) => handleUpdateDriverData('age', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Expérience</label>
                <input
                  type="number"
                  value={autoCalculatorData.driverData.experience}
                  onChange={(e) => handleUpdateDriverData('experience', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Bonus/Malus</label>
                <input
                  type="number"
                  step="0.1"
                  value={autoCalculatorData.driverData.bonus}
                  onChange={(e) => handleUpdateDriverData('bonus', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Ville</label>
                <select 
                  value={autoCalculatorData.driverData.city}
                  onChange={(e) => handleUpdateDriverData('city', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="Casablanca">Casablanca</option>
                  <option value="Rabat">Rabat</option>
                  <option value="Marrakech">Marrakech</option>
                  <option value="Fès">Fès</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-6">
          <div className="bg-blue-50 rounded-lg p-6">
            <h4 className="font-semibold text-blue-900 mb-4">Résultat du calcul</h4>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-blue-700">Prime de base:</span>
                <span className="font-semibold text-blue-900">{autoCalculatorData.result.basePremium.toLocaleString()} DH</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-blue-700">Remises:</span>
                <span className="font-semibold text-green-600">-{autoCalculatorData.result.discounts.toLocaleString()} DH</span>
              </div>
              <div className="border-t border-blue-200 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-blue-900">Prime finale:</span>
                  <span className="text-2xl font-bold text-blue-900">{autoCalculatorData.result.finalPremium.toLocaleString()} DH</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Détail des garanties</h4>
            <div className="space-y-3">
              {autoCalculatorData.result.breakdown.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-gray-700">{item.item}:</span>
                  <span className="font-medium text-gray-900">{item.amount.toLocaleString()} DH</span>
                </div>
              ))}
            </div>
          </div>

          <button className="w-full bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-800 transition-colors font-semibold">
            Générer le devis
          </button>
        </div>
      </div>
    </div>
  );

  const renderHabitationCalculator = () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Données du logement</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                <select 
                  value={habitationCalculatorData.propertyData.type}
                  onChange={(e) => handleUpdatePropertyData('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="Appartement">Appartement</option>
                  <option value="Maison">Maison</option>
                  <option value="Villa">Villa</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Surface (m²)</label>
                <input
                  type="number"
                  value={habitationCalculatorData.propertyData.surface}
                  onChange={(e) => handleUpdatePropertyData('surface', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nombre de pièces</label>
                <input
                  type="number"
                  value={habitationCalculatorData.propertyData.rooms}
                  onChange={(e) => handleUpdatePropertyData('rooms', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Valeur (DH)</label>
                <input
                  type="number"
                  value={habitationCalculatorData.propertyData.value}
                  onChange={(e) => handleUpdatePropertyData('value', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-green-50 rounded-lg p-6">
            <h4 className="font-semibold text-green-900 mb-4">Résultat du calcul</h4>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-green-700">Prime de base:</span>
                <span className="font-semibold text-green-900">{habitationCalculatorData.result.basePremium.toLocaleString()} DH</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-green-700">Remises:</span>
                <span className="font-semibold text-green-600">-{habitationCalculatorData.result.discounts.toLocaleString()} DH</span>
              </div>
              <div className="border-t border-green-200 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-green-900">Prime finale:</span>
                  <span className="text-2xl font-bold text-green-900">{habitationCalculatorData.result.finalPremium.toLocaleString()} DH</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Détail des garanties</h4>
            <div className="space-y-3">
              {habitationCalculatorData.result.breakdown.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-gray-700">{item.item}:</span>
                  <span className="font-medium text-gray-900">{item.amount.toLocaleString()} DH</span>
                </div>
              ))}
            </div>
          </div>

          <button className="w-full bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-800 transition-colors font-semibold">
            Générer le devis
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Calculateurs Automatiques</h1>
          <p className="text-gray-600 mt-2">Outils de calcul et simulation pour tous vos produits d'assurance</p>
        </div>
        <div className="flex space-x-4">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Settings className="h-4 w-4 mr-2" />
            Configuration
          </button>
        </div>
      </motion.div>

      {/* Calculator Selection */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-6">Sélectionnez un calculateur</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {calculators.map((calc) => (
            <button
              key={calc.id}
              onClick={() => setSelectedCalculator(calc.id)}
              className={`p-6 rounded-lg border-2 transition-all duration-300 text-left ${
                selectedCalculator === calc.id
                  ? 'border-axa-blue bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className={`${calc.color} w-12 h-12 rounded-lg flex items-center justify-center mb-4`}>
                <calc.icon className="h-6 w-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">{calc.name}</h4>
              <p className="text-sm text-gray-600">{calc.description}</p>
            </button>
          ))}
        </div>
      </motion.div>

      {/* Selected Calculator */}
      <motion.div
        key={selectedCalculator}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900">
            {calculators.find(c => c.id === selectedCalculator)?.name}
          </h3>
          <div className="flex space-x-4">
            <button 
              onClick={() => handleSaveCalculator(selectedCalculator)}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder
            </button>
            <button 
              onClick={selectedCalculator === 'auto' ? handleCalculateAuto : handleCalculateHabitation}
              className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
            >
              <Play className="h-4 w-4 mr-2" />
              Recalculer
            </button>
          </div>
        </div>

        {selectedCalculator === 'auto' && renderAutoCalculator()}
        {selectedCalculator === 'habitation' && renderHabitationCalculator()}
        
        {!['auto', 'habitation'].includes(selectedCalculator) && (
          <div className="text-center py-12">
            <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Calculateur en développement
            </h4>
            <p className="text-gray-600">
              Ce calculateur sera bientôt disponible avec toutes les fonctionnalités avancées.
            </p>
          </div>
        )}
      </motion.div>

      {/* Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Calculs ce mois</p>
              <p className="text-2xl font-bold text-gray-900">1,247</p>
              <p className="text-sm text-green-500">+18% vs mois dernier</p>
            </div>
            <BarChart3 className="h-12 w-12 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Taux de conversion</p>
              <p className="text-2xl font-bold text-gray-900">34.2%</p>
              <p className="text-sm text-green-500">+3.1% vs mois dernier</p>
            </div>
            <TrendingUp className="h-12 w-12 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">CA généré</p>
              <p className="text-2xl font-bold text-gray-900">485K DH</p>
              <p className="text-sm text-green-500">+22% vs mois dernier</p>
            </div>
            <DollarSign className="h-12 w-12 text-purple-500" />
          </div>
        </div>
      </motion.div>

      {/* Notification Toast */}
      {toast && (
        <AnimatePresence>
          <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />
        </AnimatePresence>
      )}
    </div>
  );
};
export { Calculators as default };