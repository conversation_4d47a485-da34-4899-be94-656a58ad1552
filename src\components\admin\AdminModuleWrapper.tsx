import React, { Suspense, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { AdminErrorBoundary } from './AdminErrorBoundary';
import { SecureAdminRoute } from './SecureAdminRoute';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useModuleTransition } from '../../hooks/useModuleTransition';
import type { AdminModule, AdminPermission } from '../../types/admin';

interface ModuleWrapperProps {
  module: AdminModule;
  children: React.ReactNode;
  requiredPermissions?: AdminPermission[];
  onError?: (error: Error) => void;
}

const loadingAnimation = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

export const AdminModuleWrapper: React.FC<ModuleWrapperProps> = ({
  module,
  children,
  requiredPermissions,
  onError
}) => {
  const [hasError, setHasError] = useState(false);
  const { isLoading, error: permissionError } = useAdminPermissions(module, requiredPermissions);
  const { isTransitioning, error: transitionError } = useModuleTransition({
    onBeforeTransition: async (from, to) => {
      // Vérifier s'il y a des changements non sauvegardés
      const unsavedChanges = window.__ADMIN_MODULE_STATE__?.[from]?.unsavedChanges;
      if (unsavedChanges) {
        return window.confirm('Des modifications non sauvegardées seront perdues. Continuer ?');
      }
      return true;
    }
  });

  // Gérer les erreurs globales du module
  useEffect(() => {
    if (hasError && onError) {
      onError(new Error(`Erreur dans le module ${module}`));
    }
  }, [hasError, module, onError]);

  // Réinitialiser l'état d'erreur lors du changement de module
  useEffect(() => {
    setHasError(false);
  }, [module]);

  if (isLoading || isTransitioning) {
    return (
      <motion.div
        className="flex items-center justify-center h-screen"
        {...loadingAnimation}
      >
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
          <p className="mt-4 text-gray-600">Chargement du module...</p>
        </div>
      </motion.div>
    );
  }

  if (permissionError || transitionError) {
    return (
      <motion.div
        className="rounded-lg bg-red-50 p-4 m-4"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Erreur de chargement
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{permissionError || transitionError}</p>
            </div>
            <div className="mt-4">
              <button
                type="button"
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Recharger le module
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <SecureAdminRoute module={module} requiredPermissions={requiredPermissions}>
      <AdminErrorBoundary
        moduleName={module}
        onError={() => setHasError(true)}
      >
        <Suspense
          fallback={
            <motion.div
              className="flex items-center justify-center h-screen"
              {...loadingAnimation}
            >
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
            </motion.div>
          }
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={module}
              initial="initial"
              animate="animate"
              exit="exit"
              variants={loadingAnimation}
              className="min-h-screen bg-gray-50"
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </Suspense>
      </AdminErrorBoundary>
    </SecureAdminRoute>
  );
};

export default AdminModuleWrapper;
