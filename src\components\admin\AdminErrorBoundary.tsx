import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, MessageSquare } from 'lucide-react';

interface Props {
  children: ReactNode;
  moduleName?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class AdminErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Erreur dans le module admin:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });

    // Envoi de l'erreur au service de monitoring
    this.logError(error, errorInfo);
  }

  private logError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      await fetch('/api/admin/log-error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          error: error.toString(),
          componentStack: errorInfo.componentStack,
          module: this.props.moduleName,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      });
    } catch (e) {
      console.error('Échec de l\'envoi du rapport d\'erreur:', e);
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleSupportContact = () => {
    window.location.href = '/contact?type=support&priority=high';
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-white px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
          <div className="max-w-max mx-auto">
            <main className="sm:flex">
              <div className="sm:ml-6">
                <div className="sm:border-l sm:border-gray-200 sm:pl-6">
                  <h1 className="text-4xl font-extrabold text-red-600 tracking-tight sm:text-5xl flex items-center">
                    <AlertTriangle className="h-12 w-12 mr-4" />
                    Erreur Module {this.props.moduleName}
                  </h1>
                  <p className="mt-1 text-base text-gray-500">
                    Une erreur est survenue dans ce module. Nos équipes techniques ont été notifiées.
                  </p>
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm font-mono text-gray-700 whitespace-pre-wrap">
                      {this.state.error?.message}
                    </p>
                  </div>
                </div>
                <div className="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
                  <button
                    onClick={this.handleReload}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Recharger le module
                  </button>
                  <button
                    onClick={this.handleSupportContact}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-axa-blue bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Contacter le support
                  </button>
                </div>
                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <details className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <summary className="text-sm font-medium text-gray-700 cursor-pointer">
                      Détails techniques (DEV)
                    </summary>
                    <pre className="mt-2 text-xs text-gray-500 overflow-auto">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            </main>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export const AdminModuleWrapper: React.FC<Props> = ({ children, moduleName }) => {
  return (
    <AdminErrorBoundary moduleName={moduleName}>
      {children}
    </AdminErrorBoundary>
  );
};

export default AdminErrorBoundary;
