// Types pour les modules admin

export type AdminModule = 
  | 'LEADS' 
  | 'QUOTES' 
  | 'CLIENTS' 
  | 'ANALYTICS' 
  | 'CONTENT' 
  | 'SETTINGS'
  | 'DIGITAL_MARKETING'
  | 'E_LEARNING'
  | 'INTEGRATIONS'
  | 'AUTOMATION'
  | 'TEMPLATES';
export type AdminPermission = 'READ' | 'WRITE' | 'DELETE' | 'MANAGE';

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'MANAGER' | 'USER';
  permissions: {
    [key in AdminModule]?: AdminPermission[];
  };
  lastLogin: string;
}

export interface DigitalCampaign {
  id: string;
  name: string;
  type: 'EMAIL' | 'SOCIAL' | 'ADS' | 'SMS';
  status: 'DRAFT' | 'SCHEDULED' | 'RUNNING' | 'COMPLETED' | 'PAUSED';
  startDate: string;
  endDate?: string;
  budget?: number;
  targetAudience: {
    age?: [number, number];
    location?: string[];
    interests?: string[];
    customSegments?: string[];
  };
  performance?: {
    reach: number;
    engagement: number;
    conversions: number;
    roi: number;
  };
  content: {
    title: string;
    description: string;
    mediaUrls: string[];
    callToAction?: {
      text: string;
      url: string;
    };
  };
  automationRules?: {
    trigger: string;
    action: string;
    conditions: Record<string, any>;
  }[];
}

export interface QuoteTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  sections: {
    title: string;
    content: string;
    variables: string[];
    optional: boolean;
  }[];
  pricing: {
    base: number;
    variables: {
      name: string;
      type: 'MULTIPLIER' | 'FIXED' | 'PERCENTAGE';
      value: number;
    }[];
  };
  validity: number; // Durée de validité en jours
  terms: string[];
  requiredFields: string[];
  customization: {
    colors: string[];
    logo: boolean;
    signature: boolean;
  };
}

export interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  duration: number; // En minutes
  modules: {
    id: string;
    title: string;
    description: string;
    content: {
      type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'EXERCISE';
      data: any;
      duration: number;
    }[];
    requirements?: string[];
  }[];
  progress?: {
    completed: number;
    score: number;
    lastAccess: string;
  };
  certification?: {
    available: boolean;
    requirements: {
      minScore: number;
      requiredModules: string[];
    };
  };
}

export interface ExternalIntegration {
  id: string;
  name: string;
  type: 'CRM' | 'ERP' | 'PAYMENT' | 'MARKETING' | 'ANALYTICS';
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  config: {
    apiKey?: string;
    endpoint: string;
    webhooks?: string[];
    settings: Record<string, any>;
  };
  syncStatus: {
    lastSync: string;
    errors: string[];
    pendingTasks: number;
  };
  permissions: string[];
  dataMapping: {
    source: string;
    target: string;
    transformation?: string;
  }[];
}

export interface Lead {
  id: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone: string;
  product: string;
  source: string;
  city?: string;
  message?: string;
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'CONVERTED' | 'LOST';
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  score: number;
  nextFollowUpDate?: string;
  estimatedValue: number;
  createdAt: string;
  updatedAt: string;
  interactions: Array<{
    id: string;
    type: 'EMAIL' | 'CALL' | 'MEETING' | 'OTHER';
    date: string;
    notes?: string;
  }>;
  interestedProducts: string[];
  [key: string]: any; // Pour les champs dynamiques
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: 'INDIVIDUAL' | 'BUSINESS';
  city: string;
  address?: string;
  dateOfBirth?: string;
  profession?: string;
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  quotes?: Quote[];
  contracts?: Contract[];
  claims?: Claim[];
}

export interface Quote {
  id: string;
  clientId: string;
  client: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  product: string;
  amount: number;
  status: 'DRAFT' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED';
  validUntil: string;
  details?: any;
  createdBy: string;
  createdUser?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Contract {
  id: string;
  clientId: string;
  client?: {
    id: string;
    name: string;
    email: string;
  };
  product: string;
  premium: number;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED';
  startDate: string;
  endDate: string;
  details?: any;
  createdAt: string;
  updatedAt: string;
}

export interface Claim {
  id: string;
  clientId: string;
  client: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  contractId?: string;
  contract?: {
    id: string;
    product: string;
    premium: number;
  };
  type: string;
  description: string;
  amount?: number;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'PAID';
  documents?: any;
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Quote {
  id: number;
  clientName: string;
  product: string;
  amount: string;
  status: 'Brouillon' | 'Envoyé' | 'Accepté' | 'Refusé' | 'Expiré';
  date: string;
  expiryDate: string;
  assignedTo: string;
}

export interface ClaimLegacy {
  id: number;
  clientName: string;
  contractType: string;
  claimType: string;
  amount: string;
  status: 'Nouveau' | 'En cours' | 'Enquête' | 'Approuvé' | 'Refusé' | 'Clôturé';
  date: string;
  assignedTo: string;
  description?: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'ADMIN' | 'USER';
  lastLogin: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SecurityActivity {
  id: number;
  user: string;
  action: string;
  ip: string;
  location: string;
  time: string;
  status: 'success' | 'failed' | 'warning';
  device: string;
}

export interface Backup {
  id: number;
  type: 'Complète' | 'Incrémentale';
  date: string;
  size: string;
  status: 'success' | 'failed' | 'in_progress';
  location: string;
  retention: string;
}

export interface ComplianceItem {
  id: number;
  requirement: string;
  status: 'compliant' | 'non_compliant' | 'pending';
  description: string;
  lastCheck: string;
}

export interface Toast {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
}

export interface Template {
  id?: number;
  name: string;
  description: string;
  category: string;
  type: string;
  icon: string;
  content?: string;
  variables?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'info';
  onClose: () => void;
}

export interface Integration {
  id: number;
  name: string;
  type: string;
  endpoint?: string;
  status: 'active' | 'inactive' | 'error' | 'syncing' | 'connected' | 'disconnected';
  description?: string;
  lastSync?: string;
  category?: string;
  icon?: any;
  color?: string;
  features?: string[];
  apiVersion?: string;
  uptime?: string;
}

export interface AppSettings {
  general: {
    companyName: string;
    companyEmail: string;
    companyPhone: string;
    companyAddress: string;
    timezone: string;
    language: string;
    currency: string;
    dateFormat: string;
    timeFormat: string;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    quoteAlerts: boolean;
    paymentAlerts: boolean;
    securityAlerts: boolean;
    marketingEmails: boolean;
    dailyReports: boolean;
    weeklyReports: boolean;
  };
  security: {
    twoFactorAuth: boolean;
    sessionTimeout: number;
    passwordMinLength: number;
    passwordRequireSpecial: boolean;
    passwordRequireNumbers: boolean;
    passwordRequireUppercase: boolean;
    maxLoginAttempts: number;
    lockoutDuration: number;
    autoLogout: boolean;
  };
  appearance: {
    theme: 'light' | 'dark';
    primaryColor: string;
    sidebarCollapsed: boolean;
    compactMode: boolean;
    showAnimations: boolean;
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
  };
  integrations: {
    smsGateway: string;
    emailProvider: string;
    paymentGateway: string;
    analytics: string;
    backupProvider: string;
  };
  backup: {
    autoBackup: boolean;
    backupFrequency: string;
    backupTime: string;
    retentionDays: number;
    includeFiles: boolean;
    includeDatabase: boolean;
    compression: boolean;
    encryption: boolean;
  };
}

export interface ContentPage {
  id: number;
  title: string;
  slug: string;
  status: 'published' | 'draft';
  lastModified: string;
  author: string;
  views: number;
  template: string;
  sections: string[];
  content: any;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  image?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  tags?: string[];
  category?: string;
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ContentPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface MediaFile {
  id: string;
  name: string;
  originalName: string;
  url: string;
  type: 'image' | 'video' | 'document';
  size: number;
  mimeType: string;
  createdAt: string;
  updatedAt: string;
}

export interface Banner {
  id: number;
  name: string;
  text: string;
  type: 'promotion' | 'info' | 'alert' | 'success';
  status: 'active' | 'inactive' | 'scheduled' | 'expired';
  backgroundColor: string;
  textColor: string;
  startDate: string;
  endDate: string;
  showContact: boolean;
  views: number;
  clicks: number;
}

export interface AdminResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

// ===== TYPES ADDITIONNELS POUR FONCTIONNALITÉS COMPLÈTES =====

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'agent' | 'viewer';
  avatar?: string;
  permissions: string[];
  lastLogin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdminStats {
  totalUsers: number;
  totalClients: number;
  totalLeads: number;
  totalPolicies: number;
  totalClaims: number;
  monthlyRevenue: number;
  conversionRate: number;
  activeUsers: number;
}

export interface Activity {
  id: string;
  type: 'call' | 'email' | 'meeting' | 'note' | 'task';
  subject: string;
  description: string;
  date: string;
  duration?: number;
  outcome?: string;
  nextAction?: string;
  createdBy: string;
}

export interface ClaimEvent {
  id: string;
  date: string;
  type: 'created' | 'updated' | 'document_added' | 'status_changed' | 'payment_made';
  description: string;
  user: string;
  details?: any;
}

export interface Witness {
  id: string;
  name: string;
  phone: string;
  email?: string;
  statement: string;
}

export interface ClaimExpense {
  id: string;
  category: string;
  description: string;
  amount: number;
  date: string;
  receipt?: string;
  approved: boolean;
}

export interface Document {
  id: string;
  name: string;
  type: 'contract' | 'claim' | 'policy' | 'report' | 'other';
  category: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
  tags: string[];
  isPublic: boolean;
  version: number;
  description?: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  type: 'call' | 'email' | 'meeting' | 'follow_up' | 'review' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  assignedTo: string;
  assignedBy: string;
  dueDate: string;
  completedAt?: string;
  relatedTo?: {
    type: 'client' | 'lead' | 'claim' | 'quote';
    id: string;
  };
  reminders: Reminder[];
  createdAt: string;
  updatedAt: string;
}

export interface Reminder {
  id: string;
  type: 'email' | 'notification' | 'sms';
  time: string;
  sent: boolean;
}

export interface Calculator {
  id: string;
  name: string;
  type: 'auto' | 'home' | 'life' | 'health' | 'business';
  description: string;
  fields: CalculatorField[];
  formula: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CalculatorField {
  id: string;
  name: string;
  label: string;
  type: 'number' | 'select' | 'checkbox' | 'text';
  required: boolean;
  options?: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'social' | 'display' | 'search';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  budget: number;
  spent: number;
  startDate: string;
  endDate: string;
  targetAudience: string[];
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    ctr: number;
    cpc: number;
    roas: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // en minutes
  instructor: string;
  thumbnail: string;
  status: 'draft' | 'published' | 'archived';
  modules: CourseModule[];
  enrollments: number;
  rating: number;
  price: number;
  createdAt: string;
  updatedAt: string;
}

export interface CourseModule {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  duration: number;
}

export interface Lesson {
  id: string;
  title: string;
  type: 'video' | 'text' | 'quiz' | 'assignment';
  content: string;
  duration: number;
  order: number;
  isCompleted?: boolean;
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'number' | 'select' | 'checkbox' | 'radio' | 'textarea' | 'date';
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
  order: number;
}

export interface QuoteForm {
  id: string;
  name: string;
  description: string;
  productType: string;
  fields: FormField[];
  isActive: boolean;
  submissions: number;
  conversionRate: number;
  createdAt: string;
  updatedAt: string;
}

// Types pour les validations
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Types pour les API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Types pour les filtres et recherche
export interface FilterOptions {
  search?: string;
  status?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
  assignedTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Types pour les permissions
export interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  actions: string[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}