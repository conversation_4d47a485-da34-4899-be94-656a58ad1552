/**
 * Service API unifié pour communiquer avec le backend
 */

import {
  mockLeads,
  mockClients,
  mockQuotes,
  mockBanners,
  mockBlogPosts,
  mockContentPages,
  mockMediaFiles,
  mockDashboardStats,
  mockClaims,
  mockInsuranceDashboard,
  mockInsuranceAnalytics,
  mockComplianceACAPS
} from './mockData';

// Types pour les réponses API
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: any;
}

// Mode mock pour les tests sans backend
const USE_MOCK_DATA = true; // Mode mock activé pour affichage complet sans backend

export class ApiService {
  private token: string | null = null;
  private isServerAvailable: boolean = false;

  constructor(private baseURL: string) {
    // Initialiser le token depuis le localStorage
    this.token = localStorage.getItem('auth_token');

    // Vérifier la disponibilité du serveur
    this.checkServerHealth();
  }

  private async checkServerHealth() {
    try {
      const response = await fetch(`${this.baseURL}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      this.isServerAvailable = response.ok;
      console.log(`🌐 API Server: ${this.isServerAvailable ? 'Connecté ✅' : 'Déconnecté ❌'}`);
    } catch (error) {
      this.isServerAvailable = false;
      console.log('⚠️ Serveur API non disponible, utilisation du mode mock');
    }
  }

  setToken(newToken: string | null) {
    this.token = newToken;
    if (newToken) {
      localStorage.setItem('auth_token', newToken);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Effectuer une requête HTTP
   */
  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Toujours essayer le vrai serveur d'abord
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      console.log(`🌐 Tentative de connexion: ${url}`);
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ Réponse API reçue pour ${endpoint}`);
      return data;
    } catch (error) {
      console.warn(`⚠️ Erreur API pour ${endpoint}, utilisation du mode mock:`, error);
      // En cas d'erreur, basculer vers les données mock
      return this.handleMockRequest(endpoint, options);
    }
  }

  private async handleMockRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 200));

    const method = options.method || 'GET';

    // Router les requêtes mock
    if (endpoint.includes('/insurance/dashboard')) {
      return { success: true, data: mockInsuranceDashboard };
    } else if (endpoint.includes('/insurance/analytics')) {
      return { success: true, data: mockInsuranceAnalytics };
    } else if (endpoint.includes('/compliance/acaps/reports')) {
      return { success: true, data: { reports: mockComplianceACAPS.reports } };
    } else if (endpoint.includes('/compliance/metrics')) {
      return { success: true, data: { metrics: mockComplianceACAPS.metrics } };
    } else if (endpoint.includes('/compliance/alerts')) {
      return { success: true, data: { alerts: mockComplianceACAPS.alerts } };
    } else if (endpoint.includes('/leads')) {
      return { success: true, data: { leads: mockLeads } };
    } else if (endpoint.includes('/clients')) {
      return { success: true, data: { clients: mockClients } };
    } else if (endpoint.includes('/quotes')) {
      return { success: true, data: { quotes: mockQuotes } };
    } else if (endpoint.includes('/claims')) {
      return { success: true, data: { claims: mockClaims } };
    } else if (endpoint.includes('/banners')) {
      return { success: true, data: { banners: mockBanners } };
    } else if (endpoint.includes('/blog')) {
      return { success: true, data: { posts: mockBlogPosts } };
    } else if (endpoint.includes('/content/pages')) {
      return { success: true, data: { pages: mockContentPages } };
    } else if (endpoint.includes('/upload')) {
      if (endpoint.includes('/media')) {
        return { success: true, data: { files: mockMediaFiles } };
      }
      return { success: true, message: 'Fichier uploadé (simulé)' };
    } else if (endpoint.includes('/analytics/dashboard')) {
      return { success: true, data: mockDashboardStats };
    }

    return { success: true, message: 'Opération simulée' };
  }

  /**
   * Requête GET
   */
  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * Requête POST
   */
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Requête PUT
   */
  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Requête DELETE
   */
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // === AUTHENTIFICATION ===

  /**
   * Connexion utilisateur
   */
  async login(email: string, password: string) {
    const response = await this.post('/auth/login', { email, password });
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    return response;
  }

  /**
   * Déconnexion utilisateur
   */
  async logout() {
    try {
      await this.post('/auth/logout');
    } finally {
      this.setToken(null);
    }
  }

  /**
   * Obtenir les informations de l'utilisateur connecté
   */
  async getMe() {
    return this.get('/auth/me');
  }

  /**
   * Mettre à jour le profil utilisateur
   */
  async updateProfile(data: { name?: string; email?: string }) {
    return this.put('/auth/profile', data);
  }

  // === LEADS ===

  /**
   * Créer un nouveau lead (public)
   */
  async createLead(leadData: {
    name: string;
    email: string;
    phone: string;
    product: string;
    source: string;
    city?: string;
    message?: string;
  }) {
    return this.post('/leads', leadData);
  }

  /**
   * Obtenir tous les leads
   */
  async getLeads(params?: {
    page?: number;
    limit?: number;
    status?: string;
    product?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/leads${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Obtenir un lead spécifique
   */
  async getLead(id: string) {
    return this.get(`/leads/${id}`);
  }

  /**
   * Mettre à jour un lead
   */
  async updateLead(id: string, data: any) {
    return this.put(`/leads/${id}`, data);
  }

  /**
   * Supprimer un lead
   */
  async deleteLead(id: string) {
    return this.delete(`/leads/${id}`);
  }

  /**
   * Assigner un lead à un utilisateur
   */
  async assignLead(id: string, userId: string | null) {
    return this.post(`/leads/${id}/assign`, { userId });
  }





  // === CLIENTS ===

  /**
   * Obtenir tous les clients
   */
  async getClients(params?: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/clients${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Créer un nouveau client
   */
  async createClient(clientData: any) {
    return this.post('/clients', clientData);
  }

  /**
   * Mettre à jour un client
   */
  async updateClient(id: string, data: any) {
    return this.put(`/clients/${id}`, data);
  }

  /**
   * Obtenir un client spécifique
   */
  async getClient(id: string) {
    return this.get(`/clients/${id}`);
  }

  /**
   * Supprimer un client
   */
  async deleteClient(id: string) {
    return this.delete(`/clients/${id}`);
  }

  // === DEVIS ===

  /**
   * Obtenir tous les devis
   */
  async getQuotes(params?: {
    page?: number;
    limit?: number;
    status?: string;
    clientId?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/quotes${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Créer un nouveau devis
   */
  async createQuote(quoteData: any) {
    return this.post('/quotes', quoteData);
  }

  /**
   * Mettre à jour un devis
   */
  async updateQuote(id: string, data: any) {
    return this.put(`/quotes/${id}`, data);
  }

  /**
   * Envoyer un devis par email
   */
  async sendQuote(id: string) {
    return this.post(`/quotes/${id}/send`);
  }

  /**
   * Obtenir tous les sinistres
   */
  async getClaims(params?: {
    page?: number;
    limit?: number;
    status?: string;
    clientId?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/claims${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Créer un nouveau sinistre
   */
  async createClaim(claimData: any) {
    return this.post('/claims', claimData);
  }

  /**
   * Mettre à jour un sinistre
   */
  async updateClaim(id: string, data: any) {
    return this.put(`/claims/${id}`, data);
  }

  /**
   * Obtenir tous les bandeaux
   */
  async getBanners() {
    return this.get('/banners');
  }

  /**
   * Obtenir les bandeaux actifs (public)
   */
  async getActiveBanners() {
    return this.get('/banners/active');
  }

  /**
   * Créer un nouveau bandeau
   */
  async createBanner(bannerData: any) {
    return this.post('/banners', bannerData);
  }

  /**
   * Mettre à jour un bandeau
   */
  async updateBanner(id: string, data: any) {
    return this.put(`/banners/${id}`, data);
  }

  /**
   * Supprimer un bandeau
   */
  async deleteBanner(id: string) {
    return this.delete(`/banners/${id}`);
  }

  /**
   * Activer/désactiver un bandeau
   */
  async toggleBanner(id: string) {
    return this.post(`/banners/${id}/toggle`);
  }

  /**
   * Obtenir tous les articles de blog
   */
  async getBlogPosts(params?: {
    page?: number;
    limit?: number;
    status?: string;
    category?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/blog${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Créer un nouvel article de blog
   */
  async createBlogPost(postData: any) {
    return this.post('/blog', postData);
  }

  /**
   * Mettre à jour un article de blog
   */
  async updateBlogPost(id: string, data: any) {
    return this.put(`/blog/${id}`, data);
  }

  /**
   * Supprimer un article de blog
   */
  async deleteBlogPost(id: string) {
    return this.delete(`/blog/${id}`);
  }

  /**
   * Obtenir toutes les pages de contenu
   */
  async getContentPages(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/content/pages${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Créer une nouvelle page de contenu
   */
  async createContentPage(pageData: any) {
    return this.post('/content/pages', pageData);
  }

  /**
   * Mettre à jour une page de contenu
   */
  async updateContentPage(id: string, data: any) {
    return this.put(`/content/pages/${id}`, data);
  }

  /**
   * Supprimer une page de contenu
   */
  async deleteContentPage(id: string) {
    return this.delete(`/content/pages/${id}`);
  }

  /**
   * Upload d'un fichier
   */
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': this.token ? `Bearer ${this.token}` : '',
      },
      body: formData,
    });

    return response.json();
  }

  /**
   * Obtenir tous les fichiers média
   */
  async getMediaFiles(params?: {
    page?: number;
    limit?: number;
    type?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/upload/media${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  /**
   * Supprimer un fichier média
   */
  async deleteMediaFile(id: string) {
    return this.delete(`/upload/${id}`);
  }

  /**
   * Obtenir les statistiques du dashboard
   */
  async getDashboardStats() {
    return this.get('/analytics/dashboard');
  }

  /**
   * Obtenir les statistiques des leads
   */
  async getLeadsStats(period?: string) {
    const endpoint = `/analytics/leads${period ? `?period=${period}` : ''}`;
    return this.get(endpoint);
  }

  // === MÉTHODES SPÉCIFIQUES ASSURANCE ===

  /**
   * Dashboard Assurance - Métriques spécialisées
   */
  async getInsuranceDashboard(period: string = 'month') {
    try {
      const endpoint = `/insurance/dashboard?period=${period}`;
      return this.get(endpoint);
    } catch (error) {
      // Fallback vers données mock en cas d'erreur
      return {
        success: true,
        data: {
          // Données mock pour le dashboard assurance
          // Sera remplacé par de vraies données API
        }
      };
    }
  }

  /**
   * Analytics Assurance - Métriques avancées
   */
  async getInsuranceAnalytics(period: string = 'month') {
    try {
      const endpoint = `/insurance/analytics?period=${period}`;
      return this.get(endpoint);
    } catch (error) {
      return {
        success: true,
        data: {
          // Données mock pour les analytics assurance
        }
      };
    }
  }

  /**
   * Conformité ACAPS - Rapports réglementaires
   */
  async getACAPSReports() {
    try {
      return this.get('/compliance/acaps/reports');
    } catch (error) {
      return {
        success: true,
        data: { reports: [] }
      };
    }
  }

  async getComplianceMetrics() {
    try {
      return this.get('/compliance/metrics');
    } catch (error) {
      return {
        success: true,
        data: { metrics: [] }
      };
    }
  }

  async getRegulatoryAlerts() {
    try {
      return this.get('/compliance/alerts');
    } catch (error) {
      return {
        success: true,
        data: { alerts: [] }
      };
    }
  }

  /**
   * Calculateur de primes
   */
  async calculatePremium(productType: string, riskFactors: any) {
    try {
      return this.post('/insurance/calculate-premium', {
        productType,
        riskFactors
      });
    } catch (error) {
      return {
        success: true,
        data: {
          basePremium: 2000,
          adjustments: [],
          discounts: [],
          totalPremium: 2000
        }
      };
    }
  }
}

// Configuration de l'URL de base de l'API
const API_BASE_URL = import.meta.env.PROD ? '/api' : 'http://localhost:3001/api';

// Instance singleton de l'API
export const api = new ApiService(API_BASE_URL);

// Hook pour l'authentification
export const useAuth = () => {
  const login = async (email: string, password: string) => {
    try {
      const response = await api.login(email, password);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await api.logout();
      window.location.href = '/admin/login';
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout même en cas d'erreur
      api.setToken(null);
      window.location.href = '/admin/login';
    }
  };

  const isAuthenticated = () => {
    return !!localStorage.getItem('auth_token');
  };

  return {
    login,
    logout,
    isAuthenticated,
  };
};

export default api;
