// Types corrigés pour l'administration

export interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  product: string;
  source: string;
  city?: string;
  message?: string;
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'CONVERTED' | 'LOST';
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: 'INDIVIDUAL' | 'BUSINESS';
  city: string;
  address?: string;
  dateOfBirth?: string;
  profession?: string;
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  quotes?: Quote[];
  contracts?: Contract[];
  claims?: Claim[];
}

export interface Quote {
  id: string;
  clientId: string;
  client: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  product: string;
  amount: number;
  status: 'DRAFT' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED';
  validUntil: string;
  details?: any;
  createdBy: string;
  createdUser?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Contract {
  id: string;
  clientId: string;
  client?: {
    id: string;
    name: string;
    email: string;
  };
  product: string;
  premium: number;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED';
  startDate: string;
  endDate: string;
  details?: any;
  createdAt: string;
  updatedAt: string;
}

export interface Claim {
  id: string;
  clientId: string;
  client: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  contractId?: string;
  contract?: {
    id: string;
    product: string;
    premium: number;
  };
  type: string;
  description: string;
  amount?: number;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'PAID';
  documents?: any;
  assignedTo?: string;
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Banner {
  id: string;
  name: string;
  text: string;
  type: 'INFO' | 'PROMOTION' | 'ALERT';
  status: 'ACTIVE' | 'INACTIVE' | 'SCHEDULED' | 'EXPIRED';
  backgroundColor: string;
  textColor: string;
  startDate: string;
  endDate: string;
  showContact: boolean;
  views: number;
  clicks: number;
  createdAt: string;
  updatedAt: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  image?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  tags?: string[];
  category?: string;
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ContentPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface MediaFile {
  id: string;
  name: string;
  originalName: string;
  url: string;
  type: 'image' | 'video' | 'document';
  size: number;
  mimeType: string;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DashboardStats {
  totalLeads: number;
  totalClients: number;
  totalQuotes: number;
  totalContracts: number;
  totalRevenue: number;
  leadsThisMonth: number;
  clientsThisMonth: number;
  quotesThisMonth: number;
  contractsThisMonth: number;
  revenueThisMonth: number;
  leadsGrowth: number;
  clientsGrowth: number;
  quotesGrowth: number;
  contractsGrowth: number;
  leadsByStatus: Array<{ status: string; count: number }>;
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
  }>;
}

export interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: any;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// Types pour les formulaires
export interface LeadFormData {
  name: string;
  email: string;
  phone: string;
  product: string;
  source: string;
  city?: string;
  message?: string;
}

export interface ClientFormData {
  name: string;
  email: string;
  phone: string;
  type: 'INDIVIDUAL' | 'BUSINESS';
  city: string;
  address?: string;
  dateOfBirth?: string;
  profession?: string;
}

export interface QuoteFormData {
  clientId: string;
  product: string;
  amount: number;
  validUntil: string;
  details?: any;
}

export interface ClaimFormData {
  clientId: string;
  contractId?: string;
  type: string;
  description: string;
  amount?: number;
  documents?: any;
}

export interface BannerFormData {
  name: string;
  text: string;
  type: 'INFO' | 'PROMOTION' | 'ALERT';
  backgroundColor: string;
  textColor: string;
  startDate: string;
  endDate: string;
  showContact: boolean;
}

export interface BlogPostFormData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  image?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  tags?: string[];
  category?: string;
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
}

export interface ContentPageFormData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  seo?: {
    title: string;
    description: string;
    keywords: string;
  };
}
