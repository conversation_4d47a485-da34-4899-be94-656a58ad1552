// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  leads     Lead[]
  clients   Client[]
  quotes    Quote[]
  claims    Claim[]
  sessions  Session[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Lead {
  id          String     @id @default(cuid())
  name        String
  email       String
  phone       String
  product     String
  source      String
  city        String?
  message     String?
  status      LeadStatus @default(NEW)
  assignedTo  String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  assignedUser User? @relation(fields: [assignedTo], references: [id])

  @@map("leads")
}

model Client {
  id          String     @id @default(cuid())
  name        String
  email       String     @unique
  phone       String
  type        ClientType
  city        String
  address     String?
  dateOfBirth DateTime?
  profession  String?
  assignedTo  String?
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  assignedUser User?     @relation(fields: [assignedTo], references: [id])
  quotes       Quote[]
  claims       Claim[]
  contracts    Contract[]

  @@map("clients")
}

model Quote {
  id          String      @id @default(cuid())
  clientId    String
  product     String
  amount      Decimal
  status      QuoteStatus @default(DRAFT)
  validUntil  DateTime
  details     Json?
  createdBy   String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  client    Client @relation(fields: [clientId], references: [id])
  createdUser User @relation(fields: [createdBy], references: [id])

  @@map("quotes")
}

model Contract {
  id           String         @id @default(cuid())
  clientId     String
  product      String
  premium      Decimal
  status       ContractStatus @default(ACTIVE)
  startDate    DateTime
  endDate      DateTime
  details      Json?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  // Relations
  client Client @relation(fields: [clientId], references: [id])
  claims Claim[]

  @@map("contracts")
}

model Claim {
  id          String      @id @default(cuid())
  clientId    String
  contractId  String?
  type        String
  description String
  amount      Decimal?
  status      ClaimStatus @default(SUBMITTED)
  documents   Json?
  assignedTo  String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  client       Client    @relation(fields: [clientId], references: [id])
  contract     Contract? @relation(fields: [contractId], references: [id])
  assignedUser User?     @relation(fields: [assignedTo], references: [id])

  @@map("claims")
}

model Banner {
  id              String      @id @default(cuid())
  name            String
  text            String
  type            BannerType
  status          BannerStatus @default(INACTIVE)
  backgroundColor String
  textColor       String
  startDate       DateTime
  endDate         DateTime
  showContact     Boolean     @default(false)
  views           Int         @default(0)
  clicks          Int         @default(0)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("banners")
}

model BlogPost {
  id          String    @id @default(cuid())
  title       String
  slug        String    @unique
  content     String    @db.LongText
  excerpt     String?
  image       String?
  status      PostStatus @default(DRAFT)
  publishedAt DateTime?
  tags        Json?
  category    String?
  seo         Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("blog_posts")
}

model ContentPage {
  id          String    @id @default(cuid())
  title       String
  slug        String    @unique
  content     String    @db.LongText
  excerpt     String?
  status      PostStatus @default(DRAFT)
  publishedAt DateTime?
  seo         Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("content_pages")
}

model MediaFile {
  id          String    @id @default(cuid())
  name        String
  originalName String
  url         String
  type        String
  size        Int
  mimeType    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("media_files")
}

// Enums
enum Role {
  USER
  ADMIN
  SUPER_ADMIN
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  CONVERTED
  LOST
}

enum ClientType {
  INDIVIDUAL
  BUSINESS
}

enum QuoteStatus {
  DRAFT
  SENT
  ACCEPTED
  REJECTED
  EXPIRED
}

enum ContractStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  SUSPENDED
}

enum ClaimStatus {
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
  PAID
}

enum BannerType {
  INFO
  PROMOTION
  ALERT
}

enum BannerStatus {
  ACTIVE
  INACTIVE
  SCHEDULED
  EXPIRED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}
