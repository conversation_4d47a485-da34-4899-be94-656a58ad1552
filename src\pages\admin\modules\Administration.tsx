import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  User,
  Database,
  Shield,
  Server,
  Activity,
  Box,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  HardDrive
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface SystemStatus {
  cpu: number;
  memory: number;
  disk: number;
  uptime: string;
  lastBackup: string;
  activeUsers: number;
  queuedTasks: number;
  databaseSize: string;
  serverLoad: number;
}

export const Administration: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus>({
    cpu: 45,
    memory: 68,
    disk: 72,
    uptime: '15 jours 7 heures',
    lastBackup: '2024-01-15 03:00:00',
    activeUsers: 127,
    queuedTasks: 5,
    databaseSize: '1.2 GB',
    serverLoad: 0.75
  });
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  const refreshStatus = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/api/admin/system-status');
      setStatus(response.data);
      setToast({
        message: 'Statut du système mis à jour',
        type: 'success'
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du statut:', error);
      setToast({
        message: 'Erreur lors de la mise à jour du statut',
        type: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (value: number) => {
    if (value < 60) return 'text-green-500';
    if (value < 80) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getProgressColor = (value: number) => {
    if (value < 60) return 'bg-green-500';
    if (value < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Administration Système</h1>
          <p className="text-gray-600">Supervision et maintenance du système</p>
        </div>
        <button
          onClick={refreshStatus}
          disabled={isLoading}
          className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50"
        >
          <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Actualiser</span>
        </button>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Server className="h-8 w-8 text-blue-500" />
            <span className={`text-2xl font-bold ${getStatusColor(status.cpu)}`}>{status.cpu}%</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Utilisation CPU</h3>
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(status.cpu)}`}
              style={{ width: `${status.cpu}%` }}
            ></div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <HardDrive className="h-8 w-8 text-purple-500" />
            <span className={`text-2xl font-bold ${getStatusColor(status.memory)}`}>{status.memory}%</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Mémoire</h3>
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(status.memory)}`}
              style={{ width: `${status.memory}%` }}
            ></div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Database className="h-8 w-8 text-indigo-500" />
            <span className="text-lg font-bold text-gray-900">{status.databaseSize}</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Taille Base de Données</h3>
          <p className="text-sm text-gray-500 mt-1">Dernière sauvegarde: {new Date(status.lastBackup).toLocaleString('fr-FR')}</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <Activity className="h-8 w-8 text-green-500" />
            <span className="text-2xl font-bold text-gray-900">{status.activeUsers}</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Utilisateurs Actifs</h3>
          <p className="text-sm text-gray-500 mt-1">Uptime: {status.uptime}</p>
        </motion.div>
      </div>

      {/* Informations détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tâches système */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Tâches Système</h3>
            <span className="text-sm text-gray-500">{status.queuedTasks} en attente</span>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium">Sauvegarde automatique</span>
              </div>
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                Programmée
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <RefreshCw className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium">Maintenance cache</span>
              </div>
              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                En attente
              </span>
            </div>
          </div>
        </motion.div>

        {/* Statut serveur */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Statut Serveur</h3>
            <span className={`px-2 py-1 rounded-full text-xs ${
              status.serverLoad < 0.7 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {status.serverLoad < 0.7 ? 'Normal' : 'Chargé'}
            </span>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Charge serveur</span>
              <span className="text-sm font-medium">{status.serverLoad.toFixed(2)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${getProgressColor(status.serverLoad * 100)}`}
                style={{ width: `${status.serverLoad * 100}%` }}
              ></div>
            </div>
          </div>
        </motion.div>
      </div>

      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default Administration;
