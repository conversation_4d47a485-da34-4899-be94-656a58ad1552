import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Toast } from '../../components/common/Toast';

describe('Toast', () => {
  it('renders success toast correctly', () => {
    const onClose = vi.fn();
    
    render(
      <Toast 
        message="Success message" 
        type="success" 
        onClose={onClose} 
      />
    );

    expect(screen.getByText('Success message')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('renders error toast correctly', () => {
    const onClose = vi.fn();
    
    render(
      <Toast 
        message="Error message" 
        type="error" 
        onClose={onClose} 
      />
    );

    expect(screen.getByText('Error message')).toBeInTheDocument();
  });

  it('renders info toast correctly', () => {
    const onClose = vi.fn();
    
    render(
      <Toast 
        message="Info message" 
        type="info" 
        onClose={onClose} 
      />
    );

    expect(screen.getByText('Info message')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const onClose = vi.fn();
    
    render(
      <Toast 
        message="Test message" 
        type="success" 
        onClose={onClose} 
      />
    );

    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);

    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('auto-closes after 3 seconds', async () => {
    const onClose = vi.fn();
    
    render(
      <Toast 
        message="Test message" 
        type="success" 
        onClose={onClose} 
      />
    );

    // Attendre que le toast se ferme automatiquement
    await waitFor(() => {
      expect(onClose).toHaveBeenCalledTimes(1);
    }, { timeout: 4000 });
  });

  it('applies correct CSS classes for different types', () => {
    const onClose = vi.fn();
    
    const { rerender } = render(
      <Toast 
        message="Success message" 
        type="success" 
        onClose={onClose} 
      />
    );

    let toastElement = screen.getByText('Success message').closest('div');
    expect(toastElement).toHaveClass('bg-green-500');

    rerender(
      <Toast 
        message="Error message" 
        type="error" 
        onClose={onClose} 
      />
    );

    toastElement = screen.getByText('Error message').closest('div');
    expect(toastElement).toHaveClass('bg-red-500');

    rerender(
      <Toast 
        message="Info message" 
        type="info" 
        onClose={onClose} 
      />
    );

    toastElement = screen.getByText('Info message').closest('div');
    expect(toastElement).toHaveClass('bg-blue-500');
  });
});
