import React from 'react';
import { Controller } from 'react-hook-form';
import { Calendar } from 'lucide-react';

interface SchedulingFormProps {
  control: any;
  errors: any;
}

export const SchedulingForm: React.FC<SchedulingFormProps> = ({ control, errors }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Programmation</h4>
      
      <Controller
        name="startDate"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de début
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                {...field}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              />
              <Calendar className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            {errors.startDate && (
              <p className="mt-1 text-sm text-red-600">
                {errors.startDate.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="endDate"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de fin
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                {...field}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              />
              <Calendar className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            {errors.endDate && (
              <p className="mt-1 text-sm text-red-600">
                {errors.endDate.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="priority"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priorité
            </label>
            <input
              type="number"
              min="1"
              max="10"
              {...field}
              onChange={(e) => field.onChange(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
            {errors.priority && (
              <p className="mt-1 text-sm text-red-600">
                {errors.priority.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default SchedulingForm;
