import { Router } from 'express';
import Joi from 'joi';
// ...existing code...
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createClaimSchema = Joi.object({
  clientId: Joi.string().required(),
  contractId: Joi.string(),
  type: Joi.string().required(),
  description: Joi.string().min(10).max(1000).required(),
  amount: Joi.number().positive(),
  documents: Joi.object(),
});

const updateClaimSchema = Joi.object({
  type: Joi.string(),
  description: Joi.string().min(10).max(1000),
  amount: Joi.number().positive(),
  status: Joi.string().valid('SUBMITTED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'PAID'),
  documents: Joi.object(),
  assignedTo: Joi.string().uuid().allow(null),
});

/**
 * GET /api/claims
 * Récupérer tous les sinistres
 */
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const clientId = req.query.clientId as string;
  const type = req.query.type as string;

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  if (status && status !== 'all') where.status = status;
  if (clientId) where.clientId = clientId;
  if (type) where.type = type;

  // Si l'utilisateur n'est pas admin, ne voir que ses sinistres assignés
  if (req.user?.role === 'USER') {
    where.assignedTo = req.user.id;
  }

  const [claims, total] = await Promise.all([
// ...existing code...
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        client: {
          select: { id: true, name: true, email: true, phone: true },
        },
        contract: {
          select: { id: true, product: true, premium: true },
        },
        assignedUser: {
          select: { id: true, name: true, email: true },
        },
      },
    }),
// ...existing code...
  ]);

  res.json({
    success: true,
    data: {
      claims,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/claims/:id
 * Récupérer un sinistre spécifique
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
// ...existing code...
    where: { id: req.params.id },
    include: {
      client: true,
      contract: true,
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  if (!claim) {
    throw createError('Sinistre non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && claim.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  res.json({
    success: true,
    data: { claim },
  });
}));

/**
 * POST /api/claims
 * Créer un nouveau sinistre
 */
router.post('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createClaimSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le client existe
// ...existing code...
    where: { id: value.clientId },
  });

  if (!client) {
    throw createError('Client non trouvé', 404);
  }

  // Vérifier le contrat si fourni
  if (value.contractId) {
// ...existing code...
      where: { id: value.contractId },
    });

    if (!contract || contract.clientId !== value.clientId) {
      throw createError('Contrat non trouvé ou non associé à ce client', 404);
    }
  }

  // Créer le sinistre
// ...existing code...
    data: {
      ...value,
      assignedTo: req.user?.role === 'USER' ? req.user.id : null,
    },
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
      contract: {
        select: { id: true, product: true },
      },
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Claim created: ${claim.id} for client ${client.name} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Sinistre créé avec succès',
    data: { claim },
  });
}));

/**
 * PUT /api/claims/:id
 * Mettre à jour un sinistre
 */
router.put('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updateClaimSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le sinistre existe
// ...existing code...
    where: { id: req.params.id },
  });

  if (!existingClaim) {
    throw createError('Sinistre non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && existingClaim.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  // Mettre à jour le sinistre
// ...existing code...
    where: { id: req.params.id },
    data: value,
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
      contract: {
        select: { id: true, product: true },
      },
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Claim updated: ${claim.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Sinistre mis à jour avec succès',
    data: { claim },
  });
}));

/**
 * POST /api/claims/:id/assign
 * Assigner un sinistre à un utilisateur
 */
router.post('/:id/assign', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.body;

  if (userId) {
    // Vérifier que l'utilisateur existe
// ...existing code...
      where: { id: userId },
    });

    if (!user) {
      throw createError('Utilisateur non trouvé', 404);
    }
  }

// ...existing code...
    where: { id: req.params.id },
    data: { assignedTo: userId || null },
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Claim ${claim.id} assigned to ${userId || 'unassigned'} by ${req.user?.email}`);

  res.json({
    success: true,
    message: userId ? 'Sinistre assigné avec succès' : 'Sinistre désassigné avec succès',
    data: { claim },
  });
}));

/**
 * POST /api/claims/:id/approve
 * Approuver un sinistre
 */
router.post('/:id/approve', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { amount } = req.body;

// ...existing code...
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!claim) {
    throw createError('Sinistre non trouvé', 404);
  }

  if (claim.status !== 'UNDER_REVIEW') {
    throw createError('Ce sinistre ne peut pas être approuvé', 400);
  }

  // Mettre à jour le sinistre
// ...existing code...
    where: { id: req.params.id },
    data: { 
      status: 'APPROVED',
      amount: amount || claim.amount,
    },
    include: {
      client: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Claim approved: ${claim.id} for amount ${amount || claim.amount} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Sinistre approuvé avec succès',
    data: { claim: updatedClaim },
  });
}));

/**
 * POST /api/claims/:id/reject
 * Rejeter un sinistre
 */
router.post('/:id/reject', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { reason } = req.body;

  if (!reason) {
    throw createError('Raison du rejet requise', 400);
  }

// ...existing code...
    where: { id: req.params.id },
    include: { client: true },
  });

  if (!claim) {
    throw createError('Sinistre non trouvé', 404);
  }

  if (claim.status === 'PAID' || claim.status === 'REJECTED') {
    throw createError('Ce sinistre ne peut pas être rejeté', 400);
  }

  // Mettre à jour le sinistre
// ...existing code...
    where: { id: req.params.id },
    data: { 
      status: 'REJECTED',
      documents: {
        ...(claim.documents as any || {}),
        rejectionReason: reason,
        rejectedAt: new Date(),
        rejectedBy: req.user?.id,
      },
    },
  });

  logger.info(`Claim rejected: ${claim.id} by ${req.user?.email}, reason: ${reason}`);

  res.json({
    success: true,
    message: 'Sinistre rejeté',
    data: { claim: updatedClaim },
  });
}));

/**
 * DELETE /api/claims/:id
 * Supprimer un sinistre
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
// ...existing code...
    where: { id: req.params.id },
  });

  if (!claim) {
    throw createError('Sinistre non trouvé', 404);
  }

  if (claim.status === 'PAID') {
    throw createError('Impossible de supprimer un sinistre payé', 400);
  }

  await prisma.claim.delete({
    where: { id: req.params.id },
  });

  logger.info(`Claim deleted: ${claim.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Sinistre supprimé avec succès',
  });
}));

export default router;
