import React from 'react';
import { Controller } from 'react-hook-form';
import { Palette } from 'lucide-react';

interface StyleFormProps {
  control: any;
  errors: any;
}

export const StyleForm: React.FC<StyleFormProps> = ({ control, errors }) => {
  const predefinedStyles = [
    { 
      name: '<PERSON><PERSON>fa<PERSON>',
      backgroundColor: '#1D4ED8', 
      textColor: '#FFFFFF'
    },
    { 
      name: 'Succ<PERSON>',
      backgroundColor: '#059669', 
      textColor: '#FFFFFF'
    },
    { 
      name: 'Attention',
      backgroundColor: '#D97706', 
      textColor: '#FFFFFF'
    },
    { 
      name: 'Erreur',
      backgroundColor: '#DC2626', 
      textColor: '#FFFFFF'
    },
    { 
      name: 'Info',
      backgroundColor: '#2563EB', 
      textColor: '#FFFFFF'
    },
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Style</h4>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        {predefinedStyles.map((style) => (
          <button
            key={style.name}
            type="button"
            onClick={() => {
              control.setValue('backgroundColor', style.backgroundColor);
              control.setValue('textColor', style.textColor);
            }}
            className="p-2 border border-gray-200 rounded-lg hover:border-axa-blue focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
          >
            <div
              className="h-8 rounded-md mb-2"
              style={{ backgroundColor: style.backgroundColor }}
            />
            <span className="text-sm text-gray-700">{style.name}</span>
          </button>
        ))}
      </div>

      <Controller
        name="backgroundColor"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couleur de fond
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                {...field}
                className="h-9 w-16 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={field.value}
                onChange={(e) => field.onChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                placeholder="#000000"
              />
            </div>
            {errors.backgroundColor && (
              <p className="mt-1 text-sm text-red-600">
                {errors.backgroundColor.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="textColor"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couleur du texte
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                {...field}
                className="h-9 w-16 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={field.value}
                onChange={(e) => field.onChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                placeholder="#FFFFFF"
              />
            </div>
            {errors.textColor && (
              <p className="mt-1 text-sm text-red-600">
                {errors.textColor.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default StyleForm;
