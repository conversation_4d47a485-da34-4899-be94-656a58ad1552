import { useState, useEffect } from 'react';
import { QuoteTemplate, Lead } from '../../types/admin';
import aiService from '../../services/aiService';

interface UseQuoteAIReturn {
  loading: boolean;
  optimizedTemplate: QuoteTemplate | null;
  recommendations: {
    title: string;
    description: string;
    impact: number;
    confidence: number;
  }[];
  predictedOutcome: {
    probability: number;
    factors: string[];
  } | null;
  error: string | null;
}

export const useQuoteAI = (
  template: QuoteTemplate | null,
  leadData: Lead | null,
  formData: Record<string, any>
): UseQuoteAIReturn => {
  const [loading, setLoading] = useState(false);
  const [optimizedTemplate, setOptimizedTemplate] = useState<QuoteTemplate | null>(null);
  const [recommendations, setRecommendations] = useState<UseQuoteAIReturn['recommendations']>([]);
  const [predictedOutcome, setPredictedOutcome] = useState<UseQuoteAIReturn['predictedOutcome']>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const optimizeQuote = async () => {
      if (!template || !leadData) return;

      setLoading(true);
      setError(null);

      try {
        // Personnaliser le modèle de devis
        const customizedTemplate = await aiService.customizeQuote(template, leadData);
        setOptimizedTemplate(customizedTemplate);

        // Prédire le résultat
        const prediction = await aiService.predictOutcome(
          {
            template: customizedTemplate,
            lead: leadData,
            formData
          },
          'QUOTE'
        );

        // Mettre à jour l'état avec les prédictions
        setPredictedOutcome({
          probability: prediction.probability,
          factors: prediction.factors
        });

        // Transformer les recommandations
        setRecommendations(
          prediction.recommendations.map(rec => ({
            title: rec.title,
            description: rec.description,
            impact: rec.expectedImpact,
            confidence: rec.confidence
          }))
        );
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
        console.error('Erreur lors de l\'optimisation du devis:', err);
      } finally {
        setLoading(false);
      }
    };

    optimizeQuote();
  }, [template, leadData, formData]);

  return {
    loading,
    optimizedTemplate,
    recommendations,
    predictedOutcome,
    error
  };
};

export default useQuoteAI;
