import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Edit,
  Eye,
  Trash2,
  Save,
  Play,
  Pause,
  Monitor,
  Smartphone,
  Tablet,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

// Types pour les bandeaux
interface Banner {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  targetPages: string[];
  priority: number;
  createdAt: string;
  updatedAt: string;
}

export const BannerManagement: React.FC = () => {
  const [selectedBanner, setSelectedBanner] = useState<Banner | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [banners, setBanners] = useState<Banner[]>([
    {
      id: 1,
      title: 'Promotion Assurance Auto',
      content: 'Profitez de -20% sur votre assurance auto jusqu\'au 31 mars 2024',
      type: 'success',
      isActive: true,
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      targetPages: ['/', '/assurance-auto'],
      priority: 1,
      createdAt: '2024-01-15 10:00',
      updatedAt: '2024-01-15 10:00'
    },
    {
      id: 2,
      title: 'Maintenance Programmée',
      content: 'Maintenance du site prévue le dimanche 25 février de 2h à 4h du matin',
      type: 'warning',
      isActive: false,
      startDate: '2024-02-24',
      endDate: '2024-02-25',
      targetPages: ['/'],
      priority: 2,
      createdAt: '2024-02-20 14:30',
      updatedAt: '2024-02-20 14:30'
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  // Fonction de validation des bandeaux
  const validateBanner = (bannerData: Partial<Banner>) => {
    const errors: string[] = [];

    if (!bannerData.title || bannerData.title.trim().length < 3) {
      errors.push('Le titre doit contenir au moins 3 caractères');
    }

    if (!bannerData.content || bannerData.content.trim().length < 10) {
      errors.push('Le contenu doit contenir au moins 10 caractères');
    }

    if (!bannerData.type || !['info', 'warning', 'success', 'error'].includes(bannerData.type)) {
      errors.push('Le type de bandeau est requis');
    }

    if (bannerData.startDate && bannerData.endDate && new Date(bannerData.startDate) > new Date(bannerData.endDate)) {
      errors.push('La date de fin doit être postérieure à la date de début');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  useEffect(() => {
    // Simulation du chargement initial
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showToast('Bandeaux chargés avec succès', 'success');
    }, 1000);
  }, []);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadBanners = async () => {
    try {
      setIsLoading(true);
      // Simulation d'un appel API
      await new Promise(resolve => setTimeout(resolve, 500));
      showToast('Bandeaux rechargés avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des bandeaux', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Ajout/édition avec validation stricte
  const handleSaveBanner = (bannerData: Partial<Banner>) => {
    try {
      const validation = validateBanner(bannerData);
      if (!validation.isValid) {
        showToast(validation.errors.join(', '), 'error');
        return;
      }

      if (selectedBanner) {
        setBanners(banners.map(banner =>
          banner.id === selectedBanner.id
            ? { ...banner, ...bannerData, updatedAt: new Date().toISOString().slice(0, 16) }
            : banner
        ));
        showToast('Bandeau modifié avec succès', 'success');
      } else {
        const newBanner: Banner = {
          id: Math.max(...banners.map(b => b.id), 0) + 1,
          title: bannerData.title || '',
          content: bannerData.content || '',
          type: bannerData.type || 'info',
          isActive: bannerData.isActive || false,
          startDate: bannerData.startDate,
          endDate: bannerData.endDate,
          targetPages: bannerData.targetPages || ['/'],
          priority: bannerData.priority || 1,
          createdAt: new Date().toISOString().slice(0, 16),
          updatedAt: new Date().toISOString().slice(0, 16)
        };
        setBanners([...banners, newBanner]);
        showToast('Bandeau créé avec succès', 'success');
      }
      setShowEditor(false);
      setSelectedBanner(null);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du bandeau:', error);
      showToast('Erreur lors de la sauvegarde', 'error');
    }
  };

  const handleDeleteBanner = (bannerId: number) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce bandeau ?')) {
      try {
        setBanners(banners.filter(banner => banner.id !== bannerId));
        showToast('Bandeau supprimé avec succès', 'success');
      } catch (error) {
        console.error('Erreur lors de la suppression du bandeau:', error);
        showToast('Erreur lors de la suppression', 'error');
      }
    }
  };

  const handleToggleStatus = (bannerId: number) => {
    try {
      setBanners(banners.map(banner =>
        banner.id === bannerId
          ? { ...banner, isActive: !banner.isActive, updatedAt: new Date().toISOString().slice(0, 16) }
          : banner
      ));
      const banner = banners.find(b => b.id === bannerId);
      showToast(`Bandeau ${banner?.isActive ? 'désactivé' : 'activé'} avec succès`, 'success');
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
      showToast('Erreur lors du changement de statut', 'error');
    }
  };

  const handleEditBanner = (banner: Banner) => {
    setSelectedBanner(banner);
    setShowEditor(true);
  };

  const handleNewBanner = () => {
    setSelectedBanner(null);
    setShowEditor(true);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Bandeaux</h1>
          <p className="text-gray-600 mt-2">Gérez les bandeaux d'information en haut du site</p>
        </div>
        <button
          onClick={handleNewBanner}
          className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau bandeau
        </button>
      </motion.div>

      {/* Banners List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <div className="space-y-6">
          {banners.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">Aucun bandeau configuré</p>
              <button
                onClick={handleNewBanner}
                className="mt-4 text-axa-blue hover:text-blue-800"
              >
                Créer votre premier bandeau
              </button>
            </div>
          ) : Array.isArray(banners) && banners.length > 0 ? (
            banners.map((banner) => (
              <div key={banner.id} className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{banner.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(banner.isActive)}`}>
                        {banner.isActive ? 'Actif' : 'Inactif'}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(banner.type)}`}>
                        {banner.type === 'success' ? 'Succès' :
                         banner.type === 'warning' ? 'Avertissement' :
                         banner.type === 'error' ? 'Erreur' : 'Information'}
                      </span>
                    </div>
                    
                    {/* Preview */}
                    <div className={`${getTypeColor(banner.type)} p-3 rounded-lg mb-4`}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{banner.content}</span>
                        <div className="text-xs opacity-75">
                          Priorité: {banner.priority}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Période:</span>
                        <div>
                          {banner.startDate && banner.endDate
                            ? `${banner.startDate} - ${banner.endDate}`
                            : 'Permanent'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Pages cibles:</span>
                        <div>{banner.targetPages.join(', ')}</div>
                      </div>
                      <div>
                        <span className="font-medium">Dernière modification:</span>
                        <div>{banner.updatedAt}</div>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button 
                      onClick={() => handleEditBanner(banner)}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleToggleStatus(banner.id)}
                      className="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                      title={banner.isActive ? 'Désactiver' : 'Activer'}
                    >
                      {banner.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </button>
                    <button 
                      onClick={() => handleDeleteBanner(banner.id)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Aucun bandeau trouvé</p>
              <button
                onClick={() => setShowEditor(true)}
                className="mt-4 px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
              >
                Créer le premier bandeau
              </button>
            </div>
          )}
        </div>
      </motion.div>

      {/* Banner Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedBanner ? 'Modifier le Bandeau' : 'Nouveau Bandeau'}
              </h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowEditor(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Annuler
                </button>
                <button 
                  onClick={() => handleSaveBanner({
                    name: 'Nouveau bandeau',
                    text: 'Texte du bandeau',
                    type: 'info',
                    backgroundColor: 'bg-gradient-to-r from-axa-blue to-blue-700',
                    textColor: 'text-white',
                    startDate: new Date().toISOString().split('T')[0],
                    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    showContact: false
                  })}
                  className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                >
                  <Save className="h-4 w-4 inline mr-2" />
                  Sauvegarder
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <p className="text-gray-600">Formulaire d'édition de bandeau (à implémenter)</p>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export { BannerManagement as default };