import axios from 'axios';
import { Template } from '../types/admin';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export class PDFService {
  private static instance: PDFService;
  private constructor() {}

  static getInstance(): PDFService {
    if (!PDFService.instance) {
      PDFService.instance = new PDFService();
    }
    return PDFService.instance;
  }

  async getTemplates(): Promise<Template[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/templates`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des templates:', error);
      throw error;
    }
  }

  async createTemplate(template: Partial<Template>): Promise<Template> {
    try {
      const response = await axios.post(`${API_BASE_URL}/templates`, template);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du template:', error);
      throw error;
    }
  }

  async updateTemplate(id: number, template: Partial<Template>): Promise<Template> {
    try {
      const response = await axios.put(`${API_BASE_URL}/templates/${id}`, template);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du template:', error);
      throw error;
    }
  }

  async deleteTemplate(id: number): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/templates/${id}`);
    } catch (error) {
      console.error('Erreur lors de la suppression du template:', error);
      throw error;
    }
  }

  async generatePDF(templateId: number, data: any): Promise<Blob> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/templates/${templateId}/generate`,
        data,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      throw error;
    }
  }

  async previewPDF(templateId: number, data: any): Promise<string> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/templates/${templateId}/preview`,
        data
      );
      return response.data.previewUrl;
    } catch (error) {
      console.error('Erreur lors de la prévisualisation du PDF:', error);
      throw error;
    }
  }
}
