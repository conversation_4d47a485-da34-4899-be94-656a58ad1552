import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Database, Zap, Globe, Smartphone, CreditCard, Mail, MessageSquare, Shield, CheckCircle, XCircle, Settings, Plus, RefreshCw as Refresh, AlertCircle, ExternalLink } from 'lucide-react';

export const Integrations: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'Toutes', count: 12 },
    { id: 'axa', name: 'AXA Systems', count: 3 },
    { id: 'government', name: 'Organismes officiels', count: 4 },
    { id: 'payment', name: 'Paiements', count: 2 },
    { id: 'communication', name: 'Communication', count: 3 }
  ];

  const integrations = [
    {
      id: 1,
      name: 'AXA Core System',
      description: 'Connexion avec le système central AXA pour la gestion des polices',
      category: 'axa',
      status: 'connected',
      icon: Shield,
      color: 'bg-blue-500',
      lastSync: '2024-01-15 14:30',
      features: ['Gestion des polices', 'Calcul des primes', 'Historique client'],
      apiVersion: 'v2.1',
      uptime: '99.8%'
    },
    {
      id: 2,
      name: 'ACAPS Integration',
      description: 'Interface avec l\'Autorité de Contrôle des Assurances et de la Prévoyance Sociale',
      category: 'government',
      status: 'connected',
      icon: Database,
      color: 'bg-green-500',
      lastSync: '2024-01-15 12:00',
      features: ['Déclarations réglementaires', 'Reporting ACAPS', 'Conformité'],
      apiVersion: 'v1.3',
      uptime: '99.5%'
    },
    {
      id: 3,
      name: 'CMI Payment Gateway',
      description: 'Passerelle de paiement pour les transactions au Maroc',
      category: 'payment',
      status: 'connected',
      icon: CreditCard,
      color: 'bg-purple-500',
      lastSync: '2024-01-15 15:45',
      features: ['Paiements en ligne', 'Cartes bancaires', 'Virements'],
      apiVersion: 'v3.0',
      uptime: '99.9%'
    },
    {
      id: 4,
      name: 'Vérification Immatriculation',
      description: 'API pour vérifier les données d\'immatriculation des véhicules',
      category: 'government',
      status: 'connected',
      icon: Globe,
      color: 'bg-orange-500',
      lastSync: '2024-01-15 16:20',
      features: ['Vérification VIN', 'Données techniques', 'Historique véhicule'],
      apiVersion: 'v2.0',
      uptime: '98.7%'
    },
    {
      id: 5,
      name: 'WhatsApp Business API',
      description: 'Communication client via WhatsApp Business',
      category: 'communication',
      status: 'pending',
      icon: MessageSquare,
      color: 'bg-green-600',
      lastSync: null,
      features: ['Messages automatiques', 'Support client', 'Notifications'],
      apiVersion: 'v1.0',
      uptime: null
    },
    {
      id: 6,
      name: 'SendGrid Email',
      description: 'Service d\'envoi d\'emails transactionnels et marketing',
      category: 'communication',
      status: 'connected',
      icon: Mail,
      color: 'bg-blue-600',
      lastSync: '2024-01-15 17:00',
      features: ['Emails transactionnels', 'Newsletters', 'Analytics'],
      apiVersion: 'v3.0',
      uptime: '99.9%'
    },
    {
      id: 7,
      name: 'Codes Postaux Maroc',
      description: 'Base de données des codes postaux et adresses au Maroc',
      category: 'government',
      status: 'connected',
      icon: Database,
      color: 'bg-indigo-500',
      lastSync: '2024-01-15 08:00',
      features: ['Validation adresses', 'Codes postaux', 'Géolocalisation'],
      apiVersion: 'v1.5',
      uptime: '99.2%'
    },
    {
      id: 8,
      name: 'AXA Claims System',
      description: 'Système de gestion des sinistres AXA',
      category: 'axa',
      status: 'error',
      icon: AlertCircle,
      color: 'bg-red-500',
      lastSync: '2024-01-14 22:15',
      features: ['Déclaration sinistres', 'Suivi dossiers', 'Expertise'],
      apiVersion: 'v1.8',
      uptime: '95.3%'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'disconnected': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return CheckCircle;
      case 'pending': return AlertCircle;
      case 'error': return XCircle;
      default: return XCircle;
    }
  };

  const filteredIntegrations = selectedCategory === 'all' 
    ? integrations 
    : integrations.filter(integration => integration.category === selectedCategory);

  const stats = [
    { title: 'Intégrations actives', value: '8', color: 'bg-green-500' },
    { title: 'En attente', value: '1', color: 'bg-yellow-500' },
    { title: 'Erreurs', value: '1', color: 'bg-red-500' },
    { title: 'Uptime moyen', value: '98.9%', color: 'bg-blue-500' }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Intégrations Métier</h1>
          <p className="text-gray-600 mt-2">Connectez vos systèmes et automatisez vos processus</p>
        </div>
        <div className="flex space-x-4">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Refresh className="h-4 w-4 mr-2" />
            Synchroniser tout
          </button>
          <button className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors">
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle intégration
          </button>
        </div>
      </motion.div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className={`${stat.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                <Database className="h-6 w-6 text-white" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Categories */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <div className="flex space-x-4">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-axa-blue text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>
      </motion.div>

      {/* Integrations Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {filteredIntegrations.map((integration) => {
          const StatusIcon = getStatusIcon(integration.status);
          return (
            <div key={integration.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`${integration.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                    <integration.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{integration.name}</h3>
                    <p className="text-sm text-gray-500">v{integration.apiVersion}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <StatusIcon className={`h-5 w-5 ${
                    integration.status === 'connected' ? 'text-green-500' :
                    integration.status === 'pending' ? 'text-yellow-500' : 'text-red-500'
                  }`} />
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(integration.status)}`}>
                    {integration.status === 'connected' ? 'Connecté' :
                     integration.status === 'pending' ? 'En attente' :
                     integration.status === 'error' ? 'Erreur' : 'Déconnecté'}
                  </span>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-4">{integration.description}</p>

              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Fonctionnalités</h4>
                  <div className="flex flex-wrap gap-1">
                    {integration.features.map((feature, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {integration.status === 'connected' && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Dernière sync:</span>
                      <div className="font-medium text-gray-900">{integration.lastSync}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Uptime:</span>
                      <div className="font-medium text-green-600">{integration.uptime}</div>
                    </div>
                  </div>
                )}

                <div className="flex space-x-2 pt-4 border-t border-gray-200">
                  <button className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <Settings className="h-4 w-4 inline mr-1" />
                    Configurer
                  </button>
                  <button className="flex-1 px-3 py-2 text-sm bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors">
                    <ExternalLink className="h-4 w-4 inline mr-1" />
                    Tester
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </motion.div>

      {/* Available Integrations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-6">Intégrations disponibles</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
            <Smartphone className="h-8 w-8 text-blue-500 mb-3" />
            <h4 className="font-medium text-gray-900 mb-1">SMS Gateway</h4>
            <p className="text-sm text-gray-600">Notifications SMS automatiques</p>
          </div>
          <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
            <Zap className="h-8 w-8 text-yellow-500 mb-3" />
            <h4 className="font-medium text-gray-900 mb-1">Zapier</h4>
            <p className="text-sm text-gray-600">Automatisation workflow</p>
          </div>
          <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
            <Globe className="h-8 w-8 text-green-500 mb-3" />
            <h4 className="font-medium text-gray-900 mb-1">Google Maps</h4>
            <p className="text-sm text-gray-600">Géolocalisation et cartes</p>
          </div>
          <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
            <Database className="h-8 w-8 text-purple-500 mb-3" />
            <h4 className="font-medium text-gray-900 mb-1">Backup Cloud</h4>
            <p className="text-sm text-gray-600">Sauvegarde automatique</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export { Integrations as default };