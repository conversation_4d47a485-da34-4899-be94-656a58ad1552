import { useState, useEffect } from 'react';
import LeadScoringService from '../utils/leadScoringService';
import type { Lead } from '../types/admin';

export function useLeadScoring(lead?: Lead) {
  const [score, setScore] = useState(lead?.score || 0);
  const [scoreHistory, setScoreHistory] = useState([]);
  const [detailedScoring, setDetailedScoring] = useState([]);

  useEffect(() => {
    if (lead) {
      const scoringService = LeadScoringService.getInstance();
      
      // Calculer le nouveau score
      const newScore = scoringService.calculateScore(lead);
      if (newScore !== lead.score) {
        scoringService.updateLeadScore({
          ...lead,
          score: newScore
        });
      }
      
      setScore(newScore);
      setScoreHistory(scoringService.getLeadScoreHistory(lead.id));
      setDetailedScoring(scoringService.getDetailedScoring(lead));
    }
  }, [lead]);

  const recalculateScore = (updatedLead: Lead) => {
    const scoringService = LeadScoringService.getInstance();
    const newScore = scoringService.calculateScore(updatedLead);
    scoringService.updateLeadScore({
      ...updatedLead,
      score: newScore
    });
    return newScore;
  };

  return {
    score,
    scoreHistory,
    detailedScoring,
    recalculateScore
  };
}

export default useLeadScoring;
