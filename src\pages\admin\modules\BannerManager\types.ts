export type BannerType = 'info' | 'warning' | 'success' | 'error' | 'promotion';
export type BannerStatus = 'active' | 'inactive' | 'scheduled' | 'expired';

export interface Banner {
  id: string;
  title: string;
  content: string;
  type: BannerType;
  status: BannerStatus;
  backgroundColor: string;
  textColor: string;
  startDate: string;
  endDate: string;
  showContact: boolean;
  priority: number;
  targetPages: string[];
  views: number;
  clicks: number;
  createdAt: string;
  updatedAt: string;
}

export interface BannerFilter {
  search: string;
  status: BannerStatus | 'all';
  type: BannerType | 'all';
  dateRange: { start: Date; end: Date } | null;
}

export interface BannerStats {
  totalBanners: number;
  activeBanners: number;
  totalViews: number;
  totalClicks: number;
  clickThroughRate: number;
}

export interface BannerEditorState {
  title: string;
  content: string;
  type: BannerType;
  backgroundColor: string;
  textColor: string;
  startDate: string;
  endDate: string;
  showContact: boolean;
  priority: number;
  targetPages: string[];
}
