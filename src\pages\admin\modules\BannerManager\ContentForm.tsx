import React from 'react';
import { Controller } from 'react-hook-form';
import type { BannerType } from './types';

interface ContentFormProps {
  control: any;
  errors: any;
}

export const ContentForm: React.FC<ContentFormProps> = ({ control, errors }) => {
  const bannerTypes: { value: BannerType; label: string }[] = [
    { value: 'info', label: 'Information' },
    { value: 'warning', label: 'Avertissement' },
    { value: 'success', label: 'Succès' },
    { value: 'error', label: 'Erreur' },
    { value: 'promotion', label: 'Promotion' },
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Contenu</h4>
      
      <Controller
        name="title"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Titre
            </label>
            <input
              type="text"
              {...field}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Titre de la bannière"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">
                {errors.title.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="content"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Message
            </label>
            <textarea
              {...field}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Message à afficher dans la bannière"
            />
            {errors.content && (
              <p className="mt-1 text-sm text-red-600">
                {errors.content.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="type"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              {...field}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            >
              {bannerTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="mt-1 text-sm text-red-600">
                {errors.type.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default ContentForm;
