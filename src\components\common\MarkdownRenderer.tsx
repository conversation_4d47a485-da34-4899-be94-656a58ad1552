import React from 'react';

interface MarkdownRendererProps {
  content: string;
}

const convertMarkdownToJSX = (markdown: string): JSX.Element[] => {
  const lines = markdown.split('\n');
  const elements: JSX.Element[] = [];
  let inList = false;
  let listItems: JSX.Element[] = [];

  lines.forEach((line, index) => {
    // Headers
    if (line.startsWith('#')) {
      const level = line.match(/^#+/)[0].length;
      const text = line.replace(/^#+\s/, '');
      const Tag = `h${level}` as keyof JSX.IntrinsicElements;
      elements.push(
        <Tag key={index} className={`text-${5-level}xl font-bold mb-4`}>
          {text}
        </Tag>
      );
      return;
    }

    // Lists
    if (line.match(/^[\*\-\+]\s/)) {
      if (!inList) {
        inList = true;
        listItems = [];
      }
      const text = line.replace(/^[\*\-\+]\s/, '');
      listItems.push(<li key={`${index}-li`} className="mb-1">{text}</li>);
      
      if (index === lines.length - 1 || !lines[index + 1].match(/^[\*\-\+]\s/)) {
        elements.push(<ul key={index} className="list-disc pl-5 mb-4">{listItems}</ul>);
        inList = false;
      }
      return;
    }

    // Bold
    let content = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Italic
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Links
    content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:underline">$1</a>');
    
    // Code
    content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-100 rounded px-1 py-0.5 font-mono text-sm">$1</code>');

    // Empty lines become line breaks
    if (!line.trim()) {
      elements.push(<br key={index} />);
      return;
    }

    // Regular paragraphs
    if (!inList && content.trim()) {
      elements.push(
        <p key={index} className="mb-4" dangerouslySetInnerHTML={{ __html: content }} />
      );
    }
  });

  return elements;
};

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
  return (
    <div className="markdown-body">
      {convertMarkdownToJSX(content)}
    </div>
  );
};
