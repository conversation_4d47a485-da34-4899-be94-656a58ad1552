import { Lead } from '../types/admin';

type NotificationType = 'FOLLOW_UP' | 'TASK_DUE' | 'LEAD_SCORE_CHANGE' | 'QUOTE_EXPIRING';

interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  date: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  read: boolean;
  data?: any;
}

class NotificationManager {
  private static instance: NotificationManager;
  private notifications: Notification[] = [];

  private constructor() {}

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public addNotification(notification: Omit<Notification, 'id' | 'read'>) {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      read: false
    };
    this.notifications.push(newNotification);
    this.triggerNotificationEvent(newNotification);
  }

  private triggerNotificationEvent(notification: Notification) {
    const event = new CustomEvent('newNotification', { detail: notification });
    window.dispatchEvent(event);
  }

  public checkLeadFollowUps(lead: Lead) {
    const today = new Date();
    const nextFollowUpDate = new Date(lead.nextFollowUpDate);
    
    if (nextFollowUpDate.getTime() - today.getTime() <= 24 * 60 * 60 * 1000) {
      this.addNotification({
        type: 'FOLLOW_UP',
        title: 'Suivi de prospect à faire',
        message: `Suivi prévu pour ${lead.firstName} ${lead.lastName}`,
        date: new Date().toISOString(),
        priority: 'HIGH',
        data: { leadId: lead.id }
      });
    }
  }

  public checkLeadScoreChange(lead: Lead, previousScore: number) {
    if (Math.abs(lead.score - previousScore) >= 10) {
      this.addNotification({
        type: 'LEAD_SCORE_CHANGE',
        title: 'Changement important du score',
        message: `Le score de ${lead.firstName} ${lead.lastName} a changé de ${previousScore} à ${lead.score}`,
        date: new Date().toISOString(),
        priority: 'MEDIUM',
        data: { leadId: lead.id, scoreDelta: lead.score - previousScore }
      });
    }
  }

  public getUnreadNotifications(): Notification[] {
    return this.notifications.filter(n => !n.read);
  }

  public markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
    }
  }
}

export { NotificationManager };
export default NotificationManager;
