import React from 'react';
import { Controller } from 'react-hook-form';
import { Target } from 'lucide-react';

interface TargetingFormProps {
  control: any;
  errors: any;
}

export const TargetingForm: React.FC<TargetingFormProps> = ({ control, errors }) => {
  const availablePages = [
    { value: '/', label: 'Accueil' },
    { value: '/products', label: 'Produits' },
    { value: '/services', label: 'Services' },
    { value: '/about', label: 'À propos' },
    { value: '/contact', label: 'Contact' },
    { value: '/blog', label: 'Blog' },
    { value: '/quote', label: 'Devi<PERSON>' },
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Ciblage</h4>
      
      <Controller
        name="targetPages"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Pages cibles
            </label>
            <div className="space-y-2">
              {availablePages.map((page) => (
                <label key={page.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={field.value?.includes(page.value)}
                    onChange={(e) => {
                      const updatedPages = e.target.checked
                        ? [...(field.value || []), page.value]
                        : field.value?.filter((p: string) => p !== page.value) || [];
                      field.onChange(updatedPages);
                    }}
                    className="h-4 w-4 text-axa-blue border-gray-300 rounded focus:ring-axa-blue"
                  />
                  <span className="ml-2 text-sm text-gray-700">{page.label}</span>
                </label>
              ))}
            </div>
            {errors.targetPages && (
              <p className="mt-1 text-sm text-red-600">
                {errors.targetPages.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="showContact"
        control={control}
        render={({ field }) => (
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={field.value}
                onChange={(e) => field.onChange(e.target.checked)}
                className="h-4 w-4 text-axa-blue border-gray-300 rounded focus:ring-axa-blue"
              />
              <span className="ml-2 text-sm text-gray-700">
                Afficher le bouton "Nous contacter"
              </span>
            </label>
          </div>
        )}
      />
    </div>
  );
};

export default TargetingForm;
