import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Header } from './components/layout/Header';
import { Footer } from './components/layout/Footer';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { Home } from './pages/Home';
import { Products } from './pages/Products';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { Quote } from './pages/Quote';
// ...existing code...
import { Blog } from './pages/Blog';

// Lazy loading pour les modules admin
const AdminDashboard = React.lazy(() => import('./pages/admin/AdminDashboard'));
const AdminLogin = React.lazy(() => import('./pages/admin/AdminLogin'));

// Composant de chargement
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-axa-blue"></div>
  </div>
);

function App() {
  return (
    <HelmetProvider>
      <ErrorBoundary>
        <Router>
          <div className="min-h-screen bg-white">
            <Routes>
            <Route path="/admin/login" element={
              <ErrorBoundary>
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminLogin />
                </Suspense>
              </ErrorBoundary>
            } />
            <Route path="/admin/*" element={
              <ErrorBoundary>
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminDashboard />
                </Suspense>
              </ErrorBoundary>
            } />
            <Route path="/*" element={
              <ErrorBoundary>
                <Header />
                <main id="main-content" role="main" tabIndex={-1}>
                  <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/produits" element={<Products />} />
                    <Route path="/a-propos" element={<About />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="/devis" element={<Quote />} />
// ...existing code...
                    <Route path="/actualites" element={<Blog />} />
                  </Routes>
                </main>
                <Footer />
              </ErrorBoundary>
            } />
            </Routes>
          </div>
        </Router>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;