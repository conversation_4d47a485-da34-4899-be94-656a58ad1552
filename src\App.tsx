import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Header } from './components/layout/Header';
import { Footer } from './components/layout/Footer';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { Home } from './pages/Home';
import { Products } from './pages/Products';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { Quote } from './pages/Quote';
// ...existing code...
import { Blog } from './pages/Blog';

// Lazy loading pour les modules admin
const AdminDashboard = React.lazy(() => import('./pages/admin/AdminDashboard'));
const AdminLogin = React.lazy(() => import('./pages/admin/AdminLogin'));

// Composant de chargement
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-axa-blue"></div>
  </div>
);

function App() {
  return (
    <HelmetProvider>
      <ErrorBoundary>
        <Router>
          <div className="min-h-screen bg-white">
            <Routes>
              {/* Routes d'administration */}
              <Route path="/admin/login" element={
                <ErrorBoundary>
                  <Suspense fallback={<LoadingSpinner />}>
                    <AdminLogin />
                  </Suspense>
                </ErrorBoundary>
              } />
              <Route path="/admin/*" element={
                <ErrorBoundary>
                  <Suspense fallback={<LoadingSpinner />}>
                    <AdminDashboard />
                  </Suspense>
                </ErrorBoundary>
              } />

              {/* Routes publiques */}
              <Route path="/" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <Home />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
              <Route path="/produits" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <Products />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
              <Route path="/a-propos" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <About />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
              <Route path="/contact" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <Contact />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
              <Route path="/devis" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <Quote />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
              <Route path="/actualites" element={
                <ErrorBoundary>
                  <Header />
                  <main id="main-content" role="main" tabIndex={-1}>
                    <Blog />
                  </main>
                  <Footer />
                </ErrorBoundary>
              } />
            </Routes>
          </div>
        </Router>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;