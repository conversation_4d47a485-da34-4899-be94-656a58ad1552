import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface ModuleAccess {
  name: string;
  isAccessible: boolean;
  requiredRole: string[];
  description: string;
}

export const useAdminAccess = (moduleName?: string) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [moduleAccess, setModuleAccess] = useState<ModuleAccess[]>([]);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const token = localStorage.getItem('token');

        if (!token) {
          navigate('/admin/login');
          return;
        }

        // Vérifier si le token est toujours valide
        const response = await fetch('/api/admin/verify-token', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (!response.ok) {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/admin/login');
          return;
        }

        // Récupérer les accès aux modules
        const accessResponse = await fetch('/api/admin/module-access', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (accessResponse.ok) {
          const moduleAccessData = await accessResponse.json();
          setModuleAccess(moduleAccessData);

          if (moduleName) {
            const moduleHasAccess = moduleAccessData.some(
              (module: ModuleAccess) => 
                module.name === moduleName && 
                module.isAccessible && 
                module.requiredRole.includes(user.role)
            );
            setHasAccess(moduleHasAccess);
          } else {
            setHasAccess(true);
          }
        }

      } catch (error) {
        setError('Erreur lors de la vérification des accès');
        console.error('Erreur de vérification des accès:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, [moduleName, navigate]);

  const checkModuleAccess = (moduleName: string) => {
    return moduleAccess.some(
      (module) => module.name === moduleName && module.isAccessible
    );
  };

  const getModuleInfo = (moduleName: string) => {
    return moduleAccess.find((module) => module.name === moduleName);
  };

  return {
    isLoading,
    error,
    hasAccess,
    moduleAccess,
    checkModuleAccess,
    getModuleInfo
  };
};

export default useAdminAccess;
