-- ===========================================
-- SCRIPT DE CONFIGURATION MYSQL HOSTINGER
-- ===========================================

-- Créer la base de données (si elle n'existe pas)
CREATE DATABASE IF NOT EXISTS mtp_production 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Créer un utilisateur dédié (optionnel, selon les permissions Hostinger)
-- CREATE USER IF NOT EXISTS 'mtp_user'@'localhost' IDENTIFIED BY 'your_secure_password';
-- GRANT ALL PRIVILEGES ON mtp_production.* TO 'mtp_user'@'localhost';

-- Utiliser la base de données
USE mtp_production;

-- Configuration des paramètres MySQL pour optimiser les performances
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL max_connections = 100;
SET GLOBAL query_cache_size = 16M;
SET GLOBAL query_cache_type = 1;

-- Configuration du timezone
SET GLOBAL time_zone = '+01:00'; -- Heure du Maroc

-- Optimisations pour Prisma
SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- Vérifier la configuration
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';
SHOW VARIABLES LIKE 'time_zone';

-- Afficher les informations de la base
SELECT 
    SCHEMA_NAME as 'Database',
    DEFAULT_CHARACTER_SET_NAME as 'Charset',
    DEFAULT_COLLATION_NAME as 'Collation'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'mtp_production';

-- Commandes utiles pour le monitoring
-- SHOW PROCESSLIST;
-- SHOW STATUS LIKE 'Connections';
-- SHOW STATUS LIKE 'Threads_connected';
-- SHOW STATUS LIKE 'Queries';

-- Flush des privilèges
FLUSH PRIVILEGES;
