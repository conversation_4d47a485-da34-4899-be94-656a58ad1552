import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AlertTriangle,
  Info,
  Shield,
  Search,
  Filter,
  Download,
  Calendar,
  ChevronDown,
  RefreshCw
} from 'lucide-react';
import type { AdminLog, LogLevel } from '../../services/adminLogger';
import { useAdminLogger } from '../../hooks/useAdminLogger';

const LogLevelIcon: Record<LogLevel, React.ReactNode> = {
  info: <Info className="h-5 w-5 text-blue-500" />,
  warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
  error: <AlertTriangle className="h-5 w-5 text-red-500" />,
  security: <Shield className="h-5 w-5 text-purple-500" />
};

const LogLevelColor: Record<LogLevel, string> = {
  info: 'bg-blue-50 text-blue-800 border-blue-200',
  warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
  error: 'bg-red-50 text-red-800 border-red-200',
  security: 'bg-purple-50 text-purple-800 border-purple-200'
};

const AdminLogs: React.FC = () => {
  const [logs, setLogs] = useState<AdminLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    level: 'all',
    module: 'all',
    startDate: '',
    endDate: '',
    search: ''
  });
  const { logError } = useAdminLogger('logs');

  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/logs', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des logs');
      }

      const data = await response.json();
      setLogs(data);
    } catch (error) {
      logError('read', 'Erreur lors du chargement des logs', { error });
    } finally {
      setIsLoading(false);
    }
  };

  const filterLogs = () => {
    return logs.filter(log => {
      if (filters.level !== 'all' && log.level !== filters.level) return false;
      if (filters.module !== 'all' && log.module !== filters.module) return false;
      if (filters.startDate && new Date(log.timestamp) < new Date(filters.startDate)) return false;
      if (filters.endDate && new Date(log.timestamp) > new Date(filters.endDate)) return false;
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        return (
          log.message.toLowerCase().includes(searchTerm) ||
          log.userEmail.toLowerCase().includes(searchTerm) ||
          log.action.toLowerCase().includes(searchTerm)
        );
      }
      return true;
    });
  };

  const exportLogs = () => {
    const filteredLogs = filterLogs();
    const csv = [
      ['Date', 'Niveau', 'Action', 'Module', 'Message', 'Utilisateur', 'Rôle', 'IP'].join(','),
      ...filteredLogs.map(log => [
        log.timestamp,
        log.level,
        log.action,
        log.module,
        `"${log.message.replace(/"/g, '""')}"`,
        log.userEmail,
        log.userRole,
        log.ipAddress
      ].join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `admin_logs_${new Date().toISOString()}.csv`;
    link.click();
  };

  const getUniqueModules = () => {
    return Array.from(new Set(logs.map(log => log.module)));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Journaux d'Administration</h1>
          <p className="text-gray-600 mt-2">
            Consultez et analysez l'activité administrative
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadLogs}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
          <button
            onClick={exportLogs}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-axa-blue hover:bg-blue-800"
          >
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Rechercher dans les logs..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            />
            <Search className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
          </div>
          <select
            value={filters.level}
            onChange={(e) => setFilters({ ...filters, level: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="all">Tous les niveaux</option>
            <option value="info">Information</option>
            <option value="warning">Avertissement</option>
            <option value="error">Erreur</option>
            <option value="security">Sécurité</option>
          </select>
          <select
            value={filters.module}
            onChange={(e) => setFilters({ ...filters, module: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="all">Tous les modules</option>
            {getUniqueModules().map(module => (
              <option key={module} value={module}>{module}</option>
            ))}
          </select>
          <div className="flex space-x-2">
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg"
            />
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg"
            />
          </div>
        </div>
      </div>

      {/* Liste des logs */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Niveau
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Module
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Message
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisateur
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <AnimatePresence>
                {filterLogs().map(log => (
                  <motion.tr
                    key={log.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${LogLevelColor[log.level]}`}>
                        {LogLevelIcon[log.level]}
                        <span className="ml-1 capitalize">{log.level}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(log.timestamp).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.module}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.action}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {log.message}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.userEmail}
                      <span className="ml-2 text-xs text-gray-400">({log.userRole})</span>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdminLogs;
