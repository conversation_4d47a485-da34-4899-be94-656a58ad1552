import React from 'react';
import { motion } from 'framer-motion';
import { X, Phone, Mail, Clock } from 'lucide-react';

interface TopBannerProps {
  isVisible: boolean;
  onClose: () => void;
  content: {
    text: string;
    type: 'info' | 'promotion' | 'alert';
    backgroundColor: string;
    textColor: string;
    showContact: boolean;
  };
}

export const TopBanner: React.FC<TopBannerProps> = ({ isVisible, onClose, content }) => {
  if (!isVisible) return null;

  const getBannerStyle = () => {
    switch (content.type) {
      case 'promotion':
        return 'bg-gradient-to-r from-axa-red to-red-600';
      case 'alert':
        return 'bg-gradient-to-r from-yellow-500 to-orange-500';
      default:
        return 'bg-gradient-to-r from-axa-blue to-blue-700';
    }
  };

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      className={`${content.backgroundColor || getBannerStyle()} text-white relative overflow-hidden`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">{content.text}</span>
            </div>
            {content.showContact && (
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>+212 5XX-XXXXXX</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Lun-Ven 8h30-18h</span>
                </div>
              </div>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  );
};