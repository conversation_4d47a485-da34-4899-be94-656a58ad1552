import React from 'react';
import { Controller } from 'react-hook-form';

interface MetadataPanelProps {
  control: any;
  errors: any;
}

export const MetadataPanel: React.FC<MetadataPanelProps> = ({ control, errors }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Métadonnées</h4>
      
      <Controller
        name="excerpt"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Extrait
            </label>
            <textarea
              {...field}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Bref résumé du contenu"
            />
            <div className="mt-1 text-sm text-gray-500">
              {field.value?.length || 0}/500 caractères
            </div>
            {errors.excerpt && (
              <p className="mt-1 text-sm text-red-600">
                {errors.excerpt.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="featuredImage"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image à la une
            </label>
            <input
              {...field}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="URL de l'image"
            />
            {field.value && (
              <img
                src={field.value}
                alt="Preview"
                className="mt-2 w-full h-32 object-cover rounded-lg"
              />
            )}
            {errors.featuredImage && (
              <p className="mt-1 text-sm text-red-600">
                {errors.featuredImage.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default MetadataPanel;
