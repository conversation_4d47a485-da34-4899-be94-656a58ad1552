import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest, optionalAuth } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createPostSchema = Joi.object({
  title: Joi.string().min(1).max(200).required(),
  slug: Joi.string().min(1).max(200).required(),
  content: Joi.string().required(),
  excerpt: Joi.string().max(500),
  image: Joi.string().uri(),
  status: Joi.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED').default('DRAFT'),
  tags: Joi.array().items(Joi.string()),
  category: Joi.string().max(50),
  seo: Joi.object({
    title: Joi.string().max(60),
    description: Joi.string().max(160),
    keywords: Joi.string().max(255),
  }),
});

const updatePostSchema = Joi.object({
  title: Joi.string().min(1).max(200),
  slug: Joi.string().min(1).max(200),
  content: Joi.string(),
  excerpt: Joi.string().max(500),
  image: Joi.string().uri(),
  status: Joi.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED'),
  tags: Joi.array().items(Joi.string()),
  category: Joi.string().max(50),
  seo: Joi.object({
    title: Joi.string().max(60),
    description: Joi.string().max(160),
    keywords: Joi.string().max(255),
  }),
});

/**
 * GET /api/blog
 * Récupérer tous les articles (admin) ou publiés (public)
 */
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const category = req.query.category as string;
  const search = req.query.search as string;
  const isAdmin = req.user && ['ADMIN', 'SUPER_ADMIN'].includes(req.user.role);

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  // Si pas admin, ne montrer que les articles publiés
  if (!isAdmin) {
    where.status = 'PUBLISHED';
  } else if (status && status !== 'all') {
    where.status = status;
  }
  
  if (category) where.category = category;
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
      { excerpt: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [posts, total] = await Promise.all([
    prisma.blogPost.findMany({
      where,
      skip,
      take: limit,
      orderBy: { publishedAt: 'desc' },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        image: true,
        status: true,
        publishedAt: true,
        tags: true,
        category: true,
        createdAt: true,
        updatedAt: true,
        ...(isAdmin && { content: true, seo: true }),
      },
    }),
    prisma.blogPost.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/blog/slug/:slug
 * Récupérer un article par son slug (public)
 */
router.get('/slug/:slug', asyncHandler(async (req, res) => {
  const post = await prisma.blogPost.findUnique({
    where: { 
      slug: req.params.slug,
      status: 'PUBLISHED',
    },
  });

  if (!post) {
    throw createError('Article non trouvé', 404);
  }

  res.json({
    success: true,
    data: { post },
  });
}));

/**
 * GET /api/blog/:id
 * Récupérer un article spécifique (admin)
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const post = await prisma.blogPost.findUnique({
    where: { id: req.params.id },
  });

  if (!post) {
    throw createError('Article non trouvé', 404);
  }

  res.json({
    success: true,
    data: { post },
  });
}));

/**
 * POST /api/blog
 * Créer un nouvel article
 */
router.post('/', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createPostSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le slug est unique
  const existingPost = await prisma.blogPost.findUnique({
    where: { slug: value.slug },
  });

  if (existingPost) {
    throw createError('Un article avec ce slug existe déjà', 409);
  }

  // Créer l'article
  const post = await prisma.blogPost.create({
    data: {
      ...value,
      publishedAt: value.status === 'PUBLISHED' ? new Date() : null,
    },
  });

  logger.info(`Blog post created: ${post.title} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Article créé avec succès',
    data: { post },
  });
}));

/**
 * PUT /api/blog/:id
 * Mettre à jour un article
 */
router.put('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updatePostSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que l'article existe
  const existingPost = await prisma.blogPost.findUnique({
    where: { id: req.params.id },
  });

  if (!existingPost) {
    throw createError('Article non trouvé', 404);
  }

  // Vérifier l'unicité du slug si modifié
  if (value.slug && value.slug !== existingPost.slug) {
    const slugExists = await prisma.blogPost.findUnique({
      where: { slug: value.slug },
    });

    if (slugExists) {
      throw createError('Un article avec ce slug existe déjà', 409);
    }
  }

  // Mettre à jour l'article
  const updateData: any = { ...value };
  
  // Gérer la date de publication
  if (value.status === 'PUBLISHED' && existingPost.status !== 'PUBLISHED') {
    updateData.publishedAt = new Date();
  } else if (value.status !== 'PUBLISHED') {
    updateData.publishedAt = null;
  }

  const post = await prisma.blogPost.update({
    where: { id: req.params.id },
    data: updateData,
  });

  logger.info(`Blog post updated: ${post.title} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Article mis à jour avec succès',
    data: { post },
  });
}));

/**
 * DELETE /api/blog/:id
 * Supprimer un article
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const post = await prisma.blogPost.findUnique({
    where: { id: req.params.id },
  });

  if (!post) {
    throw createError('Article non trouvé', 404);
  }

  await prisma.blogPost.delete({
    where: { id: req.params.id },
  });

  logger.info(`Blog post deleted: ${post.title} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Article supprimé avec succès',
  });
}));

/**
 * GET /api/blog/categories/list
 * Récupérer toutes les catégories
 */
router.get('/categories/list', asyncHandler(async (req, res) => {
  const categories = await prisma.blogPost.findMany({
    where: { 
      status: 'PUBLISHED',
      category: { not: null },
    },
    select: { category: true },
    distinct: ['category'],
  });

  const categoryList = categories
    .map(item => item.category)
    .filter(Boolean)
    .sort();

  res.json({
    success: true,
    data: { categories: categoryList },
  });
}));

/**
 * GET /api/blog/tags/list
 * Récupérer tous les tags
 */
router.get('/tags/list', asyncHandler(async (req, res) => {
  const posts = await prisma.blogPost.findMany({
    where: { 
      status: 'PUBLISHED',
      tags: { not: null },
    },
    select: { tags: true },
  });

  const allTags = posts
    .flatMap(post => post.tags as string[] || [])
    .filter((tag, index, array) => array.indexOf(tag) === index)
    .sort();

  res.json({
    success: true,
    data: { tags: allTags },
  });
}));

/**
 * GET /api/blog/recent/:count
 * Récupérer les articles récents
 */
router.get('/recent/:count', asyncHandler(async (req, res) => {
  const count = parseInt(req.params.count) || 5;

  const posts = await prisma.blogPost.findMany({
    where: { status: 'PUBLISHED' },
    take: count,
    orderBy: { publishedAt: 'desc' },
    select: {
      id: true,
      title: true,
      slug: true,
      excerpt: true,
      image: true,
      publishedAt: true,
      category: true,
    },
  });

  res.json({
    success: true,
    data: { posts },
  });
}));

export default router;
