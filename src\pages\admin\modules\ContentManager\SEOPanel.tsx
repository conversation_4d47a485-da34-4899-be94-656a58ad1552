import React from 'react';
import { Controller } from 'react-hook-form';

interface SEOPanelProps {
  control: any;
  errors: any;
}

export const SEOPanel: React.FC<SEOPanelProps> = ({ control, errors }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">SEO</h4>
      
      <Controller
        name="seo.title"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Titre SEO
            </label>
            <input
              {...field}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Titre optimisé pour les moteurs de recherche"
            />
            <div className="mt-1 text-sm text-gray-500">
              {field.value?.length || 0}/60 caractères
            </div>
            {errors.seo?.title && (
              <p className="mt-1 text-sm text-red-600">
                {errors.seo.title.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="seo.description"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description SEO
            </label>
            <textarea
              {...field}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Description qui apparaîtra dans les résultats de recherche"
            />
            <div className="mt-1 text-sm text-gray-500">
              {field.value?.length || 0}/160 caractères
            </div>
            {errors.seo?.description && (
              <p className="mt-1 text-sm text-red-600">
                {errors.seo.description.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="seo.keywords"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mots-clés
            </label>
            <input
              {...field}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              placeholder="Mots-clés séparés par des virgules"
            />
            {errors.seo?.keywords && (
              <p className="mt-1 text-sm text-red-600">
                {errors.seo.keywords.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default SEOPanel;
