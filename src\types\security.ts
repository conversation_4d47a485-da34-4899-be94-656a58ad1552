export type SecurityLevel = 'WARNING' | 'ERROR' | 'CRITICAL';

export interface SecurityCheck {
  threatLevel: 0 | 1 | 2 | 3;
  message: string;
  requiresAdditionalAuth: boolean;
  details?: Record<string, unknown>;
}

export interface SecurityEvent {
  type: 'ACCESS_ATTEMPT' | 'PERMISSION_CHANGE' | 'SECURITY_ALERT' | 'SUSPICIOUS_ACTIVITY';
  timestamp: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  module: string;
  userId: string | null;
  ip: string;
  details: {
    authorized: boolean;
    requiredPermissions?: string[];
    path?: string;
  };
  metadata: {
    userAgent: string;
    location: unknown;
    device: {
      type: string;
      os: string;
      browser: string;
    };
  };
}

export interface SecurityService {
  checkRouteAccess(path: string): Promise<SecurityCheck>;
  processSecurityEvent(event: SecurityEvent): Promise<void>;
  validateRequest(request: unknown): Promise<boolean>;
  isBlacklisted(ip: string): Promise<boolean>;
  getSecurityMetrics(): Promise<Record<string, number>>;
}
