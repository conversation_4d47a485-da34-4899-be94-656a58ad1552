import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '@/middleware/auth';
import { emailRateLimiterMiddleware } from '@/middleware/rateLimiter';
import { sendEmail } from '@/utils/email';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createLeadSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^(\+212|0)[5-7]\d{8}$/).required().messages({
    'string.pattern.base': 'Numéro de téléphone marocain invalide',
  }),
  product: Joi.string().valid('Assurance Auto', 'Assurance Habitation', 'Assurance Santé', 'Prévoyance', 'Épargne Retraite').required(),
  source: Joi.string().max(50).required(),
  city: Joi.string().max(50),
  message: Joi.string().max(1000),
});

const updateLeadSchema = Joi.object({
  name: Joi.string().min(2).max(100),
  email: Joi.string().email(),
  phone: Joi.string().pattern(/^(\+212|0)[5-7]\d{8}$/),
  product: Joi.string().valid('Assurance Auto', 'Assurance Habitation', 'Assurance Santé', 'Prévoyance', 'Épargne Retraite'),
  source: Joi.string().max(50),
  city: Joi.string().max(50),
  message: Joi.string().max(1000),
  status: Joi.string().valid('NEW', 'CONTACTED', 'QUALIFIED', 'CONVERTED', 'LOST'),
  assignedTo: Joi.string().uuid().allow(null),
});

/**
 * POST /api/leads
 * Créer un nouveau lead (public)
 */
router.post('/', emailRateLimiterMiddleware, asyncHandler(async (req, res) => {
  // Validation des données
  const { error, value } = createLeadSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Créer le lead
  const lead = await prisma.lead.create({
    data: {
      ...value,
      email: value.email.toLowerCase(),
    },
  });

  // Envoyer un email de notification à l'équipe
  try {
    await sendEmail({
      to: process.env.NOTIFICATION_EMAIL || '<EMAIL>',
      subject: `Nouveau lead: ${value.product}`,
      template: 'new-lead',
      data: {
        lead: value,
        adminUrl: `${process.env.FRONTEND_URL}/admin/leads/${lead.id}`,
      },
    });
  } catch (emailError) {
    logger.error('Failed to send lead notification email:', emailError);
  }

  // Envoyer un email de confirmation au client
  try {
    await sendEmail({
      to: value.email,
      subject: 'Demande de devis reçue - MOUMEN TECHNIQUE ET PREVOYANCE',
      template: 'lead-confirmation',
      data: {
        name: value.name,
        product: value.product,
      },
    });
  } catch (emailError) {
    logger.error('Failed to send lead confirmation email:', emailError);
  }

  logger.info(`New lead created: ${value.email} for ${value.product}`);

  res.status(201).json({
    success: true,
    message: 'Demande de devis envoyée avec succès',
    data: { lead: { id: lead.id } },
  });
}));

/**
 * GET /api/leads
 * Récupérer tous les leads (authentifié)
 */
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const product = req.query.product as string;
  const search = req.query.search as string;

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  if (status) where.status = status;
  if (product) where.product = product;
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search } },
    ];
  }

  // Si l'utilisateur n'est pas admin, ne voir que ses leads assignés
  if (req.user?.role === 'USER') {
    where.assignedTo = req.user.id;
  }

  const [leads, total] = await Promise.all([
    prisma.lead.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        assignedUser: {
          select: { id: true, name: true, email: true },
        },
      },
    }),
    prisma.lead.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      leads,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/leads/:id
 * Récupérer un lead spécifique
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const lead = await prisma.lead.findUnique({
    where: { id: req.params.id },
    include: {
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  if (!lead) {
    throw createError('Lead non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && lead.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  res.json({
    success: true,
    data: { lead },
  });
}));

/**
 * PUT /api/leads/:id
 * Mettre à jour un lead
 */
router.put('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updateLeadSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le lead existe
  const existingLead = await prisma.lead.findUnique({
    where: { id: req.params.id },
  });

  if (!existingLead) {
    throw createError('Lead non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && existingLead.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  // Mettre à jour le lead
  const lead = await prisma.lead.update({
    where: { id: req.params.id },
    data: value,
    include: {
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Lead updated: ${lead.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Lead mis à jour avec succès',
    data: { lead },
  });
}));

/**
 * DELETE /api/leads/:id
 * Supprimer un lead (admin seulement)
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const lead = await prisma.lead.findUnique({
    where: { id: req.params.id },
  });

  if (!lead) {
    throw createError('Lead non trouvé', 404);
  }

  await prisma.lead.delete({
    where: { id: req.params.id },
  });

  logger.info(`Lead deleted: ${lead.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Lead supprimé avec succès',
  });
}));

/**
 * POST /api/leads/:id/assign
 * Assigner un lead à un utilisateur
 */
router.post('/:id/assign', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.body;

  if (userId) {
    // Vérifier que l'utilisateur existe
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw createError('Utilisateur non trouvé', 404);
    }
  }

  const lead = await prisma.lead.update({
    where: { id: req.params.id },
    data: { assignedTo: userId || null },
    include: {
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Lead ${lead.id} assigned to ${userId || 'unassigned'} by ${req.user?.email}`);

  res.json({
    success: true,
    message: userId ? 'Lead assigné avec succès' : 'Lead désassigné avec succès',
    data: { lead },
  });
}));

export default router;
