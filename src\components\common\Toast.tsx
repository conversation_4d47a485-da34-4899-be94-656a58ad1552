import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, AlertCircle, X } from 'lucide-react';

interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  onClose: () => void;
  duration?: number;
}

export const Toast: React.FC<ToastProps> = ({ message, type, onClose, duration = 3000 }) => {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [onClose, duration]);

  const getStyles = () => {
    switch (type) {
      case 'success':
        return { bg: 'bg-green-500', icon: <CheckCircle className="h-5 w-5" /> };
      case 'error':
        return { bg: 'bg-red-500', icon: <AlertCircle className="h-5 w-5" /> };
      case 'warning':
        return { bg: 'bg-yellow-500', icon: <AlertCircle className="h-5 w-5" /> };
      case 'info':
      default:
        return { bg: 'bg-blue-500', icon: <AlertCircle className="h-5 w-5" /> };
    }
  };

  const { bg, icon } = getStyles();

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50, scale: 0.3 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.3 }}
        className={`fixed top-4 right-4 z-50 flex items-center space-x-2 px-4 py-3 rounded-lg shadow-lg ${bg} text-white`}
      >
        {icon}
        <span className="font-medium">{message}</span>
        <button onClick={onClose} className="ml-2 hover:opacity-75">
          <X className="h-4 w-4" />
        </button>
      </motion.div>
    </AnimatePresence>
  );
};