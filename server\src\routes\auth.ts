import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authRateLimiterMiddleware } from '@/middleware/rateLimiter';
import { authenticate, AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Email invalide',
    'any.required': 'Email requis',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Mot de passe trop court',
    'any.required': 'Mot de passe requis',
  }),
});

const registerSchema = Joi.object({
  name: Joi.string().min(2).max(50).required().messages({
    'string.min': 'Nom trop court',
    'string.max': 'Nom trop long',
    'any.required': 'Nom requis',
  }),
  email: Joi.string().email().required().messages({
    'string.email': 'Email invalide',
    'any.required': 'Email requis',
  }),
  password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')).required().messages({
    'string.min': 'Mot de passe trop court (minimum 8 caractères)',
    'string.pattern.base': 'Mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial',
    'any.required': 'Mot de passe requis',
  }),
});

/**
 * POST /api/auth/login
 * Connexion utilisateur
 */
router.post('/login', authRateLimiterMiddleware, asyncHandler(async (req, res) => {
  // Validation des données
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  const { email, password } = value;

  // Vérifier si l'utilisateur existe
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
  });

  if (!user || !user.isActive) {
    throw createError('Email ou mot de passe incorrect', 401);
  }

  // Vérifier le mot de passe
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    throw createError('Email ou mot de passe incorrect', 401);
  }

  // Générer le token JWT
  if (!process.env.JWT_SECRET) {
    throw createError('Configuration serveur manquante', 500);
  }

  const token = jwt.sign(
    { userId: user.id, email: user.email },
    process.env.JWT_SECRET as string,
    { expiresIn: '7d' }
  );

  // Créer une session
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7 jours

  await prisma.session.create({
    data: {
      userId: user.id,
      token,
      expiresAt,
    },
  });

  logger.info(`User logged in: ${user.email}`);

  res.json({
    success: true,
    message: 'Connexion réussie',
    data: {
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    },
  });
}));

/**
 * POST /api/auth/register
 * Inscription utilisateur (admin seulement)
 */
router.post('/register', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Vérifier les permissions
  if (req.user?.role !== 'ADMIN' && req.user?.role !== 'SUPER_ADMIN') {
    throw createError('Permissions insuffisantes', 403);
  }

  // Validation des données
  const { error, value } = registerSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  const { name, email, password } = value;

  // Vérifier si l'utilisateur existe déjà
  const existingUser = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
  });

  if (existingUser) {
    throw createError('Un utilisateur avec cet email existe déjà', 409);
  }

  // Hasher le mot de passe
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  // Créer l'utilisateur
  const user = await prisma.user.create({
    data: {
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      role: 'USER',
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
    },
  });

  logger.info(`New user registered: ${user.email} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'Utilisateur créé avec succès',
    data: { user },
  });
}));

/**
 * POST /api/auth/logout
 * Déconnexion utilisateur
 */
router.post('/logout', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.substring(7);

  if (token) {
    // Supprimer la session
    await prisma.session.deleteMany({
      where: { token },
    });
  }

  logger.info(`User logged out: ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Déconnexion réussie',
  });
}));

/**
 * GET /api/auth/me
 * Informations utilisateur connecté
 */
router.get('/me', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  res.json({
    success: true,
    data: { user },
  });
}));

/**
 * PUT /api/auth/profile
 * Mise à jour du profil utilisateur
 */
router.put('/profile', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const updateSchema = Joi.object({
    name: Joi.string().min(2).max(50),
    email: Joi.string().email(),
  });

  const { error, value } = updateSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  const updatedUser = await prisma.user.update({
    where: { id: req.user!.id },
    data: value,
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      updatedAt: true,
    },
  });

  logger.info(`User profile updated: ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Profil mis à jour avec succès',
    data: { user: updatedUser },
  });
}));

export default router;
