import React from 'react';
import { Edit, Trash2, Eye } from 'lucide-react';
import type { ContentItem, ViewMode } from './types';

interface ContentListProps {
  items: ContentItem[];
  viewMode: ViewMode;
  onEdit: (item: ContentItem) => void;
  onDelete: (item: ContentItem) => void;
  onPreview: (item: ContentItem) => void;
}

export const ContentList: React.FC<ContentListProps> = ({
  items,
  viewMode,
  onEdit,
  onDelete,
  onPreview,
}) => {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'archived':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item) => (
          <div
            key={item.id}
            className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
          >
            {item.featuredImage ? (
              <img
                src={item.featuredImage}
                alt={item.title}
                className="w-full h-48 object-cover rounded-t-lg"
              />
            ) : (
              <div className="w-full h-48 bg-gray-100 flex items-center justify-center rounded-t-lg">
                <span className="text-gray-400">Aucune image</span>
              </div>
            )}
            
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-500 mb-2">
                    {new Date(item.updatedAt).toLocaleDateString('fr-FR')}
                  </p>
                </div>
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeColor(
                    item.status
                  )}`}
                >
                  {item.status}
                </span>
              </div>
              
              {item.excerpt && (
                <p className="text-gray-600 text-sm mt-2 line-clamp-2">
                  {item.excerpt}
                </p>
              )}

              <div className="flex items-center justify-end space-x-2 mt-4">
                <button
                  onClick={() => onPreview(item)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onEdit(item)}
                  className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onDelete(item)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {items.map((item) => (
        <div
          key={item.id}
          className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-shadow"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  {item.title}
                </h3>
                <span
                  className={`ml-3 px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeColor(
                    item.status
                  )}`}
                >
                  {item.status}
                </span>
              </div>
              
              {item.excerpt && (
                <p className="text-gray-600 text-sm mt-1">{item.excerpt}</p>
              )}
              
              <div className="flex items-center text-sm text-gray-500 mt-2">
                <span>
                  Mis à jour le{' '}
                  {new Date(item.updatedAt).toLocaleDateString('fr-FR')}
                </span>
                {item.category && (
                  <>
                    <span className="mx-2">•</span>
                    <span>{item.category}</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => onPreview(item)}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <Eye className="h-4 w-4" />
              </button>
              <button
                onClick={() => onEdit(item)}
                className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => onDelete(item)}
                className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ContentList;
