import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { 
  Car, 
  Home as HomeIcon, 
  Heart, 
  Shield, 
  PiggyBank, 
  Building2,
  ArrowRight,
  ArrowLeft,
  Calculator,
  CheckCircle,
  FileText,
  Phone
} from 'lucide-react';

interface QuoteForm {
  productType: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    birthDate: string;
    city: string;
  };
  productDetails: Record<string, string | number | undefined>;
}

export const Quote: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [quoteGenerated, setQuoteGenerated] = useState(false);
  
  const { register, handleSubmit, formState: { errors } } = useForm<QuoteForm>();

  const products = [
    {
      id: 'auto',
      name: 'Assurance Auto',
      icon: Car,
      description: 'Protection complète pour votre véhicule',
      color: 'bg-blue-500'
    },
    {
      id: 'habitation',
      name: 'Assurance Habitation',
      icon: HomeIcon,
      description: 'Sécurisez votre foyer et vos biens',
      color: 'bg-green-500'
    },
    {
      id: 'sante',
      name: 'Assurance Santé',
      icon: Heart,
      description: 'Votre santé, notre priorité',
      color: 'bg-red-500'
    },
    {
      id: 'prevoyance',
      name: 'Prévoyance',
      icon: Shield,
      description: 'Protégez votre famille et votre avenir',
      color: 'bg-purple-500'
    },
    {
      id: 'epargne',
      name: 'Épargne Retraite',
      icon: PiggyBank,
      description: 'Préparez sereinement votre retraite',
      color: 'bg-yellow-500'
    },
    {
      id: 'pro',
      name: 'Assurance Professionnelle',
      icon: Building2,
      description: 'Solutions sur mesure pour votre entreprise',
      color: 'bg-indigo-500'
    }
  ];

  const steps = [
    { number: 1, title: 'Type d\'assurance', description: 'Choisissez votre produit' },
    { number: 2, title: 'Informations personnelles', description: 'Vos coordonnées' },
    { number: 3, title: 'Détails du produit', description: 'Spécificités de votre demande' },
    { number: 4, title: 'Votre devis', description: 'Résultat personnalisé' }
  ];

  const onSubmit = (data: QuoteForm) => {
    console.log('Quote form submitted:', data);
    setQuoteGenerated(true);
    setCurrentStep(4);
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const selectProduct = (productId: string) => {
    setSelectedProduct(productId);
    nextStep();
  };

  const renderProductSpecificFields = () => {
    switch (selectedProduct) {
      case 'auto':
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-axa-blue mb-4">
              Informations sur votre véhicule
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Marque du véhicule *
                </label>
                <select
                  {...register('productDetails.brand', { required: 'La marque est requise' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="">Sélectionnez une marque</option>
                  <option value="renault">Renault</option>
                  <option value="peugeot">Peugeot</option>
                  <option value="citroen">Citroën</option>
                  <option value="dacia">Dacia</option>
                  <option value="toyota">Toyota</option>
                  <option value="hyundai">Hyundai</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Modèle *
                </label>
                <input
                  type="text"
                  {...register('productDetails.model', { required: 'Le modèle est requis' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  placeholder="Ex: Clio, 208, C3..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Année de mise en circulation *
                </label>
                <input
                  type="number"
                  min="1990"
                  max="2024"
                  {...register('productDetails.year', { required: 'L\'année est requise' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  placeholder="2020"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Puissance fiscale *
                </label>
                <input
                  type="number"
                  {...register('productDetails.power', { required: 'La puissance est requise' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  placeholder="7"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Usage du véhicule *
              </label>
              <select
                {...register('productDetails.usage', { required: 'L\'usage est requis' })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              >
                <option value="">Sélectionnez l'usage</option>
                <option value="personnel">Personnel</option>
                <option value="professionnel">Professionnel</option>
                <option value="mixte">Mixte</option>
              </select>
            </div>
          </div>
        );
      
      case 'habitation':
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-axa-blue mb-4">
              Informations sur votre logement
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type de logement *
                </label>
                <select
                  {...register('productDetails.propertyType', { required: 'Le type est requis' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="">Sélectionnez le type</option>
                  <option value="appartement">Appartement</option>
                  <option value="maison">Maison</option>
                  <option value="villa">Villa</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Surface (m²) *
                </label>
                <input
                  type="number"
                  {...register('productDetails.surface', { required: 'La surface est requise' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  placeholder="120"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de pièces *
                </label>
                <input
                  type="number"
                  {...register('productDetails.rooms', { required: 'Le nombre de pièces est requis' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  placeholder="4"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Statut d'occupation *
                </label>
                <select
                  {...register('productDetails.occupancy', { required: 'Le statut est requis' })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                >
                  <option value="">Sélectionnez le statut</option>
                  <option value="proprietaire">Propriétaire occupant</option>
                  <option value="locataire">Locataire</option>
                  <option value="proprietaire-bailleur">Propriétaire bailleur</option>
                </select>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-axa-blue mb-4">
              Informations complémentaires
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Décrivez vos besoins spécifiques
              </label>
              <textarea
                {...register('productDetails.description')}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent resize-none"
                placeholder="Décrivez vos besoins, attentes ou questions spécifiques..."
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="py-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-axa-blue to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              Demande de Devis
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Obtenez votre devis personnalisé en quelques minutes. Simple, rapide et sans engagement.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Progress Steps */}
      <section className="py-12 bg-axa-gray">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${
                      currentStep >= step.number
                        ? 'bg-axa-blue text-white'
                        : 'bg-white text-gray-400 border-2 border-gray-300'
                    }`}
                  >
                    {currentStep > step.number ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      step.number
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`flex-1 h-1 mx-4 ${
                      currentStep > step.number ? 'bg-axa-blue' : 'bg-gray-300'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Form Content */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Step 1: Product Selection */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-axa-blue mb-4">
                    Quel type d'assurance vous intéresse ?
                  </h2>
                  <p className="text-gray-600">
                    Sélectionnez le produit pour lequel vous souhaitez obtenir un devis
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {products.map((product) => (
                    <motion.div
                      key={product.id}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => selectProduct(product.id)}
                      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-axa-blue p-6"
                    >
                      <div className={`${product.color} w-16 h-16 rounded-xl flex items-center justify-center mb-4`}>
                        <product.icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {product.description}
                      </p>
                      <div className="flex items-center text-axa-blue font-semibold">
                        Choisir ce produit
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Step 2: Personal Information */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-xl p-8"
              >
                <h2 className="text-3xl font-bold text-axa-blue mb-8">
                  Vos informations personnelles
                </h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Prénom *
                      </label>
                      <input
                        type="text"
                        {...register('personalInfo.firstName', { required: 'Le prénom est requis' })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                        placeholder="Votre prénom"
                      />
                      {errors.personalInfo?.firstName && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.firstName.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nom *
                      </label>
                      <input
                        type="text"
                        {...register('personalInfo.lastName', { required: 'Le nom est requis' })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                        placeholder="Votre nom"
                      />
                      {errors.personalInfo?.lastName && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.lastName.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        {...register('personalInfo.email', { 
                          required: 'L\'email est requis',
                          pattern: {
                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                            message: 'Email invalide'
                          }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                      {errors.personalInfo?.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.email.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Téléphone *
                      </label>
                      <input
                        type="tel"
                        {...register('personalInfo.phone', { required: 'Le téléphone est requis' })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                        placeholder="+212 6XX-XXXXXX"
                      />
                      {errors.personalInfo?.phone && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.phone.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date de naissance *
                      </label>
                      <input
                        type="date"
                        {...register('personalInfo.birthDate', { required: 'La date de naissance est requise' })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                      />
                      {errors.personalInfo?.birthDate && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.birthDate.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Ville *
                      </label>
                      <select
                        {...register('personalInfo.city', { required: 'La ville est requise' })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                      >
                        <option value="">Sélectionnez votre ville</option>
                        <option value="casablanca">Casablanca</option>
                        <option value="rabat">Rabat</option>
                        <option value="marrakech">Marrakech</option>
                        <option value="fes">Fès</option>
                        <option value="tanger">Tanger</option>
                        <option value="agadir">Agadir</option>
                        <option value="meknes">Meknès</option>
                        <option value="oujda">Oujda</option>
                        <option value="kenitra">Kénitra</option>
                        <option value="tetouan">Tétouan</option>
                      </select>
                      {errors.personalInfo?.city && (
                        <p className="text-red-500 text-sm mt-1">{errors.personalInfo.city.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Retour
                  </button>
                  <button
                    type="button"
                    onClick={nextStep}
                    className="flex items-center px-6 py-3 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                  >
                    Continuer
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 3: Product Details */}
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-xl p-8"
              >
                <h2 className="text-3xl font-bold text-axa-blue mb-8">
                  Détails de votre demande
                </h2>

                {renderProductSpecificFields()}

                <div className="flex justify-between mt-8">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Retour
                  </button>
                  <button
                    type="submit"
                    className="flex items-center px-8 py-3 bg-axa-red text-white rounded-lg hover:bg-red-600 transition-colors font-semibold"
                  >
                    <Calculator className="mr-2 h-5 w-5" />
                    Calculer mon devis
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 4: Quote Result */}
            {currentStep === 4 && quoteGenerated && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center"
              >
                <div className="bg-white rounded-2xl shadow-xl p-12 mb-8">
                  <div className="mb-8">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h2 className="text-3xl font-bold text-axa-blue mb-4">
                      Votre devis est prêt !
                    </h2>
                    <p className="text-gray-600">
                      Nous avons calculé une estimation personnalisée pour votre demande d'assurance.
                    </p>
                  </div>

                  <div className="bg-axa-gray rounded-xl p-8 mb-8">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                      <div>
                        <div className="text-3xl font-bold text-axa-blue mb-2">
                          À partir de
                        </div>
                        <div className="text-4xl font-bold text-axa-red mb-2">
                          1 250 DH
                        </div>
                        <div className="text-gray-600">par an</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900 mb-2">
                          Formule recommandée
                        </div>
                        <div className="text-2xl font-bold text-axa-blue mb-2">
                          Confort
                        </div>
                        <div className="text-gray-600">Protection étendue</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900 mb-2">
                          Économies possibles
                        </div>
                        <div className="text-2xl font-bold text-green-500 mb-2">
                          Jusqu'à 30%
                        </div>
                        <div className="text-gray-600">vs concurrence</div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button className="bg-axa-red hover:bg-red-600 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center">
                      <FileText className="mr-2 h-5 w-5" />
                      Télécharger le devis
                    </button>
                    <button className="bg-axa-blue hover:bg-blue-800 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center">
                      <Phone className="mr-2 h-5 w-5" />
                      Parler à un conseiller
                    </button>
                  </div>

                  <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Important :</strong> Ce devis est une estimation basée sur les informations fournies. 
                      Le tarif définitif sera établi après étude complète de votre dossier par nos experts.
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </form>
        </div>
      </section>
    </div>
  );
};