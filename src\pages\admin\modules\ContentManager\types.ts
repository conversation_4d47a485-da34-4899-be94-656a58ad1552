export type ContentType = 'page' | 'blog' | 'media';
export type ContentStatus = 'draft' | 'published' | 'scheduled' | 'archived';
export type ViewMode = 'grid' | 'list';

export interface SEO {
  title: string;
  description: string;
  keywords: string;
}

export interface ContentItem {
  id: string;
  title: string;
  slug: string;
  type: ContentType;
  content?: string;
  excerpt?: string;
  status: ContentStatus;
  publishDate?: string | null;
  unpublishDate?: string | null;
  category?: string;
  tags?: string[];
  featuredImage?: string;
  seo: SEO;
  createdAt: string;
  updatedAt: string;
}

export interface MediaFile {
  id: string;
  originalName: string;
  name: string;
  url: string;
  size: number;
  mimeType: string;
  createdAt: string;
  updatedAt: string;
}

export interface EditorState {
  content: string;
  title: string;
  slug: string;
  excerpt?: string;
  status: ContentStatus;
  publishDate?: string | null;
  unpublishDate?: string | null;
  category?: string;
  tags?: string[];
  featuredImage?: string;
  seo: SEO;
}

export interface ContentFilter {
  search: string;
  status: ContentStatus | 'all';
  type: ContentType | 'all';
  category: string;
  dateRange: { start: Date; end: Date } | null;
}

export interface TabItem {
  id: string;
  name: string;
  icon: any;
  count: number;
}
