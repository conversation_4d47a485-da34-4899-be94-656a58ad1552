import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Pie<PERSON>hart, 
  LineChart, 
  Activity, 
  Users, 
  DollarSign, 
  Target, 
  Calendar, 
  Download, 
  Filter, 
  RefreshCw,
  Eye,
  MousePointer,
  Phone,
  Mail,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Star,
  Award,
  Zap,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface AnalyticsData {
  overview: {
    totalRevenue: number;
    revenueGrowth: number;
    totalClients: number;
    clientsGrowth: number;
    totalLeads: number;
    leadsGrowth: number;
    conversionRate: number;
    conversionGrowth: number;
  };
  salesFunnel: {
    leads: number;
    qualified: number;
    quotes: number;
    contracts: number;
    conversionRates: {
      leadToQualified: number;
      qualifiedToQuote: number;
      quoteToContract: number;
      overall: number;
    };
  };
  revenueByProduct: Array<{
    product: string;
    revenue: number;
    percentage: number;
    growth: number;
  }>;
  performanceByAgent: Array<{
    agentId: string;
    agentName: string;
    leadsGenerated: number;
    clientsAcquired: number;
    revenue: number;
    conversionRate: number;
    rating: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    revenue: number;
    leads: number;
    clients: number;
    quotes: number;
  }>;
  channelPerformance: Array<{
    channel: string;
    leads: number;
    conversions: number;
    conversionRate: number;
    cost: number;
    roi: number;
  }>;
  customerSegments: Array<{
    segment: string;
    count: number;
    revenue: number;
    avgValue: number;
    retention: number;
  }>;
  geographicData: Array<{
    city: string;
    clients: number;
    revenue: number;
    growth: number;
  }>;
}

export const AnalyticsEnhanced: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [activeTab, setActiveTab] = useState('overview');
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for comprehensive analytics
      const mockData: AnalyticsData = {
        overview: {
          totalRevenue: 1250000,
          revenueGrowth: 18.5,
          totalClients: 342,
          clientsGrowth: 12.3,
          totalLeads: 1247,
          leadsGrowth: 25.7,
          conversionRate: 27.4,
          conversionGrowth: 8.2
        },
        salesFunnel: {
          leads: 1247,
          qualified: 623,
          quotes: 387,
          contracts: 342,
          conversionRates: {
            leadToQualified: 49.9,
            qualifiedToQuote: 62.1,
            quoteToContract: 88.4,
            overall: 27.4
          }
        },
        revenueByProduct: [
          { product: 'Assurance Auto', revenue: 485000, percentage: 38.8, growth: 15.2 },
          { product: 'Assurance Habitation', revenue: 312000, percentage: 25.0, growth: 22.1 },
          { product: 'Assurance Santé', revenue: 278000, percentage: 22.2, growth: 18.7 },
          { product: 'Prévoyance', revenue: 125000, percentage: 10.0, growth: 12.5 },
          { product: 'Épargne Retraite', revenue: 50000, percentage: 4.0, growth: 8.3 }
        ],
        performanceByAgent: [
          {
            agentId: '2',
            agentName: 'Conseiller 1',
            leadsGenerated: 523,
            clientsAcquired: 145,
            revenue: 625000,
            conversionRate: 27.7,
            rating: 4.8
          },
          {
            agentId: '3',
            agentName: 'Conseiller 2',
            leadsGenerated: 487,
            clientsAcquired: 132,
            revenue: 578000,
            conversionRate: 27.1,
            rating: 4.6
          },
          {
            agentId: '1',
            agentName: 'Admin',
            leadsGenerated: 237,
            clientsAcquired: 65,
            revenue: 47000,
            conversionRate: 27.4,
            rating: 4.9
          }
        ],
        timeSeriesData: [
          { date: '2024-01-01', revenue: 95000, leads: 89, clients: 24, quotes: 31 },
          { date: '2024-01-02', revenue: 102000, leads: 95, clients: 26, quotes: 33 },
          { date: '2024-01-03', revenue: 87000, leads: 78, clients: 21, quotes: 28 },
          { date: '2024-01-04', revenue: 115000, leads: 103, clients: 29, quotes: 38 },
          { date: '2024-01-05', revenue: 98000, leads: 91, clients: 25, quotes: 32 },
          { date: '2024-01-06', revenue: 108000, leads: 97, clients: 27, quotes: 35 },
          { date: '2024-01-07', revenue: 125000, leads: 112, clients: 31, quotes: 42 }
        ],
        channelPerformance: [
          { channel: 'Site Web', leads: 456, conversions: 125, conversionRate: 27.4, cost: 12500, roi: 4.2 },
          { channel: 'Réseaux Sociaux', leads: 287, conversions: 78, conversionRate: 27.2, cost: 8900, roi: 3.8 },
          { channel: 'Recommandations', leads: 234, conversions: 89, conversionRate: 38.0, cost: 0, roi: 0 },
          { channel: 'Téléphone', leads: 156, conversions: 34, conversionRate: 21.8, cost: 3400, roi: 2.9 },
          { channel: 'Email Marketing', leads: 114, conversions: 16, conversionRate: 14.0, cost: 2100, roi: 1.8 }
        ],
        customerSegments: [
          { segment: 'Particuliers Jeunes', count: 128, revenue: 285000, avgValue: 2227, retention: 89.2 },
          { segment: 'Familles', count: 156, revenue: 467000, avgValue: 2994, retention: 92.1 },
          { segment: 'Seniors', count: 89, revenue: 234000, avgValue: 2629, retention: 94.7 },
          { segment: 'Entreprises PME', count: 34, revenue: 198000, avgValue: 5824, retention: 87.3 },
          { segment: 'Grandes Entreprises', count: 12, revenue: 156000, avgValue: 13000, retention: 95.8 }
        ],
        geographicData: [
          { city: 'Casablanca', clients: 145, revenue: 523000, growth: 18.2 },
          { city: 'Rabat', clients: 89, revenue: 312000, growth: 15.7 },
          { city: 'Marrakech', clients: 67, revenue: 234000, growth: 22.1 },
          { city: 'Fès', clients: 45, revenue: 167000, growth: 12.8 },
          { city: 'Tanger', clients: 32, revenue: 145000, growth: 25.3 },
          { city: 'Agadir', clients: 28, revenue: 123000, growth: 19.4 }
        ]
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des analytics', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />;
  };

  const periods = [
    { value: '7d', label: '7 jours' },
    { value: '30d', label: '30 jours' },
    { value: '90d', label: '3 mois' },
    { value: '1y', label: '1 an' }
  ];

  const tabs = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: BarChart3 },
    { id: 'sales', name: 'Ventes', icon: TrendingUp },
    { id: 'performance', name: 'Performance', icon: Target },
    { id: 'customers', name: 'Clients', icon: Users },
    { id: 'geographic', name: 'Géographie', icon: Globe }
  ];

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="h-8 w-8 mr-3 text-purple-600" />
            Analytics Avancées
          </h1>
          <p className="text-gray-600 mt-2">Analysez vos performances et optimisez votre stratégie</p>
        </div>
        <div className="flex space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            {periods.map(period => (
              <option key={period.value} value={period.value}>{period.label}</option>
            ))}
          </select>
          <button
            onClick={loadAnalyticsData}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-purple-600 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(analyticsData.overview.totalRevenue)}
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className={`mt-4 flex items-center text-sm ${getGrowthColor(analyticsData.overview.revenueGrowth)}`}>
                {getGrowthIcon(analyticsData.overview.revenueGrowth)}
                <span className="ml-1">{formatPercentage(analyticsData.overview.revenueGrowth)}</span>
                <span className="text-gray-500 ml-1">vs période précédente</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Clients</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalClients}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className={`mt-4 flex items-center text-sm ${getGrowthColor(analyticsData.overview.clientsGrowth)}`}>
                {getGrowthIcon(analyticsData.overview.clientsGrowth)}
                <span className="ml-1">{formatPercentage(analyticsData.overview.clientsGrowth)}</span>
                <span className="text-gray-500 ml-1">vs période précédente</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Leads</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalLeads}</p>
                </div>
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Target className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className={`mt-4 flex items-center text-sm ${getGrowthColor(analyticsData.overview.leadsGrowth)}`}>
                {getGrowthIcon(analyticsData.overview.leadsGrowth)}
                <span className="ml-1">{formatPercentage(analyticsData.overview.leadsGrowth)}</span>
                <span className="text-gray-500 ml-1">vs période précédente</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de Conversion</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(analyticsData.overview.conversionRate)}
                  </p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Activity className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className={`mt-4 flex items-center text-sm ${getGrowthColor(analyticsData.overview.conversionGrowth)}`}>
                {getGrowthIcon(analyticsData.overview.conversionGrowth)}
                <span className="ml-1">{formatPercentage(analyticsData.overview.conversionGrowth)}</span>
                <span className="text-gray-500 ml-1">vs période précédente</span>
              </div>
            </div>
          </div>

          {/* Sales Funnel */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Entonnoir de Vente</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.salesFunnel.leads}</p>
                <p className="text-sm text-gray-600">Leads</p>
              </div>
              
              <div className="text-center">
                <div className="bg-yellow-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.salesFunnel.qualified}</p>
                <p className="text-sm text-gray-600">Qualifiés</p>
                <p className="text-xs text-green-600 mt-1">
                  {formatPercentage(analyticsData.salesFunnel.conversionRates.leadToQualified)}
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-orange-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-orange-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.salesFunnel.quotes}</p>
                <p className="text-sm text-gray-600">Devis</p>
                <p className="text-xs text-green-600 mt-1">
                  {formatPercentage(analyticsData.salesFunnel.conversionRates.qualifiedToQuote)}
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.salesFunnel.contracts}</p>
                <p className="text-sm text-gray-600">Contrats</p>
                <p className="text-xs text-green-600 mt-1">
                  {formatPercentage(analyticsData.salesFunnel.conversionRates.quoteToContract)}
                </p>
              </div>
            </div>
            
            <div className="mt-6 bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">Taux de conversion global</span>
                <span className="text-lg font-bold text-green-600">
                  {formatPercentage(analyticsData.salesFunnel.conversionRates.overall)}
                </span>
              </div>
            </div>
          </div>

          {/* Revenue by Product */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Chiffre d'Affaires par Produit</h3>
            <div className="space-y-4">
              {analyticsData.revenueByProduct.map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">{product.product}</span>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm font-bold text-gray-900">
                          {formatCurrency(product.revenue)}
                        </span>
                        <span className={`text-sm ${getGrowthColor(product.growth)} flex items-center`}>
                          {getGrowthIcon(product.growth)}
                          <span className="ml-1">{formatPercentage(product.growth)}</span>
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-axa-blue h-2 rounded-full transition-all duration-300"
                        style={{ width: `${product.percentage}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-gray-500">{formatPercentage(product.percentage)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Performance Tab */}
      {activeTab === 'performance' && (
        <div className="space-y-6">
          {/* Agent Performance */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance par Conseiller</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Conseiller
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Leads Générés
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Clients Acquis
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Chiffre d'Affaires
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Taux de Conversion
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Note
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.performanceByAgent.map((agent) => (
                    <tr key={agent.agentId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-axa-blue flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {agent.agentName.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{agent.agentName}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{agent.leadsGenerated}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{agent.clientsAcquired}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(agent.revenue)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatPercentage(agent.conversionRate)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 mr-1" />
                          <span className="text-sm text-gray-900">{agent.rating}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Channel Performance */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance par Canal</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {analyticsData.channelPerformance.map((channel, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-gray-900">{channel.channel}</h4>
                    <div className="flex items-center space-x-1">
                      {channel.channel === 'Site Web' && <Globe className="h-4 w-4 text-blue-600" />}
                      {channel.channel === 'Réseaux Sociaux' && <Smartphone className="h-4 w-4 text-purple-600" />}
                      {channel.channel === 'Recommandations' && <Users className="h-4 w-4 text-green-600" />}
                      {channel.channel === 'Téléphone' && <Phone className="h-4 w-4 text-orange-600" />}
                      {channel.channel === 'Email Marketing' && <Mail className="h-4 w-4 text-red-600" />}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-600">Leads</span>
                      <span className="text-sm font-medium text-gray-900">{channel.leads}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-600">Conversions</span>
                      <span className="text-sm font-medium text-gray-900">{channel.conversions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-600">Taux de conversion</span>
                      <span className="text-sm font-medium text-green-600">
                        {formatPercentage(channel.conversionRate)}
                      </span>
                    </div>
                    {channel.cost > 0 && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-600">Coût</span>
                          <span className="text-sm font-medium text-gray-900">
                            {formatCurrency(channel.cost)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-600">ROI</span>
                          <span className="text-sm font-medium text-purple-600">
                            {channel.roi.toFixed(1)}x
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};
