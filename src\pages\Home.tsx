import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Car, 
  Home as HomeIcon, 
  Heart, 
  PiggyBank, 
  Building2,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Award,
  Clock,
  Phone,
  Mail,
  MapPin,
  Target,
  Zap,
  Calculator,
  FileText,
  Headphones
} from 'lucide-react';

export const Home: React.FC = () => {
  const products = [
    {
      icon: Car,
      title: 'Assurance Auto',
      description: 'Protection complète pour votre véhicule avec assistance 24h/24',
      color: 'bg-blue-500',
      image: 'https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['Responsabilité civile', 'Tous risques', 'Assistance 24h/24', 'Véhicule de remplacement']
    },
    {
      icon: HomeIcon,
      title: 'Assurance Habitation',
      description: 'Sécurisez votre foyer et vos biens contre tous les risques',
      color: 'bg-green-500',
      image: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['Incendie', 'Dégâts des eaux', 'Vol', 'Responsabilité civile']
    },
    {
      icon: Heart,
      title: 'Assurance Santé',
      description: 'Votre santé et celle de votre famille, notre priorité absolue',
      color: 'bg-red-500',
      image: 'https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['Hospitalisation', 'Soins dentaires', 'Optique', 'Médecines douces']
    },
    {
      icon: Shield,
      title: 'Prévoyance',
      description: 'Protégez votre famille et votre avenir financier',
      color: 'bg-purple-500',
      image: 'https://images.pexels.com/photos/6801648/pexels-photo-6801648.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['Capital décès', 'Rente éducation', 'Incapacité', 'Invalidité']
    },
    {
      icon: PiggyBank,
      title: 'Épargne Retraite',
      description: 'Préparez sereinement votre retraite avec nos solutions d\'épargne',
      color: 'bg-yellow-500',
      image: 'https://images.pexels.com/photos/6289065/pexels-photo-6289065.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['Versements libres', 'Avantages fiscaux', 'Gestion pilotée', 'Transmission']
    },
    {
      icon: Building2,
      title: 'Assurance Professionnelle',
      description: 'Solutions sur mesure pour protéger votre activité professionnelle',
      color: 'bg-indigo-500',
      image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800',
      features: ['RC Professionnelle', 'Multirisque', 'Perte d\'exploitation', 'Cyber-risques']
    }
  ];

  const stats = [
    { number: '15+', label: 'Années d\'expérience', icon: Award, description: 'Au service des Marocains' },
    { number: '5000+', label: 'Clients satisfaits', icon: Users, description: 'Particuliers et entreprises' },
    { number: '98%', label: 'Taux de satisfaction', icon: Star, description: 'Clients recommandent nos services' },
    { number: '24/7', label: 'Service client', icon: Clock, description: 'Assistance disponible' }
  ];

  const testimonials = [
    {
      name: 'Ahmed Benali',
      role: 'Chef d\'entreprise',
      content: 'Service exceptionnel et conseils personnalisés. L\'équipe de MOUMEN TECHNIQUE ET PREVOYANCE a su comprendre mes besoins spécifiques et me proposer des solutions adaptées.',
      rating: 5,
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
      company: 'Entreprise BENALI SARL'
    },
    {
      name: 'Fatima Zahra',
      role: 'Particulier',
      content: 'Très professionnels et réactifs. Mon sinistre auto a été traité rapidement et efficacement. Je recommande vivement leurs services.',
      rating: 5,
      image: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400',
      company: 'Cliente depuis 2019'
    },
    {
      name: 'Mohamed Alami',
      role: 'Commerçant',
      content: 'Une équipe à l\'écoute qui comprend les besoins spécifiques de mon activité professionnelle. Excellent rapport qualité-prix.',
      rating: 5,
      image: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=400',
      company: 'Commerce ALAMI'
    }
  ];

  const advantages = [
    {
      icon: Shield,
      title: 'Expertise AXA',
      description: 'Bénéficiez de l\'expertise du leader mondial de l\'assurance',
      color: 'text-blue-500'
    },
    {
      icon: Target,
      title: 'Solutions Sur Mesure',
      description: 'Des produits adaptés à vos besoins spécifiques',
      color: 'text-green-500'
    },
    {
      icon: Zap,
      title: 'Réactivité',
      description: 'Traitement rapide de vos demandes et sinistres',
      color: 'text-yellow-500'
    },
    {
      icon: Users,
      title: 'Proximité',
      description: 'Un service personnalisé et une relation de confiance',
      color: 'text-purple-500'
    }
  ];

  const services = [
    {
      icon: Calculator,
      title: 'Devis en ligne',
      description: 'Obtenez votre devis personnalisé en 2 minutes',
      link: '/devis'
    },
    {
      icon: FileText,
      title: 'Espace client',
      description: 'Gérez vos contrats et documents en ligne',
// ...existing code...
    },
    {
      icon: Headphones,
      title: 'Support 24/7',
      description: 'Une équipe à votre écoute jour et nuit',
      link: '/contact'
    }
  ];

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-axa-blue via-blue-700 to-blue-900 text-white py-20 lg:py-32 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=1600" 
            alt="Bureau d'assurance moderne"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-axa-blue/90 via-blue-700/90 to-blue-900/90"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Award className="h-6 w-6 text-axa-red" />
                  <span className="text-blue-200 font-medium">Agent Général AXA Certifié depuis 2008</span>
                </div>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Votre Protection,
                  <span className="text-axa-red block">Notre Expertise</span>
                </h1>
                <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed">
                  Agent général AXA au Maroc depuis 15 ans, nous vous accompagnons dans tous vos projets d'assurance avec professionnalisme et proximité.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/devis"
                  className="bg-axa-red hover:bg-red-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center group"
                >
                  <Calculator className="mr-2 h-5 w-5" />
                  Devis Gratuit
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  to="/contact"
                  className="border-2 border-white text-white hover:bg-white hover:text-axa-blue px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center"
                >
                  <Phone className="mr-2 h-5 w-5" />
                  Nous Contacter
                </Link>
              </div>

              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-sm">Devis en 2 minutes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-sm">Sans engagement</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-sm">Réponse immédiate</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-bold mb-6">Pourquoi nous choisir ?</h3>
                <div className="space-y-4">
                  {advantages.map((advantage, index) => (
                    <motion.div 
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                      className="flex items-start space-x-3"
                    >
                      <advantage.icon className={`h-6 w-6 ${advantage.color} mt-1`} />
                      <div>
                        <div className="font-semibold">{advantage.title}</div>
                        <div className="text-sm text-blue-200">{advantage.description}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-axa-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <stat.icon className="h-8 w-8 text-axa-blue mx-auto mb-4" />
                <div className="text-3xl lg:text-4xl font-bold text-axa-blue mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-900 font-medium mb-1">{stat.label}</div>
                <div className="text-sm text-gray-600">{stat.description}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-axa-blue mb-6">
              Nos Services
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Des services digitaux modernes pour vous faciliter la vie
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center p-8 rounded-2xl bg-gray-50 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <service.icon className="h-12 w-12 text-axa-blue mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <Link
                  to={service.link}
                  className="inline-flex items-center text-axa-blue font-semibold hover:text-axa-red transition-colors"
                >
                  En savoir plus
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-axa-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-axa-blue mb-6">
              Nos Solutions d'Assurance
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez notre gamme complète de produits d'assurance AXA, adaptés à tous vos besoins de protection.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer"
              >
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={product.image} 
                    alt={product.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className={`absolute top-4 left-4 ${product.color} w-12 h-12 rounded-xl flex items-center justify-center`}>
                    <product.icon className="h-6 w-6 text-white" />
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-axa-blue transition-colors">
                    {product.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {product.description}
                  </p>
                  
                  <div className="space-y-2 mb-6">
                    {product.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    to="/devis"
                    className="inline-flex items-center text-axa-blue font-semibold hover:text-axa-red transition-colors group"
                  >
                    Demander un devis
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mt-12"
          >
            <Link
              to="/produits"
              className="bg-axa-blue hover:bg-blue-800 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 inline-flex items-center group"
            >
              Voir tous nos produits
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-axa-blue mb-6">
              Ils nous font confiance
            </h2>
            <p className="text-xl text-gray-600">
              Découvrez les témoignages de nos clients satisfaits
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-axa-gray rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center mb-6">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                    <div className="text-xs text-axa-blue">{testimonial.company}</div>
                  </div>
                </div>
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 italic leading-relaxed">
                  "{testimonial.content}"
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gradient-to-r from-axa-blue to-blue-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                Prêt à vous protéger ?
              </h2>
              <p className="text-xl text-blue-100 mb-8">
                Obtenez votre devis personnalisé en quelques minutes et découvrez nos solutions d'assurance adaptées à vos besoins.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/devis"
                  className="bg-axa-red hover:bg-red-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center group"
                >
                  <Calculator className="mr-2 h-5 w-5" />
                  Obtenir mon devis
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  to="/contact"
                  className="border-2 border-white text-white hover:bg-white hover:text-axa-blue px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 inline-flex items-center justify-center"
                >
                  <Phone className="mr-2 h-5 w-5" />
                  Parler à un expert
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20"
            >
              <h3 className="text-2xl font-bold mb-6">Contactez-nous</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-6 w-6 text-axa-red" />
                  <div>
                    <div className="font-semibold">Téléphone</div>
                    <div className="text-blue-200">+212 5XX-XXXXXX</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-6 w-6 text-axa-red" />
                  <div>
                    <div className="font-semibold">Email</div>
                    <div className="text-blue-200"><EMAIL></div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-6 w-6 text-axa-red" />
                  <div>
                    <div className="font-semibold">Adresse</div>
                    <div className="text-blue-200">123 Boulevard Mohammed V, Casablanca</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-6 w-6 text-axa-red" />
                  <div>
                    <div className="font-semibold">Horaires</div>
                    <div className="text-blue-200">Lun-Ven 8h30-18h, Sam 9h-13h</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};