import React from 'react';
import { motion } from 'framer-motion';
import { Banner } from './types';

interface BannerPreviewProps {
  banner: Partial<Banner>;
}

export const BannerPreview: React.FC<BannerPreviewProps> = ({ banner }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Aperçu</h4>
      
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full"
      >
        <div
          className={`p-4 rounded-lg`}
          style={{
            backgroundColor: banner.backgroundColor || '#1D4ED8',
            color: banner.textColor || '#FFFFFF',
          }}
        >
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex-1">
              <p className="font-medium">{banner.content || 'Contenu de la bannière'}</p>
            </div>
            {banner.showContact && (
              <button
                className={`px-4 py-2 rounded-lg ml-4 text-sm font-medium`}
                style={{
                  backgroundColor: banner.textColor || '#FFFFFF',
                  color: banner.backgroundColor || '#1D4ED8',
                }}
              >
                Nous contacter
              </button>
            )}
          </div>
        </div>
      </motion.div>

      <div className="text-sm text-gray-500">
        <p>
          Cette bannière sera affichée sur {banner.targetPages?.length || 0} page(s)
          {banner.startDate && banner.endDate && (
            <> du {new Date(banner.startDate).toLocaleDateString()} au{' '}
            {new Date(banner.endDate).toLocaleDateString()}</>
          )}
        </p>
      </div>
    </div>
  );
};

export default BannerPreview;
