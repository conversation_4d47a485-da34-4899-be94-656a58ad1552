import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { Lock, User, Shield, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../services/api';
import { useMockAuth } from '../../services/mockAuth';

interface AdminLoginForm {
  email: string;
  password: string;
}

export const AdminLogin: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { login } = useAuth();
  const mockAuth = useMockAuth();
  const { register, handleSubmit, formState: { errors } } = useForm<AdminLoginForm>();

  const onLogin = async (data: AdminLoginForm) => {
    setIsLoading(true);
    setError(null);

    try {
      // Essayer d'abord l'API réelle
      let response;
      try {
        response = await login(data.email, data.password);
      } catch (apiError) {
        console.log('API non disponible, utilisation du mode mock');
        // Si l'API échoue, utiliser l'authentification mock
        response = await mockAuth.login(data.email, data.password);
      }

      if (response.success) {
        navigate('/admin/dashboard');
      } else {
        setError(response.message || 'Erreur de connexion');
      }
    } catch (error: any) {
      setError(error.message || 'Erreur de connexion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-axa-blue via-blue-700 to-blue-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="max-w-md w-full space-y-8"
      >
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <div className="text-center mb-8">
            <div className="bg-axa-blue w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Shield className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-axa-blue mb-2">
              Administration
            </h2>
            <p className="text-gray-600">
              Accès réservé aux administrateurs
            </p>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3 mb-6"
            >
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
              <span className="text-red-700 text-sm">{error}</span>
            </motion.div>
          )}

          <form onSubmit={handleSubmit(onLogin)} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <div className="relative">
                <input
                  type="email"
                  {...register('email', {
                    required: 'Email requis',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Email invalide'
                    }
                  })}
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent transition-colors"
                  placeholder="<EMAIL>"
                />
                <User className="absolute left-4 top-3.5 h-5 w-5 text-gray-400" />
              </div>
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mot de passe *
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  {...register('password', { required: 'Le mot de passe est requis' })}
                  className="w-full pl-12 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent transition-colors"
                  placeholder="••••••••"
                />
                <Lock className="absolute left-4 top-3.5 h-5 w-5 text-gray-400" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-3.5 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-axa-red hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Connexion...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-5 w-5" />
                  Se connecter
                </>
              )}
            </button>
          </form>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <Lock className="h-4 w-4" />
              <span>Connexion sécurisée SSL</span>
            </div>
          </div>
        </div>

        <div className="text-center">
          <div className="bg-white/10 rounded-lg p-4 text-white/90 text-sm">
            <p className="font-semibold mb-2">🔑 Identifiants de test :</p>
            <div className="space-y-1">
              <p><strong>Admin :</strong> <EMAIL> / Admin123!</p>
              <p><strong>Conseiller 1 :</strong> <EMAIL> / User123!</p>
              <p><strong>Conseiller 2 :</strong> <EMAIL> / User123!</p>
            </div>
            <p className="text-xs mt-2 text-white/70">
              Mode démo - Fonctionne même sans backend
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default AdminLogin;