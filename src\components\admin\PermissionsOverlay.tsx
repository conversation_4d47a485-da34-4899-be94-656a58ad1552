import React from 'react';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { Shield, Lock, CheckCircle, XCircle } from 'lucide-react';
import { AdminModule, AdminPermission } from '../../types/admin';

interface PermissionsOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PermissionsOverlay: React.FC<PermissionsOverlayProps> = ({ isOpen, onClose }) => {
  const { user, canAccess, hasPermission } = useAdminPermissions();

  if (!isOpen) return null;

  const modules: AdminModule[] = [
    'LEADS',
    'QUOTES',
    'CLIENTS',
    'ANALYTICS',
    'CONTENT',
    'SETTINGS'
  ];

  const permissions: AdminPermission[] = [
    'READ',
    'WRITE',
    'DELETE',
    'MANAGE'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Shield className="h-6 w-6 mr-2 text-axa-blue" />
            Permissions Actuelles
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XCircle className="h-6 w-6" />
          </button>
        </div>

        <div className="border rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Module
                </th>
                {permissions.map(permission => (
                  <th
                    key={permission}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {permission}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {modules.map(module => (
                <tr key={module}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Lock className="h-4 w-4 mr-2 text-axa-blue" />
                      <span className="font-medium text-gray-900">{module}</span>
                    </div>
                  </td>
                  {permissions.map(permission => (
                    <td key={`${module}-${permission}`} className="px-6 py-4 whitespace-nowrap">
                      {hasPermission(module, permission) ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {user && (
          <div className="mt-4 bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900">Informations de compte</h3>
            <div className="mt-2 text-sm text-gray-500">
              <p>Utilisateur : {user.name}</p>
              <p>Rôle : {user.role}</p>
              <p>Dernière connexion : {new Date(user.lastLogin).toLocaleString('fr-FR')}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PermissionsOverlay;
