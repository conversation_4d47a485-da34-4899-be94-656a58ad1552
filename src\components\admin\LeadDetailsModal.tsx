import React from 'react';
import { motion } from 'framer-motion';
import {
  X,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  User,
  Building,
  Target,
  DollarSign
} from 'lucide-react';
import { Lead } from '../../types/admin';
import { ScoreDetails } from './ScoreDetails';
import { TaskList } from './TaskList';
import useLeadScoring from '../../hooks/useLeadScoring';

interface LeadDetailsModalProps {
  lead: Lead;
  isOpen: boolean;
  onClose: () => void;
}

export const LeadDetailsModal: React.FC<LeadDetailsModalProps> = ({
  lead,
  isOpen,
  onClose
}) => {
  const { score, scoreHistory, detailedScoring } = useLeadScoring(lead);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {lead.firstName} {lead.lastName}
            </h2>
            <p className="text-sm text-gray-500">ID: {lead.id}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="grid grid-cols-3 gap-6 p-6">
          {/* Colonne d'informations principales */}
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <h3 className="font-medium text-gray-900">Informations de contact</h3>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  {lead.email}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {lead.phone}
                </div>
                {lead.city && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    {lead.city}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <h3 className="font-medium text-gray-900">Informations professionnelles</h3>
              <div className="space-y-2">
                {lead.company && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Building className="h-4 w-4 mr-2" />
                    {lead.company}
                  </div>
                )}
                {lead.position && (
                  <div className="flex items-center text-sm text-gray-600">
                    <User className="h-4 w-4 mr-2" />
                    {lead.position}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <h3 className="font-medium text-gray-900">Détails de l'opportunité</h3>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Target className="h-4 w-4 mr-2" />
                  Probabilité: {lead.probability}%
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Valeur: {lead.estimatedValue.toLocaleString()} DH
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  Clôture prévue: {new Date(lead.expectedCloseDate).toLocaleDateString()}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  Dernier contact: {new Date(lead.lastContact).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          {/* Colonne de scoring */}
          <div className="space-y-6">
            <ScoreDetails lead={lead} />
          </div>

          {/* Colonne des tâches */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-medium text-gray-900 mb-4">Tâches à effectuer</h3>
              <TaskList leadId={lead.id} />
            </div>

            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-medium text-gray-900 mb-4">Notes</h3>
              <p className="text-sm text-gray-600 whitespace-pre-wrap">
                {lead.notes || 'Aucune note disponible'}
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default LeadDetailsModal;
