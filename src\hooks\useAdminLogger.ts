import { useCallback, useContext, createContext } from 'react';
import { useLocation } from 'react-router-dom';
import adminLogger, { LogLevel, LogAction } from '../services/adminLogger';
import type { AdminModule } from '../types/admin';

interface AdminLoggerContextType {
  logInfo: (module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) => Promise<void>;
  logWarning: (module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) => Promise<void>;
  logError: (module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) => Promise<void>;
  logSecurity: (module: AdminModule, action: LogAction, message: string, metadata?: Record<string, any>) => Promise<void>;
}

const AdminLoggerContext = createContext<AdminLoggerContextType | null>(null);

export const useAdminLogger = (module?: AdminModule) => {
  const context = useContext(AdminLoggerContext);
  const location = useLocation();

  if (!context) {
    throw new Error('useAdminLogger doit être utilisé dans un AdminLoggerProvider');
  }

  const enrichMetadata = useCallback((metadata?: Record<string, any>) => {
    return {
      ...metadata,
      path: location.pathname,
      module,
      timestamp: new Date().toISOString()
    };
  }, [location.pathname, module]);

  const log = useCallback(async (
    level: LogLevel,
    action: LogAction,
    message: string,
    metadata?: Record<string, any>
  ) => {
    if (!module) {
      console.warn('Module non spécifié dans useAdminLogger');
      return;
    }

    try {
      switch (level) {
        case 'info':
          await context.logInfo(module, action, message, enrichMetadata(metadata));
          break;
        case 'warning':
          await context.logWarning(module, action, message, enrichMetadata(metadata));
          break;
        case 'error':
          await context.logError(module, action, message, enrichMetadata(metadata));
          break;
        case 'security':
          await context.logSecurity(module, action, message, enrichMetadata(metadata));
          break;
      }
    } catch (error) {
      console.error('Erreur lors de la journalisation:', error);
    }
  }, [context, module, enrichMetadata]);

  return {
    log,
    logInfo: useCallback((action: LogAction, message: string, metadata?: Record<string, any>) => 
      log('info', action, message, metadata), [log]),
    logWarning: useCallback((action: LogAction, message: string, metadata?: Record<string, any>) => 
      log('warning', action, message, metadata), [log]),
    logError: useCallback((action: LogAction, message: string, metadata?: Record<string, any>) => 
      log('error', action, message, metadata), [log]),
    logSecurity: useCallback((action: LogAction, message: string, metadata?: Record<string, any>) => 
      log('security', action, message, metadata), [log])
  };
};

export const AdminLoggerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const value = {
    logInfo: adminLogger.info.bind(adminLogger),
    logWarning: adminLogger.warning.bind(adminLogger),
    logError: adminLogger.error.bind(adminLogger),
    logSecurity: adminLogger.security.bind(adminLogger)
  };

  return (
    <AdminLoggerContext.Provider value={value}>
      {children}
    </AdminLoggerContext.Provider>
  );
};

export default useAdminLogger;
