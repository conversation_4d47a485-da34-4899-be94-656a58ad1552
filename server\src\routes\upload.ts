import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Configuration de stockage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'uploads');
    
    // Créer le dossier s'il n'existe pas
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Générer un nom unique
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// Configuration multer
const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB par défaut
  },
  fileFilter: (req, file, cb) => {
    // Types de fichiers autorisés
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'video/mp4',
      'video/webm',
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Type de fichier non autorisé'));
    }
  }
});

/**
 * POST /api/upload
 * Upload d'un fichier
 */
router.post('/', authenticate, upload.single('file'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('Aucun fichier fourni', 400);
  }

  try {
    // Enregistrer les informations du fichier en base
    const mediaFile = await prisma.mediaFile.create({
      data: {
        name: req.file.filename,
        originalName: req.file.originalname,
        url: `/uploads/${req.file.filename}`,
        type: req.file.mimetype.startsWith('image/') ? 'image' : 
              req.file.mimetype.startsWith('video/') ? 'video' : 'document',
        size: req.file.size,
        mimeType: req.file.mimetype,
      },
    });

    logger.info(`File uploaded: ${req.file.originalname} by ${req.user?.email}`);

    res.status(201).json({
      success: true,
      message: 'Fichier uploadé avec succès',
      data: {
        file: {
          id: mediaFile.id,
          name: mediaFile.name,
          originalName: mediaFile.originalName,
          url: mediaFile.url,
          type: mediaFile.type,
          size: mediaFile.size,
        },
      },
    });
  } catch (error) {
    // Supprimer le fichier en cas d'erreur de base de données
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    throw error;
  }
}));

/**
 * POST /api/upload/multiple
 * Upload de plusieurs fichiers
 */
router.post('/multiple', authenticate, upload.array('files', 10), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    throw createError('Aucun fichier fourni', 400);
  }

  try {
    // Enregistrer tous les fichiers en base
    const mediaFiles = await Promise.all(
      files.map(file => 
        prisma.mediaFile.create({
          data: {
            name: file.filename,
            originalName: file.originalname,
            url: `/uploads/${file.filename}`,
            type: file.mimetype.startsWith('image/') ? 'image' : 
                  file.mimetype.startsWith('video/') ? 'video' : 'document',
            size: file.size,
            mimeType: file.mimetype,
          },
        })
      )
    );

    logger.info(`${files.length} files uploaded by ${req.user?.email}`);

    res.status(201).json({
      success: true,
      message: `${files.length} fichiers uploadés avec succès`,
      data: {
        files: mediaFiles.map(file => ({
          id: file.id,
          name: file.name,
          originalName: file.originalName,
          url: file.url,
          type: file.type,
          size: file.size,
        })),
      },
    });
  } catch (error) {
    // Supprimer les fichiers en cas d'erreur
    files.forEach(file => {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
    });
    throw error;
  }
}));

/**
 * GET /api/upload/media
 * Récupérer tous les fichiers média
 */
router.get('/media', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const type = req.query.type as string;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (type) {
    where.type = type;
  }

  const [files, total] = await Promise.all([
    prisma.mediaFile.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.mediaFile.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      files,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * DELETE /api/upload/:id
 * Supprimer un fichier
 */
router.delete('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const mediaFile = await prisma.mediaFile.findUnique({
    where: { id: req.params.id },
  });

  if (!mediaFile) {
    throw createError('Fichier non trouvé', 404);
  }

  // Supprimer le fichier physique
  const filePath = path.join(process.cwd(), 'uploads', mediaFile.name);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  // Supprimer l'enregistrement en base
  await prisma.mediaFile.delete({
    where: { id: req.params.id },
  });

  logger.info(`File deleted: ${mediaFile.originalName} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Fichier supprimé avec succès',
  });
}));

/**
 * GET /api/upload/stats
 * Statistiques des fichiers
 */
router.get('/stats', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const [totalFiles, totalSize, filesByType] = await Promise.all([
    prisma.mediaFile.count(),
    prisma.mediaFile.aggregate({
      _sum: { size: true },
    }),
    prisma.mediaFile.groupBy({
      by: ['type'],
      _count: { type: true },
    }),
  ]);

  res.json({
    success: true,
    data: {
      totalFiles,
      totalSize: totalSize._sum.size || 0,
      filesByType: filesByType.map(item => ({
        type: item.type,
        count: item._count.type,
      })),
    },
  });
}));

export default router;
