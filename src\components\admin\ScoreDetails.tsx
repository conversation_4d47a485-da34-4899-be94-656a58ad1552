import React from 'react';
import { motion } from 'framer-motion';
import { ChartBar, TrendingUp, TrendingDown } from 'lucide-react';
import LeadScoringService from '../../utils/leadScoringService';
import type { Lead } from '../../types/admin';

interface ScoreDetailsProps {
  lead: Lead;
}

export const ScoreDetails: React.FC<ScoreDetailsProps> = ({ lead }) => {
  const scoringService = LeadScoringService.getInstance();
  const detailedScoring = scoringService.getDetailedScoring(lead);
  const scoreHistory = scoringService.getLeadScoreHistory(lead.id);
  const latestHistory = scoreHistory[scoreHistory.length - 1];

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-blue-100';
    if (score >= 40) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      {/* Score global */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Score du prospect</h3>
        <div className={`flex items-center space-x-2 ${getScoreColor(lead.score)}`}>
          <ChartBar className="h-5 w-5" />
          <span className="text-2xl font-bold">{lead.score}</span>
        </div>
      </div>

      {/* Évolution du score */}
      {latestHistory && (
        <div className={`flex items-center space-x-2 p-3 rounded-lg ${
          latestHistory.newScore > latestHistory.oldScore ? 'bg-green-50' : 'bg-red-50'
        }`}>
          {latestHistory.newScore > latestHistory.oldScore ? (
            <TrendingUp className="h-5 w-5 text-green-600" />
          ) : (
            <TrendingDown className="h-5 w-5 text-red-600" />
          )}
          <span className={`text-sm ${
            latestHistory.newScore > latestHistory.oldScore ? 'text-green-700' : 'text-red-700'
          }`}>
            {latestHistory.newScore > latestHistory.oldScore ? '+' : ''}
            {latestHistory.newScore - latestHistory.oldScore} points
          </span>
          <span className="text-sm text-gray-500">
            {new Date(latestHistory.date).toLocaleDateString()}
          </span>
        </div>
      )}

      {/* Critères détaillés */}
      <div className="space-y-4">
        {detailedScoring.map((criteria, index) => (
          <div key={criteria.name} className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">
                {criteria.name}
              </span>
              <span className={`text-sm font-medium ${getScoreColor(criteria.score)}`}>
                {criteria.score}/100
              </span>
            </div>
            <motion.div
              className="h-2 rounded-full bg-gray-200 overflow-hidden"
              initial={{ opacity: 0, scaleX: 0 }}
              animate={{ opacity: 1, scaleX: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <motion.div
                className={`h-full rounded-full ${getScoreBgColor(criteria.score)}`}
                initial={{ width: 0 }}
                animate={{ width: `${criteria.score}%` }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              />
            </motion.div>
            <div className="text-xs text-gray-500">
              Coefficient : {(criteria.weight * 100).toFixed(0)}%
            </div>
          </div>
        ))}
      </div>

      {/* Historique */}
      {scoreHistory.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Historique des changements
          </h4>
          <div className="space-y-2">
            {scoreHistory.slice(-5).map((history) => (
              <div
                key={history.date}
                className="flex items-center justify-between text-sm"
              >
                <span className="text-gray-500">
                  {new Date(history.date).toLocaleDateString()}
                </span>
                <div className="flex items-center space-x-2">
                  <span className={history.newScore > history.oldScore ? 'text-green-600' : 'text-red-600'}>
                    {history.oldScore} → {history.newScore}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ScoreDetails;
