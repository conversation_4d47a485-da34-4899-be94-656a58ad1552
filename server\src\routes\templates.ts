import express from 'express';
import { prisma } from '../config/database';
import { validateTemplate, validatePDFGeneration } from '../utils/validation';
import { generatePDF } from '../utils/pdf-generator';
import { authenticate } from '../middleware/auth';
import { uploadMiddleware } from '../middleware/upload';

const router = express.Router();

// Obtenir tous les templates
router.get('/', authenticate, async (req, res) => {
  try {
    const templates = await prisma.template.findMany({
      orderBy: { lastModified: 'desc' }
    });
    res.json(templates);
  } catch (error) {
    console.error('Erreur lors de la récupération des templates:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Créer un nouveau template
router.post('/', authenticate, async (req, res) => {
  try {
    const validation = validateTemplate(req.body);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const template = await prisma.template.create({
      data: {
        ...req.body,
        lastModified: new Date(),
        usage: 0,
        status: 'active'
      }
    });

    res.status(201).json(template);
  } catch (error) {
    console.error('Erreur lors de la création du template:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Mettre à jour un template
router.put('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const validation = validateTemplate(req.body);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const template = await prisma.template.update({
      where: { id: parseInt(id) },
      data: {
        ...req.body,
        lastModified: new Date()
      }
    });

    res.json(template);
  } catch (error) {
    console.error('Erreur lors de la mise à jour du template:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Supprimer un template
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    await prisma.template.delete({
      where: { id: parseInt(id) }
    });
    res.status(204).send();
  } catch (error) {
    console.error('Erreur lors de la suppression du template:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Générer un PDF
router.post('/:id/generate', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const template = await prisma.template.findUnique({
      where: { id: parseInt(id) }
    });

    if (!template) {
      return res.status(404).json({ message: 'Template non trouvé' });
    }

    const validation = validatePDFGeneration(req.body, template);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const pdfBuffer = await generatePDF(template, req.body);
    
    // Mise à jour du compteur d'utilisation
    await prisma.template.update({
      where: { id: parseInt(id) },
      data: { usage: { increment: 1 } }
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=document.pdf');
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Erreur lors de la génération du PDF:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// Prévisualiser un PDF
router.post('/:id/preview', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const template = await prisma.template.findUnique({
      where: { id: parseInt(id) }
    });

    if (!template) {
      return res.status(404).json({ message: 'Template non trouvé' });
    }

    const validation = validatePDFGeneration(req.body, template);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const pdfBuffer = await generatePDF(template, req.body);
    
    // Conversion en base64 pour la prévisualisation
    const base64Pdf = pdfBuffer.toString('base64');
    res.json({ preview: `data:application/pdf;base64,${base64Pdf}` });
  } catch (error) {
    console.error('Erreur lors de la prévisualisation du PDF:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

export default router;
