import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Globe, 
  Edit, 
  Eye, 
  Plus, 
  Save, 
  Upload,
  Image,
  FileText,
  Video,
  Calendar,
  Tag,
  Search,
  Filter,
  Trash2,
  Copy,
  ExternalLink,
  Settings,
  Layout,
  Type,
  Palette,
  Monitor,
  Smartphone,
  Tablet,
  CheckCircle,
  Clock,
  AlertCircle,
  X,
  Pause,
  Play,
  ChevronDown,
  ChevronUp,
  Code,
  ImageIcon,
  Link,
  Bold,
  Italic,
  Underline,
  List,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Layers,
  FolderOpen,
  Archive,
  Star,
  MoreHorizontal
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';
import { ContentPage, BlogPost, MediaFile } from '../../../types/admin';

interface ContentItem {
  id: string;
  title: string;
  slug: string;
  type: 'page' | 'blog' | 'media';
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  excerpt?: string;
  image?: string;
  category?: string;
  tags?: string[];
  size?: number;
  mimeType?: string;
}

interface EditorState {
  content: string;
  title: string;
  slug: string;
  excerpt: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  seo: {
    title: string;
    description: string;
    keywords: string;
  };
  tags: string[];
  category: string;
  image: string;
}

export const ContentManagementGlobal: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('all');
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<ContentItem[]>([]);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [editorState, setEditorState] = useState<EditorState>({
    content: '',
    title: '',
    slug: '',
    excerpt: '',
    status: 'DRAFT',
    seo: { title: '', description: '', keywords: '' },
    tags: [],
    category: '',
    image: ''
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile[]>([]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  // Chargement des données
  useEffect(() => {
    loadAllContent();
  }, []);

  // Filtrage des contenus
  useEffect(() => {
    filterContent();
  }, [contentItems, searchTerm, filterStatus, filterType, selectedTab]);

  const loadAllContent = async () => {
    setIsLoading(true);
    try {
      const [pagesResponse, blogResponse, mediaResponse] = await Promise.all([
        api.getContentPages(),
        api.getBlogPosts(),
        api.getMediaFiles()
      ]);

      const allItems: ContentItem[] = [];

      // Pages
      if (pagesResponse.success && pagesResponse.data?.pages) {
        const pages = pagesResponse.data.pages.map((page: ContentPage) => ({
          ...page,
          type: 'page' as const
        }));
        allItems.push(...pages);
      }

      // Articles de blog
      if (blogResponse.success && blogResponse.data?.posts) {
        const posts = blogResponse.data.posts.map((post: BlogPost) => ({
          ...post,
          type: 'blog' as const
        }));
        allItems.push(...posts);
      }

      // Fichiers média
      if (mediaResponse.success && mediaResponse.data?.files) {
        const media = mediaResponse.data.files.map((file: MediaFile) => ({
          id: file.id,
          title: file.originalName,
          slug: file.name,
          type: 'media' as const,
          status: 'PUBLISHED' as const,
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
          size: file.size,
          mimeType: file.mimeType,
          image: file.url
        }));
        allItems.push(...media);
        setMediaFiles(mediaResponse.data.files);
      }

      setContentItems(allItems);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement du contenu', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const filterContent = () => {
    let filtered = contentItems;

    // Filtre par onglet
    if (selectedTab !== 'all') {
      filtered = filtered.filter(item => item.type === selectedTab);
    }

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.excerpt && item.excerpt.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par statut
    if (filterStatus !== 'all') {
      filtered = filtered.filter(item => item.status === filterStatus);
    }

    // Filtre par type
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType);
    }

    setFilteredItems(filtered);
  };

  const handleSaveContent = async () => {
    if (!editorState.title || !editorState.slug) {
      showToast('Titre et slug sont requis', 'error');
      return;
    }

    setIsLoading(true);
    try {
      let response;
      const contentData = {
        title: editorState.title,
        slug: editorState.slug,
        content: editorState.content,
        excerpt: editorState.excerpt,
        status: editorState.status,
        seo: editorState.seo,
        ...(selectedTab === 'blog' && {
          tags: editorState.tags,
          category: editorState.category,
          image: editorState.image
        })
      };

      if (editingItem) {
        // Mise à jour
        if (editingItem.type === 'page') {
          response = await api.updateContentPage(editingItem.id, contentData);
        } else if (editingItem.type === 'blog') {
          response = await api.updateBlogPost(editingItem.id, contentData);
        }
      } else {
        // Création
        if (selectedTab === 'page' || selectedTab === 'all') {
          response = await api.createContentPage(contentData);
        } else if (selectedTab === 'blog') {
          response = await api.createBlogPost(contentData);
        }
      }

      if (response?.success) {
        showToast(editingItem ? 'Contenu mis à jour' : 'Contenu créé', 'success');
        setShowEditor(false);
        setEditingItem(null);
        resetEditor();
        loadAllContent();
      } else {
        showToast(response?.message || 'Erreur lors de la sauvegarde', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      showToast('Erreur lors de la sauvegarde', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteContent = async (item: ContentItem) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer "${item.title}" ?`)) return;

    try {
      let response;
      if (item.type === 'page') {
        response = await api.deleteContentPage(item.id);
      } else if (item.type === 'blog') {
        response = await api.deleteBlogPost(item.id);
      } else if (item.type === 'media') {
        response = await api.deleteMediaFile(item.id);
      }

      if (response?.success) {
        showToast('Contenu supprimé', 'success');
        loadAllContent();
      } else {
        showToast('Erreur lors de la suppression', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      showToast('Erreur lors de la suppression', 'error');
    }
  };

  const handleFileUpload = async (files: FileList) => {
    for (const file of Array.from(files)) {
      try {
        const response = await api.uploadFile(file);
        if (response.success) {
          showToast(`${file.name} uploadé avec succès`, 'success');
        } else {
          showToast(`Erreur lors de l'upload de ${file.name}`, 'error');
        }
      } catch (error) {
        console.error('Erreur lors de l\'upload:', error);
        showToast(`Erreur lors de l'upload de ${file.name}`, 'error');
      }
    }
    loadAllContent();
  };

  const openEditor = (item?: ContentItem) => {
    if (item) {
      setEditingItem(item);
      setEditorState({
        content: (item as any).content || '',
        title: item.title,
        slug: item.slug,
        excerpt: item.excerpt || '',
        status: item.status,
        seo: (item as any).seo || { title: '', description: '', keywords: '' },
        tags: (item as any).tags || [],
        category: (item as any).category || '',
        image: item.image || ''
      });
    } else {
      resetEditor();
    }
    setShowEditor(true);
  };

  const resetEditor = () => {
    setEditingItem(null);
    setEditorState({
      content: '',
      title: '',
      slug: '',
      excerpt: '',
      status: 'DRAFT',
      seo: { title: '', description: '', keywords: '' },
      tags: [],
      category: '',
      image: ''
    });
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setEditorState(prev => ({
      ...prev,
      title,
      slug: !editingItem ? generateSlug(title) : prev.slug
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800';
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800';
      case 'ARCHIVED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'page': return <Layout className="h-4 w-4" />;
      case 'blog': return <FileText className="h-4 w-4" />;
      case 'media': return <Image className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const tabs = [
    { id: 'all', name: 'Tout', icon: Layers, count: contentItems.length },
    { id: 'page', name: 'Pages', icon: Layout, count: contentItems.filter(i => i.type === 'page').length },
    { id: 'blog', name: 'Blog', icon: FileText, count: contentItems.filter(i => i.type === 'blog').length },
    { id: 'media', name: 'Médias', icon: Image, count: contentItems.filter(i => i.type === 'media').length }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion de Contenu Globale</h1>
          <p className="text-gray-600 mt-2">Gérez tous vos contenus depuis une interface unifiée</p>
        </div>
        <div className="flex space-x-3">
          <label className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors cursor-pointer">
            <Upload className="h-4 w-4 mr-2" />
            Upload Média
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx"
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              className="hidden"
            />
          </label>
          <button
            onClick={() => openEditor()}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Contenu
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
                {tab.count > 0 && (
                  <span className="bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher du contenu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            >
              <option value="all">Tous les statuts</option>
              <option value="PUBLISHED">Publié</option>
              <option value="DRAFT">Brouillon</option>
              <option value="ARCHIVED">Archivé</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            >
              <option value="all">Tous les types</option>
              <option value="page">Pages</option>
              <option value="blog">Blog</option>
              <option value="media">Médias</option>
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-axa-blue text-white' : 'bg-gray-100 text-gray-600'}`}
            >
              <Layers className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-axa-blue text-white' : 'bg-gray-100 text-gray-600'}`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Content Grid/List */}
      <div className="bg-white rounded-xl shadow-lg">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
          </div>
        ) : (
          <div className="p-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredItems.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    {item.image && (
                      <div className="aspect-video bg-gray-100">
                        <img 
                          src={item.image} 
                          alt={item.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(item.type)}
                          <span className="text-xs text-gray-500 uppercase">{item.type}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status}
                        </span>
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">{item.title}</h3>
                      {item.excerpt && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.excerpt}</p>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{new Date(item.updatedAt).toLocaleDateString('fr-FR')}</span>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => openEditor(item)}
                            className="p-1 hover:bg-gray-100 rounded"
                          >
                            <Edit className="h-3 w-3" />
                          </button>
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <Eye className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteContent(item)}
                            className="p-1 hover:bg-gray-100 rounded text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredItems.map((item) => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        {item.image && (
                          <img 
                            src={item.image} 
                            alt={item.title}
                            className="w-16 h-16 object-cover rounded-lg"
                          />
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            {getTypeIcon(item.type)}
                            <span className="text-xs text-gray-500 uppercase">{item.type}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                              {item.status}
                            </span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-1">{item.title}</h3>
                          {item.excerpt && (
                            <p className="text-sm text-gray-600 mb-2">{item.excerpt}</p>
                          )}
                          <div className="text-xs text-gray-500">
                            Modifié le {new Date(item.updatedAt).toLocaleDateString('fr-FR')}
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openEditor(item)}
                          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteContent(item)}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {filteredItems.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Aucun contenu trouvé</p>
                <button
                  onClick={() => openEditor()}
                  className="mt-4 inline-flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Créer du contenu
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Editor Modal */}
      <AnimatePresence>
        {showEditor && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
            >
              {/* Editor Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    {editingItem ? 'Modifier le contenu' : 'Nouveau contenu'}
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {editingItem ? `Modification de "${editingItem.title}"` : 'Créer un nouveau contenu'}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <select
                    value={editorState.status}
                    onChange={(e) => setEditorState(prev => ({ ...prev, status: e.target.value as any }))}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                  >
                    <option value="DRAFT">Brouillon</option>
                    <option value="PUBLISHED">Publié</option>
                    <option value="ARCHIVED">Archivé</option>
                  </select>
                  <button
                    onClick={handleSaveContent}
                    disabled={isLoading}
                    className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors disabled:opacity-50"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? 'Sauvegarde...' : 'Sauvegarder'}
                  </button>
                  <button
                    onClick={() => setShowEditor(false)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Editor Content */}
              <div className="flex h-[calc(90vh-120px)]">
                {/* Main Editor */}
                <div className="flex-1 flex flex-col">
                  <div className="p-6 border-b border-gray-200">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Titre *
                        </label>
                        <input
                          type="text"
                          value={editorState.title}
                          onChange={(e) => handleTitleChange(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                          placeholder="Titre du contenu"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Slug *
                        </label>
                        <input
                          type="text"
                          value={editorState.slug}
                          onChange={(e) => setEditorState(prev => ({ ...prev, slug: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                          placeholder="slug-du-contenu"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 p-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contenu
                    </label>
                    <div className="border border-gray-300 rounded-lg overflow-hidden">
                      {/* Toolbar */}
                      <div className="bg-gray-50 border-b border-gray-300 p-3 flex items-center space-x-2">
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <Bold className="h-4 w-4" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <Italic className="h-4 w-4" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <Underline className="h-4 w-4" />
                        </button>
                        <div className="w-px h-6 bg-gray-300"></div>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <AlignLeft className="h-4 w-4" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <AlignCenter className="h-4 w-4" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <AlignRight className="h-4 w-4" />
                        </button>
                        <div className="w-px h-6 bg-gray-300"></div>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <List className="h-4 w-4" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded">
                          <Link className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setShowMediaLibrary(true)}
                          className="p-2 hover:bg-gray-200 rounded"
                        >
                          <ImageIcon className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Content Area */}
                      <textarea
                        value={editorState.content}
                        onChange={(e) => setEditorState(prev => ({ ...prev, content: e.target.value }))}
                        className="w-full h-96 p-4 border-0 focus:ring-0 resize-none"
                        placeholder="Écrivez votre contenu ici..."
                      />
                    </div>
                  </div>
                </div>

                {/* Sidebar */}
                <div className="w-80 border-l border-gray-200 bg-gray-50 overflow-y-auto">
                  <div className="p-6 space-y-6">
                    {/* Excerpt */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Extrait
                      </label>
                      <textarea
                        value={editorState.excerpt}
                        onChange={(e) => setEditorState(prev => ({ ...prev, excerpt: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                        rows={3}
                        placeholder="Résumé du contenu"
                      />
                    </div>

                    {/* Image */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Image à la une
                      </label>
                      <div className="space-y-2">
                        <input
                          type="url"
                          value={editorState.image}
                          onChange={(e) => setEditorState(prev => ({ ...prev, image: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                          placeholder="URL de l'image"
                        />
                        <button
                          onClick={() => setShowMediaLibrary(true)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          Choisir depuis la médiathèque
                        </button>
                        {editorState.image && (
                          <img
                            src={editorState.image}
                            alt="Preview"
                            className="w-full h-32 object-cover rounded-lg"
                          />
                        )}
                      </div>
                    </div>

                    {/* Tags (pour blog) */}
                    {(selectedTab === 'blog' || editingItem?.type === 'blog') && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tags
                          </label>
                          <input
                            type="text"
                            value={editorState.tags.join(', ')}
                            onChange={(e) => setEditorState(prev => ({
                              ...prev,
                              tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            placeholder="tag1, tag2, tag3"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Catégorie
                          </label>
                          <input
                            type="text"
                            value={editorState.category}
                            onChange={(e) => setEditorState(prev => ({ ...prev, category: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            placeholder="Catégorie"
                          />
                        </div>
                      </>
                    )}

                    {/* SEO */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-3">SEO</h3>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Titre SEO
                          </label>
                          <input
                            type="text"
                            value={editorState.seo.title}
                            onChange={(e) => setEditorState(prev => ({
                              ...prev,
                              seo: { ...prev.seo, title: e.target.value }
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent text-sm"
                            placeholder="Titre pour les moteurs de recherche"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Description SEO
                          </label>
                          <textarea
                            value={editorState.seo.description}
                            onChange={(e) => setEditorState(prev => ({
                              ...prev,
                              seo: { ...prev.seo, description: e.target.value }
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent text-sm"
                            rows={2}
                            placeholder="Description pour les moteurs de recherche"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">
                            Mots-clés
                          </label>
                          <input
                            type="text"
                            value={editorState.seo.keywords}
                            onChange={(e) => setEditorState(prev => ({
                              ...prev,
                              seo: { ...prev.seo, keywords: e.target.value }
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent text-sm"
                            placeholder="mot-clé1, mot-clé2"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Media Library Modal */}
      <AnimatePresence>
        {showMediaLibrary && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Médiathèque</h2>
                <button
                  onClick={() => setShowMediaLibrary(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {mediaFiles.map((file) => (
                    <div
                      key={file.id}
                      onClick={() => {
                        setEditorState(prev => ({ ...prev, image: file.url }));
                        setShowMediaLibrary(false);
                      }}
                      className="border border-gray-200 rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                    >
                      <div className="aspect-square bg-gray-100">
                        {file.type === 'image' ? (
                          <img
                            src={file.url}
                            alt={file.originalName}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <FileText className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="p-2">
                        <p className="text-xs text-gray-600 truncate">{file.originalName}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};
