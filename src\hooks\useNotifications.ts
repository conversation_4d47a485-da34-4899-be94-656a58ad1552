import { useState, useEffect } from 'react';
import NotificationManager from '../utils/notificationManager';

export function useNotifications() {
  const [notifications, setNotifications] = useState(
    NotificationManager.getInstance().getUnreadNotifications()
  );

  useEffect(() => {
    const handleNewNotification = (event: CustomEvent) => {
      setNotifications(prev => [...prev, event.detail]);
    };

    window.addEventListener('newNotification', handleNewNotification as EventListener);

    return () => {
      window.removeEventListener('newNotification', handleNewNotification as EventListener);
    };
  }, []);

  const markAsRead = (notificationId: string) => {
    NotificationManager.getInstance().markAsRead(notificationId);
    setNotifications(NotificationManager.getInstance().getUnreadNotifications());
  };

  const markAllAsRead = () => {
    notifications.forEach(n => NotificationManager.getInstance().markAsRead(n.id));
    setNotifications([]);
  };

  return {
    notifications,
    markAsRead,
    markAllAsRead,
  };
}

export default useNotifications;
