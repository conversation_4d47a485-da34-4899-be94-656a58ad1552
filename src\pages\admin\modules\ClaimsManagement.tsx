import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  FileText,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Building,
  Target,
  Activity,
  Camera,
  Upload,
  Download,
  Phone,
  Mail
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface Claim {
  id: string;
  number: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  policyNumber: string;
  incidentDate: string;
  reportDate: string;
  type: 'AUTO' | 'HOME' | 'HEALTH' | 'BUSINESS' | 'TRAVEL' | 'OTHER';
  category: 'ACCIDENT' | 'THEFT' | 'DAMAGE' | 'MEDICAL' | 'LIABILITY' | 'OTHER';
  status: 'REPORTED' | 'INVESTIGATING' | 'PROCESSING' | 'APPROVED' | 'REJECTED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  description: string;
  location: string;
  estimatedAmount: number;
  approvedAmount?: number;
  deductible: number;
  assignedTo: string;
  investigator?: string;
  documents: ClaimDocument[];
  notes: string;
  timeline: ClaimEvent[];
  tags: string[];
}

interface ClaimDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  url: string;
}

interface ClaimEvent {
  id: string;
  date: string;
  type: string;
  description: string;
  user: string;
}

export const ClaimsManagement: React.FC = () => {
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [priorityFilter, setPriorityFilter] = useState('ALL');
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{show: boolean, message: string, type: 'success' | 'error'}>({
    show: false, message: '', type: 'success'
  });

  const [newClaim, setNewClaim] = useState<Partial<Claim>>({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    policyNumber: '',
    incidentDate: '',
    reportDate: new Date().toISOString().split('T')[0],
    type: 'AUTO',
    category: 'ACCIDENT',
    status: 'REPORTED',
    priority: 'MEDIUM',
    description: '',
    location: '',
    estimatedAmount: 0,
    deductible: 0,
    assignedTo: 'current-user',
    documents: [],
    notes: '',
    timeline: [],
    tags: []
  });

  // Données de démonstration
  const demoClaims: Claim[] = [
    {
      id: '1',
      number: 'SIN-2024-001',
      clientName: 'Ahmed Benali',
      clientEmail: '<EMAIL>',
      clientPhone: '+212 6 12 34 56 78',
      policyNumber: 'POL-AUTO-2023-1234',
      incidentDate: '2024-01-08',
      reportDate: '2024-01-09',
      type: 'AUTO',
      category: 'ACCIDENT',
      status: 'INVESTIGATING',
      priority: 'HIGH',
      description: 'Collision avec un autre véhicule au carrefour Hassan II. Dégâts importants sur l\'avant du véhicule.',
      location: 'Carrefour Hassan II, Casablanca',
      estimatedAmount: 25000,
      deductible: 2000,
      assignedTo: 'Mohammed MOUMEN',
      investigator: 'Expert SAHAM',
      documents: [
        {
          id: '1',
          name: 'Constat amiable.pdf',
          type: 'PDF',
          size: 1024000,
          uploadDate: '2024-01-09',
          url: '/documents/constat-1.pdf'
        },
        {
          id: '2',
          name: 'Photos véhicule.zip',
          type: 'ZIP',
          size: 5120000,
          uploadDate: '2024-01-09',
          url: '/documents/photos-1.zip'
        }
      ],
      notes: 'Client coopératif. Expertise programmée pour demain.',
      timeline: [
        {
          id: '1',
          date: '2024-01-09',
          type: 'REPORTED',
          description: 'Sinistre déclaré par le client',
          user: 'Ahmed Benali'
        },
        {
          id: '2',
          date: '2024-01-09',
          type: 'ASSIGNED',
          description: 'Dossier assigné à Mohammed MOUMEN',
          user: 'Système'
        },
        {
          id: '3',
          date: '2024-01-10',
          type: 'INVESTIGATION',
          description: 'Début de l\'enquête - Expert contacté',
          user: 'Mohammed MOUMEN'
        }
      ],
      tags: ['Accident', 'Véhicule', 'Expertise']
    },
    {
      id: '2',
      number: 'SIN-2024-002',
      clientName: 'Fatima El Mansouri',
      clientEmail: '<EMAIL>',
      clientPhone: '+212 6 87 65 43 21',
      policyNumber: 'POL-HOME-2023-5678',
      incidentDate: '2024-01-05',
      reportDate: '2024-01-06',
      type: 'HOME',
      category: 'DAMAGE',
      status: 'APPROVED',
      priority: 'MEDIUM',
      description: 'Dégâts des eaux suite à rupture de canalisation dans la cuisine.',
      location: 'Appartement 4ème étage, Résidence Al Manar, Rabat',
      estimatedAmount: 15000,
      approvedAmount: 13000,
      deductible: 1000,
      assignedTo: 'Sarah ALAMI',
      investigator: 'Expert Habitat',
      documents: [
        {
          id: '1',
          name: 'Rapport plombier.pdf',
          type: 'PDF',
          size: 512000,
          uploadDate: '2024-01-06',
          url: '/documents/rapport-plombier.pdf'
        },
        {
          id: '2',
          name: 'Photos dégâts.jpg',
          type: 'JPG',
          size: 2048000,
          uploadDate: '2024-01-06',
          url: '/documents/photos-degats.jpg'
        }
      ],
      notes: 'Sinistre approuvé. Paiement en cours.',
      timeline: [
        {
          id: '1',
          date: '2024-01-06',
          type: 'REPORTED',
          description: 'Sinistre déclaré',
          user: 'Fatima El Mansouri'
        },
        {
          id: '2',
          date: '2024-01-07',
          type: 'INVESTIGATION',
          description: 'Expertise réalisée',
          user: 'Expert Habitat'
        },
        {
          id: '3',
          date: '2024-01-10',
          type: 'APPROVED',
          description: 'Sinistre approuvé - 13 000 DH',
          user: 'Sarah ALAMI'
        }
      ],
      tags: ['Habitation', 'Dégâts des eaux', 'Approuvé']
    },
    {
      id: '3',
      number: 'SIN-2024-003',
      clientName: 'Youssef Tazi',
      clientEmail: '<EMAIL>',
      clientPhone: '+212 5 22 33 44 55',
      policyNumber: 'POL-HEALTH-2023-9012',
      incidentDate: '2024-01-12',
      reportDate: '2024-01-12',
      type: 'HEALTH',
      category: 'MEDICAL',
      status: 'PROCESSING',
      priority: 'URGENT',
      description: 'Hospitalisation d\'urgence suite à accident de travail.',
      location: 'Clinique Al Madina, Casablanca',
      estimatedAmount: 35000,
      deductible: 500,
      assignedTo: 'Karim BENJELLOUN',
      documents: [
        {
          id: '1',
          name: 'Certificat médical.pdf',
          type: 'PDF',
          size: 256000,
          uploadDate: '2024-01-12',
          url: '/documents/certificat-medical.pdf'
        }
      ],
      notes: 'Urgence médicale. Traitement prioritaire.',
      timeline: [
        {
          id: '1',
          date: '2024-01-12',
          type: 'REPORTED',
          description: 'Sinistre déclaré en urgence',
          user: 'Youssef Tazi'
        },
        {
          id: '2',
          date: '2024-01-12',
          type: 'PROCESSING',
          description: 'Traitement prioritaire initié',
          user: 'Karim BENJELLOUN'
        }
      ],
      tags: ['Santé', 'Urgence', 'Hospitalisation']
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setClaims(demoClaims);
      setFilteredClaims(demoClaims);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Filtrer les sinistres
    let filtered = claims;

    if (searchTerm) {
      filtered = filtered.filter(claim =>
        claim.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        claim.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        claim.policyNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        claim.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(claim => claim.status === statusFilter);
    }

    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(claim => claim.type === typeFilter);
    }

    if (priorityFilter !== 'ALL') {
      filtered = filtered.filter(claim => claim.priority === priorityFilter);
    }

    setFilteredClaims(filtered);
  }, [claims, searchTerm, statusFilter, typeFilter, priorityFilter]);

  const statusOptions = [
    { value: 'REPORTED', label: 'Déclaré', color: 'bg-blue-100 text-blue-800' },
    { value: 'INVESTIGATING', label: 'Enquête', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'PROCESSING', label: 'Traitement', color: 'bg-purple-100 text-purple-800' },
    { value: 'APPROVED', label: 'Approuvé', color: 'bg-green-100 text-green-800' },
    { value: 'REJECTED', label: 'Refusé', color: 'bg-red-100 text-red-800' },
    { value: 'CLOSED', label: 'Clôturé', color: 'bg-gray-100 text-gray-800' }
  ];

  const typeOptions = [
    { value: 'AUTO', label: 'Automobile', color: 'bg-blue-100 text-blue-800' },
    { value: 'HOME', label: 'Habitation', color: 'bg-green-100 text-green-800' },
    { value: 'HEALTH', label: 'Santé', color: 'bg-red-100 text-red-800' },
    { value: 'BUSINESS', label: 'Entreprise', color: 'bg-purple-100 text-purple-800' },
    { value: 'TRAVEL', label: 'Voyage', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'OTHER', label: 'Autre', color: 'bg-gray-100 text-gray-800' }
  ];

  const priorityOptions = [
    { value: 'LOW', label: 'Faible', color: 'bg-gray-100 text-gray-800' },
    { value: 'MEDIUM', label: 'Moyenne', color: 'bg-blue-100 text-blue-800' },
    { value: 'HIGH', label: 'Élevée', color: 'bg-orange-100 text-orange-800' },
    { value: 'URGENT', label: 'Urgente', color: 'bg-red-100 text-red-800' }
  ];

  const getStatusInfo = (status: string) => {
    return statusOptions.find(s => s.value === status) || statusOptions[0];
  };

  const getTypeInfo = (type: string) => {
    return typeOptions.find(t => t.value === type) || typeOptions[0];
  };

  const getPriorityInfo = (priority: string) => {
    return priorityOptions.find(p => p.value === priority) || priorityOptions[0];
  };

  const generateClaimNumber = () => {
    const year = new Date().getFullYear();
    const count = claims.length + 1;
    return `SIN-${year}-${count.toString().padStart(3, '0')}`;
  };

  const handleAddClaim = async () => {
    try {
      const claimToAdd: Claim = {
        ...newClaim as Claim,
        id: Date.now().toString(),
        number: generateClaimNumber(),
        timeline: [
          {
            id: '1',
            date: new Date().toISOString().split('T')[0],
            type: 'REPORTED',
            description: 'Sinistre déclaré',
            user: newClaim.clientName || 'Client'
          }
        ]
      };

      setClaims([...claims, claimToAdd]);
      setNewClaim({
        clientName: '',
        clientEmail: '',
        clientPhone: '',
        policyNumber: '',
        incidentDate: '',
        reportDate: new Date().toISOString().split('T')[0],
        type: 'AUTO',
        category: 'ACCIDENT',
        status: 'REPORTED',
        priority: 'MEDIUM',
        description: '',
        location: '',
        estimatedAmount: 0,
        deductible: 0,
        assignedTo: 'current-user',
        documents: [],
        notes: '',
        timeline: [],
        tags: []
      });
      setShowAddModal(false);
      setToast({show: true, message: 'Sinistre ajouté avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de l\'ajout du sinistre', type: 'error'});
    }
  };

  const handleEditClaim = async () => {
    if (!selectedClaim) return;

    try {
      const updatedClaims = claims.map(claim =>
        claim.id === selectedClaim.id ? selectedClaim : claim
      );
      setClaims(updatedClaims);
      setShowEditModal(false);
      setSelectedClaim(null);
      setToast({show: true, message: 'Sinistre modifié avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la modification', type: 'error'});
    }
  };

  const handleDeleteClaim = async (claimId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce sinistre ?')) {
      try {
        setClaims(claims.filter(claim => claim.id !== claimId));
        setToast({show: true, message: 'Sinistre supprimé avec succès', type: 'success'});
      } catch (error) {
        setToast({show: true, message: 'Erreur lors de la suppression', type: 'error'});
      }
    }
  };

  const handleStatusChange = async (claimId: string, newStatus: string) => {
    try {
      const updatedClaims = claims.map(claim => {
        if (claim.id === claimId) {
          const updatedClaim = { ...claim, status: newStatus as any };
          updatedClaim.timeline.push({
            id: Date.now().toString(),
            date: new Date().toISOString().split('T')[0],
            type: newStatus,
            description: `Statut changé vers ${getStatusInfo(newStatus).label}`,
            user: 'Utilisateur actuel'
          });
          return updatedClaim;
        }
        return claim;
      });
      setClaims(updatedClaims);
      setToast({show: true, message: 'Statut mis à jour avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la mise à jour', type: 'error'});
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Sinistres</h1>
            <p className="text-gray-600">Gérez les déclarations et le traitement des sinistres</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Nouveau Sinistre
          </button>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sinistres</p>
                <p className="text-2xl font-bold text-gray-900">{claims.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">En Cours</p>
                <p className="text-2xl font-bold text-gray-900">
                  {claims.filter(c => ['REPORTED', 'INVESTIGATING', 'PROCESSING'].includes(c.status)).length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Montant Total</p>
                <p className="text-2xl font-bold text-gray-900">
                  {claims.reduce((sum, claim) => sum + claim.estimatedAmount, 0).toLocaleString()} DH
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taux Approbation</p>
                <p className="text-2xl font-bold text-gray-900">
                  {claims.length > 0 ? Math.round((claims.filter(c => c.status === 'APPROVED').length / claims.length) * 100) : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Rechercher un sinistre..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les statuts</option>
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les types</option>
              {typeOptions.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Toutes les priorités</option>
              {priorityOptions.map(priority => (
                <option key={priority.value} value={priority.value}>{priority.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des sinistres */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Numéro
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priorité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Montant Estimé
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date Incident
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClaims.map((claim) => {
                const statusInfo = getStatusInfo(claim.status);
                const typeInfo = getTypeInfo(claim.type);
                const priorityInfo = getPriorityInfo(claim.priority);

                return (
                  <motion.tr
                    key={claim.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{claim.number}</div>
                      <div className="text-sm text-gray-500">{claim.policyNumber}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{claim.clientName}</div>
                      <div className="text-sm text-gray-500">{claim.clientEmail}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeInfo.color}`}>
                        {typeInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={claim.status}
                        onChange={(e) => handleStatusChange(claim.id, e.target.value)}
                        className={`text-xs font-semibold rounded-full px-2 py-1 border-0 ${statusInfo.color}`}
                      >
                        {statusOptions.map(status => (
                          <option key={status.value} value={status.value}>{status.label}</option>
                        ))}
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityInfo.color}`}>
                        {priorityInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {claim.estimatedAmount.toLocaleString()} DH
                      {claim.approvedAmount && (
                        <div className="text-xs text-green-600">
                          Approuvé: {claim.approvedAmount.toLocaleString()} DH
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {claim.incidentDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedClaim(claim);
                            setShowDetailModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                          title="Voir détails"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedClaim(claim);
                            setShowEditModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClaim(claim.id)}
                          className="text-red-600 hover:text-red-700"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Toast */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({...toast, show: false})}
        />
      )}
    </div>
  );
};

export default ClaimsManagement;
