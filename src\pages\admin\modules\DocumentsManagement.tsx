import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Plus,
  Eye,
  Download,
  Upload,
  File,
  Image,
  Video,
  Music,
  Archive,
  Folder,
  FolderOpen,
  Share2,
  Copy,
  Move,
  Star,
  Clock,
  User,
  Calendar,
  Tag,
  Link,
  ExternalLink,
  Paperclip,
  Send,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Grid,
  List,
  SortAsc,
  SortDesc,
  RefreshCw,
  Settings,
  Shield,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Zap,
  TrendingUp,
  BarChart3,
  Activity,
  HardDrive,
  Cloud,
  Server
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface Document {
  id: string;
  name: string;
  originalName: string;
  type: 'PDF' | 'WORD' | 'EXCEL' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'OTHER';
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  category: 'CONTRACT' | 'QUOTE' | 'CLAIM' | 'CLIENT_DOC' | 'TEMPLATE' | 'REPORT' | 'OTHER';
  tags: string[];
  description?: string;
  isPublic: boolean;
  isTemplate: boolean;
  isFavorite: boolean;
  folderId?: string;
  folder?: {
    id: string;
    name: string;
    path: string;
  };
  relatedTo?: {
    type: 'CLIENT' | 'LEAD' | 'QUOTE' | 'CLAIM' | 'CONTRACT';
    id: string;
    name: string;
  };
  uploadedBy: string;
  uploadedUser?: {
    id: string;
    name: string;
    email: string;
  };
  permissions: {
    canView: string[];
    canEdit: string[];
    canDelete: string[];
  };
  versions: DocumentVersion[];
  metadata?: {
    pages?: number;
    duration?: number;
    dimensions?: { width: number; height: number };
    author?: string;
    subject?: string;
    keywords?: string[];
  };
  createdAt: string;
  updatedAt: string;
  lastAccessedAt?: string;
  downloadCount: number;
  viewCount: number;
}

interface DocumentVersion {
  id: string;
  version: string;
  url: string;
  size: number;
  uploadedBy: string;
  uploadedAt: string;
  comment?: string;
}

interface Folder {
  id: string;
  name: string;
  path: string;
  parentId?: string;
  isPublic: boolean;
  permissions: {
    canView: string[];
    canEdit: string[];
    canDelete: string[];
  };
  documentsCount: number;
  totalSize: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export const DocumentsManagement: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showNewFolderModal, setShowNewFolderModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showDocumentDetails, setShowDocumentDetails] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);

  useEffect(() => {
    loadDocuments();
    loadFolders();
  }, [currentFolder, searchTerm, typeFilter, categoryFilter, sortBy, sortOrder]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for comprehensive document management
      const mockDocuments: Document[] = [
        {
          id: '1',
          name: 'Contrat_Hassan_Benjelloun_Auto.pdf',
          originalName: 'Contrat Assurance Auto Hassan Benjelloun.pdf',
          type: 'PDF',
          mimeType: 'application/pdf',
          size: 2456789,
          url: '/documents/contrat_hassan_auto.pdf',
          thumbnailUrl: '/thumbnails/pdf_thumb.png',
          category: 'CONTRACT',
          tags: ['contrat', 'auto', 'hassan benjelloun'],
          description: 'Contrat d\'assurance automobile pour Hassan Benjelloun',
          isPublic: false,
          isTemplate: false,
          isFavorite: true,
          relatedTo: {
            type: 'CLIENT',
            id: '1',
            name: 'Hassan Benjelloun'
          },
          uploadedBy: '2',
          uploadedUser: {
            id: '2',
            name: 'Conseiller 1',
            email: '<EMAIL>'
          },
          permissions: {
            canView: ['1', '2', '4'],
            canEdit: ['1', '2'],
            canDelete: ['1']
          },
          versions: [
            {
              id: 'v1',
              version: '1.0',
              url: '/documents/contrat_hassan_auto.pdf',
              size: 2456789,
              uploadedBy: '2',
              uploadedAt: '2024-01-15T10:00:00Z',
              comment: 'Version initiale du contrat'
            }
          ],
          metadata: {
            pages: 12,
            author: 'MOUMEN TECHNIQUE ET PREVOYANCE',
            subject: 'Contrat Assurance Auto',
            keywords: ['assurance', 'auto', 'contrat']
          },
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          lastAccessedAt: '2024-01-15T14:30:00Z',
          downloadCount: 5,
          viewCount: 12
        },
        {
          id: '2',
          name: 'Devis_ABC_SARL_Flotte.pdf',
          originalName: 'Devis Assurance Flotte ABC SARL.pdf',
          type: 'PDF',
          mimeType: 'application/pdf',
          size: 1876543,
          url: '/documents/devis_abc_flotte.pdf',
          thumbnailUrl: '/thumbnails/pdf_thumb.png',
          category: 'QUOTE',
          tags: ['devis', 'flotte', 'abc sarl', 'entreprise'],
          description: 'Devis d\'assurance flotte véhicules pour ABC SARL',
          isPublic: false,
          isTemplate: false,
          isFavorite: false,
          relatedTo: {
            type: 'QUOTE',
            id: '2',
            name: 'Devis Flotte ABC SARL'
          },
          uploadedBy: '3',
          uploadedUser: {
            id: '3',
            name: 'Conseiller 2',
            email: '<EMAIL>'
          },
          permissions: {
            canView: ['1', '3', '4'],
            canEdit: ['1', '3'],
            canDelete: ['1']
          },
          versions: [
            {
              id: 'v1',
              version: '1.0',
              url: '/documents/devis_abc_flotte.pdf',
              size: 1876543,
              uploadedBy: '3',
              uploadedAt: '2024-01-14T15:30:00Z',
              comment: 'Première version du devis'
            }
          ],
          metadata: {
            pages: 8,
            author: 'MOUMEN TECHNIQUE ET PREVOYANCE',
            subject: 'Devis Assurance Flotte',
            keywords: ['devis', 'flotte', 'entreprise']
          },
          createdAt: '2024-01-14T15:30:00Z',
          updatedAt: '2024-01-14T15:30:00Z',
          lastAccessedAt: '2024-01-15T09:00:00Z',
          downloadCount: 3,
          viewCount: 7
        },
        {
          id: '3',
          name: 'Template_Contrat_Auto.docx',
          originalName: 'Modèle Contrat Assurance Auto.docx',
          type: 'WORD',
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          size: 987654,
          url: '/documents/template_contrat_auto.docx',
          thumbnailUrl: '/thumbnails/word_thumb.png',
          category: 'TEMPLATE',
          tags: ['template', 'modèle', 'contrat', 'auto'],
          description: 'Modèle de contrat d\'assurance automobile',
          isPublic: true,
          isTemplate: true,
          isFavorite: true,
          uploadedBy: '1',
          uploadedUser: {
            id: '1',
            name: 'Administrateur',
            email: '<EMAIL>'
          },
          permissions: {
            canView: ['all'],
            canEdit: ['1', '4'],
            canDelete: ['1']
          },
          versions: [
            {
              id: 'v2',
              version: '2.0',
              url: '/documents/template_contrat_auto_v2.docx',
              size: 987654,
              uploadedBy: '1',
              uploadedAt: '2024-01-10T14:00:00Z',
              comment: 'Mise à jour des clauses légales'
            },
            {
              id: 'v1',
              version: '1.0',
              url: '/documents/template_contrat_auto_v1.docx',
              size: 945321,
              uploadedBy: '1',
              uploadedAt: '2024-01-01T10:00:00Z',
              comment: 'Version initiale du modèle'
            }
          ],
          metadata: {
            pages: 15,
            author: 'Service Juridique',
            subject: 'Modèle Contrat Auto',
            keywords: ['modèle', 'contrat', 'automobile', 'template']
          },
          createdAt: '2024-01-01T10:00:00Z',
          updatedAt: '2024-01-10T14:00:00Z',
          lastAccessedAt: '2024-01-15T11:20:00Z',
          downloadCount: 23,
          viewCount: 45
        },
        {
          id: '4',
          name: 'Rapport_Sinistre_Photo.jpg',
          originalName: 'Photo Dégâts Véhicule Sinistre.jpg',
          type: 'IMAGE',
          mimeType: 'image/jpeg',
          size: 3456789,
          url: '/documents/rapport_sinistre_photo.jpg',
          thumbnailUrl: '/thumbnails/rapport_sinistre_photo_thumb.jpg',
          category: 'CLAIM',
          tags: ['sinistre', 'photo', 'dégâts', 'véhicule'],
          description: 'Photo des dégâts du véhicule pour le sinistre',
          isPublic: false,
          isTemplate: false,
          isFavorite: false,
          relatedTo: {
            type: 'CLAIM',
            id: '1',
            name: 'Sinistre Auto Hassan Benjelloun'
          },
          uploadedBy: '2',
          uploadedUser: {
            id: '2',
            name: 'Conseiller 1',
            email: '<EMAIL>'
          },
          permissions: {
            canView: ['1', '2', '4'],
            canEdit: ['1', '2'],
            canDelete: ['1', '2']
          },
          versions: [
            {
              id: 'v1',
              version: '1.0',
              url: '/documents/rapport_sinistre_photo.jpg',
              size: 3456789,
              uploadedBy: '2',
              uploadedAt: '2024-01-12T16:45:00Z',
              comment: 'Photo prise sur le lieu du sinistre'
            }
          ],
          metadata: {
            dimensions: { width: 1920, height: 1080 },
            author: 'Conseiller 1'
          },
          createdAt: '2024-01-12T16:45:00Z',
          updatedAt: '2024-01-12T16:45:00Z',
          lastAccessedAt: '2024-01-15T13:15:00Z',
          downloadCount: 2,
          viewCount: 8
        }
      ];

      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement des documents', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadFolders = async () => {
    try {
      // Mock folders data
      const mockFolders: Folder[] = [
        {
          id: '1',
          name: 'Contrats',
          path: '/Contrats',
          isPublic: false,
          permissions: {
            canView: ['1', '2', '3', '4'],
            canEdit: ['1', '4'],
            canDelete: ['1']
          },
          documentsCount: 15,
          totalSize: 45678901,
          createdBy: '1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          name: 'Devis',
          path: '/Devis',
          isPublic: false,
          permissions: {
            canView: ['1', '2', '3', '4'],
            canEdit: ['1', '2', '3', '4'],
            canDelete: ['1']
          },
          documentsCount: 28,
          totalSize: 67890123,
          createdBy: '1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T14:30:00Z'
        },
        {
          id: '3',
          name: 'Sinistres',
          path: '/Sinistres',
          isPublic: false,
          permissions: {
            canView: ['1', '2', '3', '4'],
            canEdit: ['1', '2', '3'],
            canDelete: ['1']
          },
          documentsCount: 42,
          totalSize: 123456789,
          createdBy: '1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T16:20:00Z'
        },
        {
          id: '4',
          name: 'Modèles',
          path: '/Modèles',
          isPublic: true,
          permissions: {
            canView: ['all'],
            canEdit: ['1', '4'],
            canDelete: ['1']
          },
          documentsCount: 12,
          totalSize: 23456789,
          createdBy: '1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-10T14:00:00Z'
        }
      ];

      setFolders(mockFolders);
    } catch (error) {
      console.error('Erreur lors du chargement des dossiers:', error);
      showToast('Erreur lors du chargement des dossiers', 'error');
    }
  };

  const handleDocumentUpload = async (files: FileList) => {
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        if (currentFolder) {
          formData.append('folderId', currentFolder);
        }

        // Simulate upload
        showToast(`Fichier "${file.name}" uploadé avec succès`, 'success');
      }
      
      loadDocuments();
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      showToast('Erreur lors de l\'upload des fichiers', 'error');
    }
  };

  const handleDocumentDelete = async (documentId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return;

    try {
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      showToast('Document supprimé avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      showToast('Erreur lors de la suppression du document', 'error');
    }
  };

  const handleDocumentDownload = async (document: Document) => {
    try {
      // Simulate download
      const link = document.createElement('a');
      link.href = document.url;
      link.download = document.originalName;
      link.click();
      
      // Update download count
      setDocuments(prev => prev.map(doc => 
        doc.id === document.id 
          ? { ...doc, downloadCount: doc.downloadCount + 1 }
          : doc
      ));
      
      showToast('Téléchargement démarré', 'success');
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      showToast('Erreur lors du téléchargement', 'error');
    }
  };

  const toggleDocumentFavorite = async (documentId: string) => {
    try {
      setDocuments(prev => prev.map(doc => 
        doc.id === documentId 
          ? { ...doc, isFavorite: !doc.isFavorite }
          : doc
      ));
      showToast('Favori mis à jour', 'success');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      showToast('Erreur lors de la mise à jour des favoris', 'error');
    }
  };

  const getFileIcon = (type: string, mimeType: string) => {
    switch (type) {
      case 'PDF': return <FileText className="h-6 w-6 text-red-600" />;
      case 'WORD': return <FileText className="h-6 w-6 text-blue-600" />;
      case 'EXCEL': return <FileText className="h-6 w-6 text-green-600" />;
      case 'IMAGE': return <Image className="h-6 w-6 text-purple-600" />;
      case 'VIDEO': return <Video className="h-6 w-6 text-orange-600" />;
      case 'AUDIO': return <Music className="h-6 w-6 text-pink-600" />;
      default: return <File className="h-6 w-6 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'CONTRACT': return 'bg-blue-100 text-blue-800';
      case 'QUOTE': return 'bg-green-100 text-green-800';
      case 'CLAIM': return 'bg-red-100 text-red-800';
      case 'CLIENT_DOC': return 'bg-purple-100 text-purple-800';
      case 'TEMPLATE': return 'bg-yellow-100 text-yellow-800';
      case 'REPORT': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'CONTRACT': return 'Contrat';
      case 'QUOTE': return 'Devis';
      case 'CLAIM': return 'Sinistre';
      case 'CLIENT_DOC': return 'Document Client';
      case 'TEMPLATE': return 'Modèle';
      case 'REPORT': return 'Rapport';
      default: return category;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const documentTypes = [
    { value: 'PDF', label: 'PDF' },
    { value: 'WORD', label: 'Word' },
    { value: 'EXCEL', label: 'Excel' },
    { value: 'IMAGE', label: 'Image' },
    { value: 'VIDEO', label: 'Vidéo' },
    { value: 'AUDIO', label: 'Audio' },
    { value: 'OTHER', label: 'Autre' }
  ];

  const documentCategories = [
    { value: 'CONTRACT', label: 'Contrat' },
    { value: 'QUOTE', label: 'Devis' },
    { value: 'CLAIM', label: 'Sinistre' },
    { value: 'CLIENT_DOC', label: 'Document Client' },
    { value: 'TEMPLATE', label: 'Modèle' },
    { value: 'REPORT', label: 'Rapport' },
    { value: 'OTHER', label: 'Autre' }
  ];

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesType = typeFilter === 'all' || doc.type === typeFilter;
    const matchesCategory = categoryFilter === 'all' || doc.category === categoryFilter;
    const matchesFolder = !currentFolder || doc.folderId === currentFolder;

    return matchesSearch && matchesType && matchesCategory && matchesFolder;
  });

  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'size':
        aValue = a.size;
        bValue = b.size;
        break;
      case 'date':
        aValue = new Date(a.updatedAt).getTime();
        bValue = new Date(b.updatedAt).getTime();
        break;
      case 'type':
        aValue = a.type;
        bValue = b.type;
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <FileText className="h-8 w-8 mr-3 text-indigo-600" />
            Gestion des Documents
          </h1>
          <p className="text-gray-600 mt-2">Organisez et gérez tous vos documents d'assurance</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowNewFolderModal(true)}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Folder className="h-4 w-4 mr-2" />
            Nouveau Dossier
          </button>
          <button
            onClick={() => setShowUploadModal(true)}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
          >
            <Upload className="h-4 w-4 mr-2" />
            Uploader
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Documents</p>
              <p className="text-2xl font-bold text-gray-900">{documents.length}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600">+15%</span>
            <span className="text-gray-500 ml-1">ce mois</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Espace Utilisé</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatFileSize(documents.reduce((sum, doc) => sum + doc.size, 0))}
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <HardDrive className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <Activity className="h-4 w-4 text-purple-500 mr-1" />
            <span className="text-purple-600">75% utilisé</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Téléchargements</p>
              <p className="text-2xl font-bold text-gray-900">
                {documents.reduce((sum, doc) => sum + doc.downloadCount, 0)}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <Download className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-green-600">+22%</span>
            <span className="text-gray-500 ml-1">cette semaine</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Favoris</p>
              <p className="text-2xl font-bold text-gray-900">
                {documents.filter(doc => doc.isFavorite).length}
              </p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <Star className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="text-yellow-600">Documents favoris</span>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un document..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
          </div>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Tous les types</option>
            {documentTypes.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>

          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Toutes les catégories</option>
            {documentCategories.map(category => (
              <option key={category.value} value={category.value}>{category.label}</option>
            ))}
          </select>

          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
            </button>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </button>
          </div>

          <button
            onClick={loadDocuments}
            className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Documents Grid/List */}
      <div className="bg-white rounded-xl shadow-lg">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6' : 'divide-y divide-gray-200'}>
            {sortedDocuments.map((document) => (
              <div key={document.id} className={viewMode === 'grid' ? 'bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow' : 'flex items-center justify-between p-4 hover:bg-gray-50'}>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {getFileIcon(document.type, document.mimeType)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {document.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(document.size)}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getCategoryColor(document.category)}`}>
                        {getCategoryLabel(document.category)}
                      </span>
                      {document.isFavorite && (
                        <Star className="h-3 w-3 text-yellow-500" />
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleDocumentDownload(document)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => toggleDocumentFavorite(document.id)}
                    className={`${document.isFavorite ? 'text-yellow-500' : 'text-gray-400'} hover:text-yellow-600`}
                  >
                    <Star className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDocumentDelete(document.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}

            {sortedDocuments.length === 0 && (
              <div className="col-span-full text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Aucun document trouvé</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export { DocumentsManagement as default };
