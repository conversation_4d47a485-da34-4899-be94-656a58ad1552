import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  FileText,
  Target,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Award,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface AnalyticsData {
  // KPIs principaux
  totalRevenue: number;
  totalClients: number;
  totalLeads: number;
  totalContracts: number;
  conversionRate: number;
  averageOrderValue: number;

  // Données temporelles
  monthlyRevenue: Array<{month: string, revenue: number, leads: number, clients: number}>;
  weeklyActivity: Array<{day: string, leads: number, calls: number, emails: number}>;

  // Répartitions
  revenueBySource: Array<{source: string, value: number, color: string}>;
  clientsBySegment: Array<{segment: string, count: number, color: string}>;
  contractsByType: Array<{type: string, count: number, value: number}>;

  // Performance équipe
  teamPerformance: Array<{name: string, leads: number, conversions: number, revenue: number}>;

  // Tendances
  trends: {
    revenue: number;
    clients: number;
    leads: number;
    conversion: number;
  };
}

export const Analytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{show: boolean, message: string, type: 'success' | 'error'}>({
    show: false, message: '', type: 'success'
  });

  // Données de démonstration
  const demoAnalyticsData: AnalyticsData = {
    totalRevenue: 2450000,
    totalClients: 156,
    totalLeads: 89,
    totalContracts: 34,
    conversionRate: 38.2,
    averageOrderValue: 15705,

    monthlyRevenue: [
      { month: 'Jan', revenue: 180000, leads: 25, clients: 12 },
      { month: 'Fév', revenue: 220000, leads: 32, clients: 18 },
      { month: 'Mar', revenue: 195000, leads: 28, clients: 15 },
      { month: 'Avr', revenue: 240000, leads: 35, clients: 22 },
      { month: 'Mai', revenue: 280000, leads: 42, clients: 28 },
      { month: 'Jun', revenue: 320000, leads: 48, clients: 35 },
      { month: 'Jul', revenue: 290000, leads: 38, clients: 30 },
      { month: 'Aoû', revenue: 310000, leads: 45, clients: 32 },
      { month: 'Sep', revenue: 275000, leads: 40, clients: 28 },
      { month: 'Oct', revenue: 295000, leads: 43, clients: 31 },
      { month: 'Nov', revenue: 315000, leads: 47, clients: 34 },
      { month: 'Déc', revenue: 330000, leads: 52, clients: 38 }
    ],

    weeklyActivity: [
      { day: 'Lun', leads: 12, calls: 25, emails: 45 },
      { day: 'Mar', leads: 15, calls: 30, emails: 52 },
      { day: 'Mer', leads: 18, calls: 35, emails: 48 },
      { day: 'Jeu', leads: 14, calls: 28, emails: 41 },
      { day: 'Ven', leads: 16, calls: 32, emails: 38 },
      { day: 'Sam', leads: 8, calls: 15, emails: 22 },
      { day: 'Dim', leads: 6, calls: 12, emails: 18 }
    ],

    revenueBySource: [
      { source: 'Site Web', value: 35, color: '#3B82F6' },
      { source: 'Références', value: 28, color: '#10B981' },
      { source: 'LinkedIn', value: 20, color: '#8B5CF6' },
      { source: 'Email', value: 12, color: '#F59E0B' },
      { source: 'Téléphone', value: 5, color: '#EF4444' }
    ],

    clientsBySegment: [
      { segment: 'Premium', count: 45, color: '#8B5CF6' },
      { segment: 'Standard', count: 78, color: '#3B82F6' },
      { segment: 'Basic', count: 33, color: '#10B981' }
    ],

    contractsByType: [
      { type: 'CRM Standard', count: 12, value: 480000 },
      { type: 'CRM Premium', count: 8, value: 720000 },
      { type: 'Maintenance', count: 14, value: 350000 },
      { type: 'Formation', count: 6, value: 180000 },
      { type: 'Consulting', count: 4, value: 240000 }
    ],

    teamPerformance: [
      { name: 'Mohammed MOUMEN', leads: 25, conversions: 12, revenue: 480000 },
      { name: 'Sarah ALAMI', leads: 22, conversions: 10, revenue: 420000 },
      { name: 'Karim BENJELLOUN', leads: 18, conversions: 8, revenue: 350000 },
      { name: 'Laila AMRANI', leads: 15, conversions: 6, revenue: 280000 },
      { name: 'Omar FASSI', leads: 12, conversions: 5, revenue: 220000 }
    ],

    trends: {
      revenue: 12.5,
      clients: 8.3,
      leads: -2.1,
      conversion: 5.7
    }
  };

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setAnalyticsData(demoAnalyticsData);
      setLoading(false);
    }, 1000);
  }, []);

  const handleRefreshData = async () => {
    setLoading(true);
    try {
      // Simuler un appel API
      setTimeout(() => {
        setAnalyticsData(demoAnalyticsData);
        setLoading(false);
        setToast({show: true, message: 'Données actualisées avec succès', type: 'success'});
      }, 1000);
    } catch (error) {
      setLoading(false);
      setToast({show: true, message: 'Erreur lors de l\'actualisation', type: 'error'});
    }
  };

  const handleExportData = () => {
    // Simuler l'export des données
    setToast({show: true, message: 'Export des données en cours...', type: 'success'});
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics CRM</h1>
            <p className="text-gray-600">Tableau de bord analytique et métriques de performance</p>
          </div>
          <div className="flex space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
              <option value="quarter">Ce trimestre</option>
              <option value="year">Cette année</option>
            </select>
            <button
              onClick={handleRefreshData}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Actualiser
            </button>
            <button
              onClick={handleExportData}
              className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Exporter
            </button>
          </div>
        </div>

        {/* KPIs principaux */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analyticsData.totalRevenue)}
                </p>
                <div className="flex items-center mt-2">
                  {analyticsData.trends.revenue > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analyticsData.trends.revenue > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(analyticsData.trends.revenue)}
                  </span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Clients</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.totalClients}</p>
                <div className="flex items-center mt-2">
                  {analyticsData.trends.clients > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analyticsData.trends.clients > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(analyticsData.trends.clients)}
                  </span>
                </div>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Prospects</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.totalLeads}</p>
                <div className="flex items-center mt-2">
                  {analyticsData.trends.leads > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analyticsData.trends.leads > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(analyticsData.trends.leads)}
                  </span>
                </div>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taux de Conversion</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.conversionRate}%</p>
                <div className="flex items-center mt-2">
                  {analyticsData.trends.conversion > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analyticsData.trends.conversion > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(analyticsData.trends.conversion)}
                  </span>
                </div>
              </div>
              <Award className="h-8 w-8 text-yellow-600" />
            </div>
          </motion.div>
        </div>

        {/* Graphiques principaux */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Évolution du chiffre d'affaires */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Évolution Mensuelle</h3>
              <BarChart3 className="h-5 w-5 text-gray-400" />
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analyticsData.monthlyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'revenue' ? formatCurrency(value as number) : value,
                    name === 'revenue' ? 'Chiffre d\'affaires' :
                    name === 'leads' ? 'Prospects' : 'Clients'
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.1}
                />
                <Area
                  type="monotone"
                  dataKey="leads"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          </motion.div>

          {/* Répartition par source */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white p-6 rounded-lg shadow-sm border"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Sources de Revenus</h3>
              <PieChartIcon className="h-5 w-5 text-gray-400" />
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analyticsData.revenueBySource}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({source, value}) => `${source}: ${value}%`}
                >
                  {analyticsData.revenueBySource.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </motion.div>
        </div>

        {/* Activité hebdomadaire */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Activité Hebdomadaire</h3>
            <Activity className="h-5 w-5 text-gray-400" />
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.weeklyActivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="leads" fill="#3B82F6" name="Prospects" />
              <Bar dataKey="calls" fill="#10B981" name="Appels" />
              <Bar dataKey="emails" fill="#8B5CF6" name="Emails" />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Performance de l'équipe */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Performance de l'Équipe</h3>
            <Users className="h-5 w-5 text-gray-400" />
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Commercial
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prospects
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conversions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Taux
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Chiffre d'Affaires
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {analyticsData.teamPerformance.map((member, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-axa-blue flex items-center justify-center">
                            <span className="text-xs font-medium text-white">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{member.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {member.leads}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {member.conversions}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-axa-blue h-2 rounded-full"
                            style={{ width: `${(member.conversions / member.leads) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-900">
                          {Math.round((member.conversions / member.leads) * 100)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(member.revenue)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Répartition des clients par segment */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Clients par Segment</h3>
            <Target className="h-5 w-5 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {analyticsData.clientsBySegment.map((segment, index) => (
              <div key={index} className="text-center p-4 border rounded-lg">
                <div
                  className="w-16 h-16 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold text-lg"
                  style={{ backgroundColor: segment.color }}
                >
                  {segment.count}
                </div>
                <h4 className="font-medium text-gray-900">{segment.segment}</h4>
                <p className="text-sm text-gray-500">
                  {Math.round((segment.count / analyticsData.totalClients) * 100)}% du total
                </p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Toast */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({...toast, show: false})}
        />
      )}
    </div>
  );
};

export default Analytics;
