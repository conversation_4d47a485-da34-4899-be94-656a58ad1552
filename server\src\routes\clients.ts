import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createClientSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^(\+212|0)[5-7]\d{8}$/).required().messages({
    'string.pattern.base': 'Numéro de téléphone marocain invalide',
  }),
  type: Joi.string().valid('INDIVIDUAL', 'BUSINESS').required(),
  city: Joi.string().max(50).required(),
  address: Joi.string().max(200),
  dateOfBirth: Joi.date(),
  profession: Joi.string().max(100),
});

const updateClientSchema = Joi.object({
  name: Joi.string().min(2).max(100),
  email: Joi.string().email(),
  phone: Joi.string().pattern(/^(\+212|0)[5-7]\d{8}$/),
  type: Joi.string().valid('INDIVIDUAL', 'BUSINESS'),
  city: Joi.string().max(50),
  address: Joi.string().max(200),
  dateOfBirth: Joi.date(),
  profession: Joi.string().max(100),
  assignedTo: Joi.string().uuid().allow(null),
  isActive: Joi.boolean(),
});

/**
 * GET /api/clients
 * Récupérer tous les clients
 */
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const type = req.query.type as string;
  const search = req.query.search as string;

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  if (type && type !== 'all') where.type = type;
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search } },
      { city: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Si l'utilisateur n'est pas admin, ne voir que ses clients assignés
  if (req.user?.role === 'USER') {
    where.assignedTo = req.user.id;
  }

  const [clients, total] = await Promise.all([
    prisma.client.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        assignedUser: {
          select: { id: true, name: true, email: true },
        },
        quotes: {
          select: { id: true, product: true, amount: true, status: true },
        },
        contracts: {
          select: { id: true, product: true, premium: true, status: true },
        },
        claims: {
          select: { id: true, type: true, status: true },
        },
      },
    }),
    prisma.client.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/clients/:id
 * Récupérer un client spécifique
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const client = await prisma.client.findUnique({
    where: { id: req.params.id },
    include: {
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
      quotes: {
        include: {
          createdUser: {
            select: { id: true, name: true },
          },
        },
      },
      contracts: true,
      claims: {
        include: {
          assignedUser: {
            select: { id: true, name: true },
          },
        },
      },
    },
  });

  if (!client) {
    throw createError('Client non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && client.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  res.json({
    success: true,
    data: { client },
  });
}));

/**
 * POST /api/clients
 * Créer un nouveau client
 */
router.post('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createClientSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que l'email est unique
  const existingClient = await prisma.client.findUnique({
    where: { email: value.email.toLowerCase() },
  });

  if (existingClient) {
    throw createError('Un client avec cet email existe déjà', 409);
  }

  // Créer le client
  const client = await prisma.client.create({
    data: {
      ...value,
      email: value.email.toLowerCase(),
      assignedTo: req.user?.id,
    },
    include: {
      assignedUser: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  logger.info(`Client created: ${client.email} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Client créé avec succès',
    data: { client },
  });
}));

/**
 * PUT /api/clients/:id
 * Mettre à jour un client
 */
router.put('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updateClientSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le client existe
  const existingClient = await prisma.client.findUnique({
    where: { id: req.params.id },
  });

  if (!existingClient) {
    throw createError('Client non trouvé', 404);
  }

  // Vérifier les permissions
  if (req.user?.role === 'USER' && existingClient.assignedTo !== req.user.id) {
    throw createError('Accès non autorisé', 403);
  }

  // Vérifier l'unicité de l'email si modifié
  if (value.email && value.email !== existingClient.email) {
    const emailExists = await prisma.client.findUnique({
      where: { email: value.email.toLowerCase() },
    });

    if (emailExists) {
      throw createError('Un client avec cet email existe déjà', 409);
    }
  }

  // Mettre à jour le client
  // Mettre à jour le client dans MySQL
  const updateFields = [];
  const updateValues = [];
  Object.entries(value).forEach(([key, val]) => {
    if (val !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(key === 'email' ? val.toLowerCase() : val);
    }
  });
  updateValues.push(req.params.id);
  const updateQuery = `UPDATE client SET ${updateFields.join(', ')} WHERE id = ?`;
  await pool.query(updateQuery, updateValues);

  // Récupérer le client mis à jour avec l'utilisateur assigné
  const [rows] = await pool.query(`SELECT c.*, u.id as assignedUserId, u.name as assignedUserName, u.email as assignedUserEmail FROM client c LEFT JOIN user u ON c.assignedTo = u.id WHERE c.id = ?`, [req.params.id]);
  const client = rows[0];

  logger.info(`Client updated: ${client.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Client mis à jour avec succès',
    data: { client },
  });
}));

/**
 * DELETE /api/clients/:id
 * Supprimer un client (admin seulement)
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Récupérer le client
  const [clientRows] = await pool.query('SELECT * FROM client WHERE id = ?', [req.params.id]);
  const client = clientRows[0];

  if (!client) {
    throw createError('Client non trouvé', 404);
  }

  // Vérifier s'il y a des contrats actifs
  const [contractRows] = await pool.query('SELECT COUNT(*) as count FROM contract WHERE clientId = ? AND status = ?', [req.params.id, 'ACTIVE']);
  const activeContracts = contractRows[0].count;

  if (activeContracts > 0) {
    throw createError('Impossible de supprimer un client avec des contrats actifs', 400);
  }

  await pool.query('DELETE FROM client WHERE id = ?', [req.params.id]);

  logger.info(`Client deleted: ${client.id} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Client supprimé avec succès',
  });
}));

/**
 * POST /api/clients/:id/assign
 * Assigner un client à un utilisateur
 */
router.post('/:id/assign', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { userId } = req.body;

  if (userId) {
    // Vérifier que l'utilisateur existe
    const [userRows] = await pool.query('SELECT * FROM user WHERE id = ?', [userId]);
    const user = userRows[0];
    if (!user) {
      throw createError('Utilisateur non trouvé', 404);
    }
  }

  await pool.query('UPDATE client SET assignedTo = ? WHERE id = ?', [userId || null, req.params.id]);

  // Récupérer le client mis à jour avec l'utilisateur assigné
  const [rows] = await pool.query(`SELECT c.*, u.id as assignedUserId, u.name as assignedUserName, u.email as assignedUserEmail FROM client c LEFT JOIN user u ON c.assignedTo = u.id WHERE c.id = ?`, [req.params.id]);
  const client = rows[0];

  logger.info(`Client ${client.id} assigned to ${userId || 'unassigned'} by ${req.user?.email}`);

  res.json({
    success: true,
    message: userId ? 'Client assigné avec succès' : 'Client désassigné avec succès',
    data: { client },
  });
}));

export default router;
