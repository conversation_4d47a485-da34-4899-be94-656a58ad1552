/**
 * Données mock pour les tests sans backend
 */

// Données mock pour les leads
export const mockLeads = [
  {
    id: '1',
    name: '<PERSON>',
    email: 'ahmed.ben<PERSON>@email.com',
    phone: '+212 6 12 34 56 78',
    product: 'Assurance Auto',
    source: 'Site Web',
    city: 'Casablanca',
    message: 'Intéressé par une assurance auto complète',
    status: 'NEW',
    assignedTo: null,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+212 6 87 65 43 21',
    product: 'Assurance Habitation',
    source: 'Téléphone',
    city: 'Ra<PERSON>',
    message: 'Besoin d\'une assurance pour mon appartement',
    status: 'CONTACTED',
    assignedTo: '2',
    assignedUser: {
      id: '2',
      name: 'Conseiller 1',
      email: '<EMAIL>'
    },
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-15T09:15:00Z'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+212 6 55 44 33 22',
    product: 'Assurance Santé',
    source: 'Recommandation',
    city: 'Marrakech',
    message: 'Recherche une assurance santé familiale',
    status: 'QUALIFIED',
    assignedTo: '3',
    assignedUser: {
      id: '3',
      name: 'Conseiller 2',
      email: '<EMAIL>'
    },
    createdAt: '2024-01-13T16:45:00Z',
    updatedAt: '2024-01-15T11:30:00Z'
  }
];
// Mock pour Dashboard Assurance
export const mockInsuranceDashboard = {
  totalPolicies: 1247,
  activePolicies: 1156,
  totalPremiums: 2850000,
  monthlyPremiums: 285000,
  totalClaims: 89,
  openClaims: 23,
  claimsRatio: 0.65,
  averageClaimAmount: 15500,
  newPoliciesThisMonth: 34,
  renewalsThisMonth: 156,
  cancellationsThisMonth: 12,
  conversionRate: 0.78,
  grossPremiums: 2850000,
  netPremiums: 2565000,
  commissionsEarned: 285000,
  profitMargin: 0.22,
  premiumsByProduct: [
    { product: 'Auto', amount: 1425000, count: 623 },
    { product: 'Habitation', amount: 570000, count: 289 },
    { product: 'Santé', amount: 456000, count: 178 },
    { product: 'Prévoyance', amount: 285000, count: 134 },
    { product: 'Épargne', amount: 114000, count: 23 }
  ],
  claimsByMonth: [
    { month: 'Jan', claims: 15, amount: 185000 },
    { month: 'Fév', claims: 12, amount: 156000 },
    { month: 'Mar', claims: 18, amount: 234000 },
    { month: 'Avr', claims: 14, amount: 178000 },
    { month: 'Mai', claims: 16, amount: 198000 },
    { month: 'Juin', claims: 14, amount: 167000 }
  ],
  policyDistribution: [
    { type: 'Particuliers', count: 934, percentage: 75 },
    { type: 'Professionnels', count: 222, percentage: 18 },
    { type: 'Entreprises', count: 91, percentage: 7 }
  ],
  recentActivity: [
    { id: '1', type: 'claim', message: 'Nouveau sinistre auto - Ahmed Benali', timestamp: '2024-01-15T10:30:00Z', priority: 'high' },
    { id: '2', type: 'policy', message: 'Police renouvelée - Fatima Zahra', timestamp: '2024-01-14T14:20:00Z', priority: 'medium' }
  ],
  regulatoryAlerts: [
    { id: '1', type: 'acaps', message: 'Déclaration ACAPS à soumettre', severity: 'warning', dueDate: '2024-08-15' },
    { id: '2', type: 'compliance', message: 'Nouvelle réglementation entrée en vigueur', severity: 'info' }
  ]
};

// Mock pour Analytics Assurance
export const mockInsuranceAnalytics = {
  totalPremiums: 2850000,
  totalClaims: 89,
  claimsRatio: 0.65,
  monthlyAnalytics: [
    { month: 'Jan', premiums: 450000, claims: 15 },
    { month: 'Fév', premiums: 420000, claims: 12 },
    { month: 'Mar', premiums: 480000, claims: 18 },
    { month: 'Avr', premiums: 430000, claims: 14 },
    { month: 'Mai', premiums: 470000, claims: 16 },
    { month: 'Juin', premiums: 410000, claims: 14 }
  ],
  productBreakdown: [
    { product: 'Auto', premiums: 1425000, claims: 45 },
    { product: 'Habitation', premiums: 570000, claims: 18 },
    { product: 'Santé', premiums: 456000, claims: 16 },
    { product: 'Prévoyance', premiums: 285000, claims: 7 },
    { product: 'Épargne', premiums: 114000, claims: 3 }
  ]
};

// Mock pour Conformité ACAPS
export const mockComplianceACAPS = {
  reports: [
    { id: 'R1', title: 'Rapport ACAPS Janvier', status: 'validé', date: '2024-01-31' },
    { id: 'R2', title: 'Rapport ACAPS Février', status: 'en attente', date: '2024-02-28' }
  ],
  metrics: [
    { id: 'M1', name: 'Ratio Solvabilité', value: 0.92 },
    { id: 'M2', name: 'Ratio Sinistres', value: 0.65 }
  ],
  alerts: [
    { id: 'A1', type: 'acaps', message: 'Déclaration ACAPS à soumettre', severity: 'warning', dueDate: '2024-08-15' },
    { id: 'A2', type: 'compliance', message: 'Nouvelle réglementation entrée en vigueur', severity: 'info' }
  ]
};

// Données mock pour les clients
export const mockClients = [
  {
    id: '1',
    name: 'Hassan Benjelloun',
    email: '<EMAIL>',
    phone: '+212 6 11 22 33 44',
    type: 'INDIVIDUAL',
    city: 'Casablanca',
    address: '123 Rue Mohammed V, Casablanca',
    profession: 'Ingénieur',
    assignedTo: '2',
    assignedUser: {
      id: '2',
      name: 'Conseiller 1',
      email: '<EMAIL>'
    },
    isActive: true,
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
    quotes: [],
    contracts: [
      {
        id: '1',
        product: 'Assurance Auto',
        premium: 2500,
        status: 'ACTIVE',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z'
      }
    ],
    claims: []
  },
  {
    id: '2',
    name: 'Entreprise ABC SARL',
    email: '<EMAIL>',
    phone: '+212 5 22 33 44 55',
    type: 'BUSINESS',
    city: 'Rabat',
    address: 'Zone Industrielle, Rabat',
    assignedTo: '3',
    assignedUser: {
      id: '3',
      name: 'Conseiller 2',
      email: '<EMAIL>'
    },
    isActive: true,
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
    quotes: [],
    contracts: [
      {
        id: '2',
        product: 'Assurance Responsabilité Civile',
        premium: 15000,
        status: 'ACTIVE',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z'
      }
    ],
    claims: []
  }
];

// Données mock pour les devis
export const mockQuotes = [
  {
    id: '1',
    clientId: '1',
    client: {
      id: '1',
      name: 'Hassan Benjelloun',
      email: '<EMAIL>',
      phone: '+212 6 11 22 33 44'
    },
    product: 'Assurance Auto Premium',
    amount: 3200,
    status: 'SENT',
    validUntil: '2024-02-15T23:59:59Z',
    details: {
      vehicleType: 'Berline',
      coverage: 'Tous risques'
    },
    createdBy: '2',
    createdUser: {
      id: '2',
      name: 'Conseiller 1',
      email: '<EMAIL>'
    },
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z'
  },
  {
    id: '2',
    clientId: '2',
    client: {
      id: '2',
      name: 'Entreprise ABC SARL',
      email: '<EMAIL>',
      phone: '+212 5 22 33 44 55'
    },
    product: 'Assurance Flotte Véhicules',
    amount: 25000,
    status: 'DRAFT',
    validUntil: '2024-02-20T23:59:59Z',
    details: {
      numberOfVehicles: 10,
      coverage: 'Tous risques + Vol'
    },
    createdBy: '3',
    createdUser: {
      id: '3',
      name: 'Conseiller 2',
      email: '<EMAIL>'
    },
    createdAt: '2024-01-14T15:30:00Z',
    updatedAt: '2024-01-15T09:00:00Z'
  }
];

// Données mock pour les bandeaux
export const mockBanners = [
  {
    id: '1',
    name: 'Promotion Assurance Auto',
    text: '🚗 Profitez de -20% sur votre assurance auto ! Offre limitée jusqu\'au 31 janvier.',
    type: 'PROMOTION',
    status: 'ACTIVE',
    backgroundColor: '#10B981',
    textColor: '#FFFFFF',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-01-31T23:59:59Z',
    showContact: true,
    views: 1250,
    clicks: 89,
    createdAt: '2024-01-01T08:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z'
  },
  {
    id: '2',
    name: 'Information Maintenance',
    text: '⚠️ Maintenance programmée le dimanche 21 janvier de 2h à 6h. Services temporairement indisponibles.',
    type: 'INFO',
    status: 'SCHEDULED',
    backgroundColor: '#3B82F6',
    textColor: '#FFFFFF',
    startDate: '2024-01-21T02:00:00Z',
    endDate: '2024-01-21T06:00:00Z',
    showContact: false,
    views: 0,
    clicks: 0,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

// Données mock pour les articles de blog
export const mockBlogPosts = [
  {
    id: '1',
    title: 'Guide complet de l\'assurance auto au Maroc',
    slug: 'guide-assurance-auto-maroc',
    content: '<p>L\'assurance automobile est obligatoire au Maroc...</p>',
    excerpt: 'Tout ce que vous devez savoir sur l\'assurance auto au Maroc : obligations légales, types de couverture, et conseils pour choisir.',
    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
    status: 'PUBLISHED',
    publishedAt: '2024-01-10T10:00:00Z',
    tags: ['assurance auto', 'guide', 'maroc'],
    category: 'Guides',
    seo: {
      title: 'Guide Assurance Auto Maroc - MOUMEN TECHNIQUE ET PREVOYANCE',
      description: 'Guide complet sur l\'assurance automobile au Maroc. Obligations, couvertures, conseils d\'experts.',
      keywords: 'assurance auto maroc, assurance automobile, guide assurance'
    },
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z'
  },
  {
    id: '2',
    title: 'Les avantages de l\'assurance habitation',
    slug: 'avantages-assurance-habitation',
    content: '<p>Protéger son logement est essentiel...</p>',
    excerpt: 'Découvrez pourquoi souscrire une assurance habitation est crucial pour protéger votre patrimoine.',
    image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800',
    status: 'PUBLISHED',
    publishedAt: '2024-01-08T14:00:00Z',
    tags: ['assurance habitation', 'protection', 'patrimoine'],
    category: 'Conseils',
    seo: {
      title: 'Assurance Habitation : Avantages et Protection',
      description: 'Les avantages de l\'assurance habitation pour protéger votre logement et vos biens.',
      keywords: 'assurance habitation, protection logement, assurance maison'
    },
    createdAt: '2024-01-08T13:00:00Z',
    updatedAt: '2024-01-08T14:00:00Z'
  }
];

// Données mock pour les pages de contenu
export const mockContentPages = [
  {
    id: '1',
    title: 'À propos de nous',
    slug: 'a-propos',
    content: '<h1>MOUMEN TECHNIQUE ET PREVOYANCE</h1><p>Votre partenaire de confiance en assurance...</p>',
    excerpt: 'Découvrez l\'histoire et les valeurs de MOUMEN TECHNIQUE ET PREVOYANCE.',
    status: 'PUBLISHED',
    publishedAt: '2024-01-01T00:00:00Z',
    seo: {
      title: 'À propos - MOUMEN TECHNIQUE ET PREVOYANCE',
      description: 'Découvrez l\'histoire, les valeurs et l\'équipe de MOUMEN TECHNIQUE ET PREVOYANCE.',
      keywords: 'moumen technique prevoyance, à propos, histoire, valeurs'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Nos services',
    slug: 'nos-services',
    content: '<h1>Nos Services d\'Assurance</h1><p>Nous proposons une gamme complète de services...</p>',
    excerpt: 'Découvrez notre gamme complète de services d\'assurance adaptés à vos besoins.',
    status: 'PUBLISHED',
    publishedAt: '2024-01-01T00:00:00Z',
    seo: {
      title: 'Nos Services - MOUMEN TECHNIQUE ET PREVOYANCE',
      description: 'Services d\'assurance auto, habitation, santé, prévoyance et épargne retraite.',
      keywords: 'services assurance, assurance auto, assurance habitation, assurance santé'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T15:00:00Z'
  }
];

// Données mock pour les fichiers média
export const mockMediaFiles = [
  {
    id: '1',
    name: 'hero-image.jpg',
    originalName: 'hero-image.jpg',
    url: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200',
    type: 'image',
    size: 245760,
    mimeType: 'image/jpeg',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'assurance-auto.jpg',
    originalName: 'assurance-auto.jpg',
    url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
    type: 'image',
    size: 189440,
    mimeType: 'image/jpeg',
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z'
  },
  {
    id: '3',
    name: 'assurance-habitation.jpg',
    originalName: 'assurance-habitation.jpg',
    url: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800',
    type: 'image',
    size: 167890,
    mimeType: 'image/jpeg',
    createdAt: '2024-01-08T12:00:00Z',
    updatedAt: '2024-01-08T12:00:00Z'
  }
];

// Données mock pour les sinistres
export const mockClaims = [
  {
    id: '1',
    claimNumber: 'SIN-2024-001',
    clientId: '1',
    client: {
      id: '1',
      name: 'Hassan Benjelloun',
      email: '<EMAIL>',
      phone: '+212 6 11 22 33 44',
      type: 'INDIVIDUAL'
    },
    contractId: '1',
    contract: {
      id: '1',
      product: 'Assurance Auto',
      premium: 2500,
      policyNumber: 'POL-AUTO-001'
    },
    type: 'AUTO',
    category: 'Accident de la circulation',
    title: 'Collision avec un autre véhicule',
    description: 'Accident survenu au carrefour de la rue Mohammed V et Avenue Hassan II. Collision latérale avec dégâts importants sur l\'aile droite.',
    incidentDate: '2024-01-10T15:30:00Z',
    reportedDate: '2024-01-10T16:45:00Z',
    location: 'Carrefour Mohammed V / Hassan II, Casablanca',
    estimatedAmount: 15000,
    approvedAmount: 12500,
    status: 'APPROVED',
    priority: 'HIGH',
    assignedTo: '2',
    assignedUser: {
      id: '2',
      name: 'Conseiller 1',
      email: '<EMAIL>',
      role: 'AGENT'
    },
    documents: [],
    timeline: [],
    notes: [],
    createdAt: '2024-01-10T16:45:00Z',
    updatedAt: '2024-01-14T10:30:00Z'
  },
  {
    id: '2',
    claimNumber: 'SIN-2024-002',
    clientId: '2',
    client: {
      id: '2',
      name: 'Entreprise ABC SARL',
      email: '<EMAIL>',
      phone: '+212 5 22 33 44 55',
      type: 'BUSINESS'
    },
    contractId: '2',
    contract: {
      id: '2',
      product: 'Assurance Responsabilité Civile',
      premium: 15000,
      policyNumber: 'POL-RC-002'
    },
    type: 'BUSINESS',
    category: 'Dégât des eaux',
    title: 'Inondation des locaux suite à rupture de canalisation',
    description: 'Rupture de canalisation principale causant l\'inondation des bureaux au rez-de-chaussée. Dégâts sur le matériel informatique et mobilier.',
    incidentDate: '2024-01-12T08:00:00Z',
    reportedDate: '2024-01-12T09:15:00Z',
    location: 'Zone Industrielle, Rabat',
    estimatedAmount: 45000,
    status: 'INVESTIGATING',
    priority: 'URGENT',
    assignedTo: '3',
    assignedUser: {
      id: '3',
      name: 'Conseiller 2',
      email: '<EMAIL>',
      role: 'AGENT'
    },
    documents: [],
    timeline: [],
    notes: [],
    createdAt: '2024-01-12T09:15:00Z',
    updatedAt: '2024-01-15T14:20:00Z'
  },
  {
    id: '3',
    claimNumber: 'SIN-2024-003',
    clientId: '1',
    client: {
      id: '1',
      name: 'Hassan Benjelloun',
      email: '<EMAIL>',
      phone: '+212 6 11 22 33 44',
      type: 'INDIVIDUAL'
    },
    type: 'HOME',
    category: 'Vol avec effraction',
    title: 'Cambriolage du domicile',
    description: 'Cambriolage avec effraction de la porte d\'entrée. Vol d\'appareils électroniques et bijoux.',
    incidentDate: '2024-01-08T02:30:00Z',
    reportedDate: '2024-01-08T07:00:00Z',
    location: 'Résidence Al Manar, Casablanca',
    estimatedAmount: 25000,
    status: 'UNDER_REVIEW',
    priority: 'MEDIUM',
    assignedTo: '2',
    assignedUser: {
      id: '2',
      name: 'Conseiller 1',
      email: '<EMAIL>',
      role: 'AGENT'
    },
    documents: [],
    timeline: [],
    notes: [],
    createdAt: '2024-01-08T07:00:00Z',
    updatedAt: '2024-01-15T11:45:00Z'
  }
];

// Statistiques mock pour le dashboard
export const mockDashboardStats = {
  totalLeads: mockLeads.length,
  totalClients: mockClients.length,
  totalQuotes: mockQuotes.length,
  totalContracts: mockClients.reduce((acc, client) => acc + (client.contracts?.length || 0), 0),
  totalRevenue: 125000,
  leadsThisMonth: 15,
  clientsThisMonth: 8,
  quotesThisMonth: 12,
  contractsThisMonth: 6,
  revenueThisMonth: 45000,
  leadsGrowth: 25.5,
  clientsGrowth: 18.2,
  quotesGrowth: 33.1,
  contractsGrowth: 12.8,
  leadsByStatus: [
    { status: 'NEW', count: 8 },
    { status: 'CONTACTED', count: 4 },
    { status: 'QUALIFIED', count: 2 },
    { status: 'CONVERTED', count: 1 }
  ],
  recentActivity: [
    {
      id: '1',
      type: 'lead',
      message: 'Nouveau lead: Ahmed Benali (Assurance Auto)',
      timestamp: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      type: 'quote',
      message: 'Devis envoyé à Hassan Benjelloun',
      timestamp: '2024-01-15T09:15:00Z'
    },
    {
      id: '3',
      type: 'client',
      message: 'Nouveau client: Entreprise ABC SARL',
      timestamp: '2024-01-14T16:20:00Z'
    }
  ]
};
