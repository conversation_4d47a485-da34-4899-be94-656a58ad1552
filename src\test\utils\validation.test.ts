import { describe, it, expect } from 'vitest';
import { 
  validateEmail, 
  validatePhone, 
  validateName, 
  validatePassword,
  validateLead,
  validateClient,
  validateQuote,
  validateClaim,
  validateUser,
  validateBanner
} from '../../utils/validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('validates correct Moroccan phone numbers', () => {
      expect(validatePhone('+212612345678')).toBe(true);
      expect(validatePhone('0612345678')).toBe(true);
      expect(validatePhone('0712345678')).toBe(true);
      expect(validatePhone('06 12 34 56 78')).toBe(true);
    });

    it('rejects invalid phone numbers', () => {
      expect(validatePhone('123456789')).toBe(false);
      expect(validatePhone('+33612345678')).toBe(false);
      expect(validatePhone('0812345678')).toBe(false);
    });
  });

  describe('validateName', () => {
    it('validates correct names', () => {
      expect(validateName('John Doe')).toBe(true);
      expect(validateName('Ahmed')).toBe(true);
      expect(validateName('Jean-Pierre')).toBe(true);
    });

    it('rejects invalid names', () => {
      expect(validateName('')).toBe(false);
      expect(validateName('A')).toBe(false);
      expect(validateName('A'.repeat(51))).toBe(false);
      expect(validateName('   ')).toBe(false);
    });
  });

  describe('validatePassword', () => {
    it('validates strong passwords', () => {
      const result = validatePassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects weak passwords', () => {
      const result = validatePassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('checks for minimum length', () => {
      const result = validatePassword('Short1!');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.message.includes('8 caractères'))).toBe(true);
    });

    it('checks for uppercase letters', () => {
      const result = validatePassword('lowercase123!');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.message.includes('majuscule'))).toBe(true);
    });

    it('checks for lowercase letters', () => {
      const result = validatePassword('UPPERCASE123!');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.message.includes('minuscule'))).toBe(true);
    });

    it('checks for numbers', () => {
      const result = validatePassword('NoNumbers!');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.message.includes('chiffre'))).toBe(true);
    });

    it('checks for special characters', () => {
      const result = validatePassword('NoSpecial123');
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.message.includes('caractère spécial'))).toBe(true);
    });
  });

  describe('validateLead', () => {
    const validLead = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '0612345678',
      product: 'Assurance Auto',
      source: 'Facebook',
      city: 'Casablanca'
    };

    it('validates correct lead data', () => {
      const result = validateLead(validLead);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects lead with invalid email', () => {
      const result = validateLead({ ...validLead, email: 'invalid-email' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'email')).toBe(true);
    });

    it('rejects lead with missing required fields', () => {
      const result = validateLead({ ...validLead, name: '' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'name')).toBe(true);
    });
  });

  describe('validateClient', () => {
    const validClient = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '0612345678',
      type: 'Particulier',
      city: 'Casablanca'
    };

    it('validates correct client data', () => {
      const result = validateClient(validClient);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects client with invalid type', () => {
      const result = validateClient({ ...validClient, type: 'InvalidType' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'type')).toBe(true);
    });
  });

  describe('validateQuote', () => {
    const validQuote = {
      clientName: 'John Doe',
      product: 'Assurance Auto',
      amount: '1500',
      status: 'Brouillon'
    };

    it('validates correct quote data', () => {
      const result = validateQuote(validQuote);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects quote with invalid amount', () => {
      const result = validateQuote({ ...validQuote, amount: 'invalid' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'amount')).toBe(true);
    });
  });

  describe('validateBanner', () => {
    const validBanner = {
      name: 'Test Banner',
      text: 'Test banner text',
      type: 'info',
      status: 'active',
      backgroundColor: 'bg-blue-500',
      textColor: 'text-white'
    };

    it('validates correct banner data', () => {
      const result = validateBanner(validBanner);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('rejects banner with invalid type', () => {
      const result = validateBanner({ ...validBanner, type: 'invalid' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'type')).toBe(true);
    });

    it('rejects banner with missing required fields', () => {
      const result = validateBanner({ ...validBanner, name: '' });
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.field === 'name')).toBe(true);
    });
  });
});
