import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Phone, Mail, MapPin, Facebook, Instagram, Linkedin } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-axa-dark text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <img 
              src="/logo-mtp-1.png" 
              alt="MOUMEN TECHNIQUE ET PREVOYANCE" 
              className="h-16 w-auto"
            />
            <p className="text-gray-300 text-sm leading-relaxed">
              Votre agent général AXA au Maroc. Nous vous accompagnons dans tous vos projets d'assurance avec expertise et proximité.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Liens Rapides</h3>
            <ul className="space-y-2">
              <li><Link to="/" className="text-gray-300 hover:text-white transition-colors">Accueil</Link></li>
              <li><Link to="/produits" className="text-gray-300 hover:text-white transition-colors">Nos Produits</Link></li>
              <li><Link to="/devis" className="text-gray-300 hover:text-white transition-colors">Demande de Devis</Link></li>
// ...existing code...
              <li><Link to="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Products */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Nos Assurances</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Assurance Auto</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Assurance Habitation</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Assurance Santé</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Prévoyance</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Épargne Retraite</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-axa-red mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  123 Boulevard Mohammed V<br />
                  Casablanca 20000<br />
                  Maroc
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-axa-red flex-shrink-0" />
                <span className="text-gray-300 text-sm">+212 5XX-XXXXXX</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-axa-red flex-shrink-0" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 MOUMEN TECHNIQUE ET PREVOYANCE. Tous droits réservés.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                Mentions Légales
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                Politique de Confidentialité
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
                CGV
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};