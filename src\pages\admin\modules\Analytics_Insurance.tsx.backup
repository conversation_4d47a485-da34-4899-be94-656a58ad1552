import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  ComposedChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Shield,
  AlertTriangle,
  Target,
  Activity,
  PieChart as PieChartIcon,
  BarChart3,
  Calculator,
  Users,
  Car,
  Home,
  Heart,
  Building,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  Award,
  Zap,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { AdminStats, Toast as ToastType } from '../../../types/admin';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface InsuranceAnalytics {
  // KPIs Financiers
  financialMetrics: {
    totalPremiums: number;
    totalClaims: number;
    claimsRatio: number;
    grossProfit: number;
    netProfit: number;
    profitMargin: number;
    reserveRatio: number;
    solvencyRatio: number;
  };
  
  // Performance par Produit
  productPerformance: Array<{
    product: string;
    premiums: number;
    claims: number;
    claimsRatio: number;
    profitability: number;
    policyCount: number;
    avgPremium: number;
    growth: number;
  }>;
  
  // Évolution Temporelle
  timeSeriesData: Array<{
    period: string;
    premiums: number;
    claims: number;
    newPolicies: number;
    renewals: number;
    cancellations: number;
    netGrowth: number;
  }>;
  
  // Analyse des Risques
  riskAnalysis: {
    riskDistribution: Array<{
      category: string;
      count: number;
      percentage: number;
      avgClaim: number;
    }>;
    geographicRisk: Array<{
      region: string;
      claimsFrequency: number;
      avgClaimAmount: number;
      riskScore: number;
    }>;
    ageGroupRisk: Array<{
      ageGroup: string;
      claimsRatio: number;
      premiumVolume: number;
      profitability: number;
    }>;
  };
  
  // Métriques Clients
  customerMetrics: {
    totalClients: number;
    activeClients: number;
    newClients: number;
    churnRate: number;
    retentionRate: number;
    avgLifetimeValue: number;
    satisfactionScore: number;
    npsScore: number;
  };
  
  // Performance Commerciale
  salesMetrics: {
    conversionRate: number;
    avgSalesCycle: number;
    quotesToPolicies: number;
    crossSellRate: number;
    upsellRate: number;
    agentPerformance: Array<{
      agent: string;
      premiums: number;
      policies: number;
      conversionRate: number;
      customerSatisfaction: number;
    }>;
  };
  
  // Conformité Réglementaire
  complianceMetrics: {
    solvencyRatio: number;
    reserveAdequacy: number;
    regulatoryCapital: number;
    complianceScore: number;
    pendingReports: number;
    auditFindings: number;
  };
}

export const Analytics_Insurance: React.FC = () => {
  const [analytics, setAnalytics] = useState<InsuranceAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedView, setSelectedView] = useState('overview');
  const [toast, setToast] = useState<any>(null);

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      const response = await api.getInsuranceAnalytics(selectedPeriod);
      if (response.success) {
        setAnalytics(response.data);
      } else {
        // Données mock spécifiques à l'assurance
        setAnalytics({
          financialMetrics: {
            totalPremiums: 12500000,
            totalClaims: 8100000,
            claimsRatio: 0.648,
            grossProfit: 4400000,
            netProfit: 3200000,
            profitMargin: 0.256,
            reserveRatio: 0.85,
            solvencyRatio: 1.45
          },
          
          productPerformance: [
            {
              product: 'Auto',
              premiums: 6250000,
              claims: 4050000,
              claimsRatio: 0.648,
              profitability: 0.22,
              policyCount: 2500,
              avgPremium: 2500,
              growth: 0.12
            },
            {
              product: 'Habitation',
              premiums: 3125000,
              claims: 1875000,
              claimsRatio: 0.60,
              profitability: 0.28,
              policyCount: 1800,
              avgPremium: 1736,
              growth: 0.08
            },
            {
              product: 'Santé',
              premiums: 2500000,
              claims: 1800000,
              claimsRatio: 0.72,
              profitability: 0.18,
              policyCount: 1200,
              avgPremium: 2083,
              growth: 0.15
            },
            {
              product: 'Vie',
              premiums: 625000,
              claims: 375000,
              claimsRatio: 0.60,
              profitability: 0.35,
              policyCount: 300,
              avgPremium: 2083,
              growth: 0.05
            }
          ],
          
          timeSeriesData: [
            { period: 'Jan', premiums: 1000000, claims: 650000, newPolicies: 120, renewals: 450, cancellations: 25, netGrowth: 95 },
            { period: 'Fév', premiums: 1050000, claims: 680000, newPolicies: 135, renewals: 460, cancellations: 30, netGrowth: 105 },
            { period: 'Mar', premiums: 1100000, claims: 715000, newPolicies: 150, renewals: 470, cancellations: 20, netGrowth: 130 },
            { period: 'Avr', premiums: 1080000, claims: 700000, newPolicies: 140, renewals: 465, cancellations: 35, netGrowth: 105 },
            { period: 'Mai', premiums: 1120000, claims: 728000, newPolicies: 160, renewals: 480, cancellations: 28, netGrowth: 132 },
            { period: 'Juin', premiums: 1150000, claims: 747500, newPolicies: 155, renewals: 485, cancellations: 22, netGrowth: 133 }
          ],
          
          riskAnalysis: {
            riskDistribution: [
              { category: 'Faible', count: 3200, percentage: 55, avgClaim: 8500 },
              { category: 'Moyen', count: 1800, percentage: 31, avgClaim: 15000 },
              { category: 'Élevé', count: 650, percentage: 11, avgClaim: 28000 },
              { category: 'Très Élevé', count: 150, percentage: 3, avgClaim: 45000 }
            ],
            geographicRisk: [
              { region: 'Casablanca', claimsFrequency: 0.12, avgClaimAmount: 18500, riskScore: 75 },
              { region: 'Rabat', claimsFrequency: 0.09, avgClaimAmount: 16200, riskScore: 65 },
              { region: 'Marrakech', claimsFrequency: 0.08, avgClaimAmount: 14800, riskScore: 60 },
              { region: 'Fès', claimsFrequency: 0.07, avgClaimAmount: 13500, riskScore: 55 },
              { region: 'Tanger', claimsFrequency: 0.10, avgClaimAmount: 17000, riskScore: 70 }
            ],
            ageGroupRisk: [
              { ageGroup: '18-25', claimsRatio: 0.85, premiumVolume: 1200000, profitability: 0.08 },
              { ageGroup: '26-35', claimsRatio: 0.65, premiumVolume: 3500000, profitability: 0.25 },
              { ageGroup: '36-50', claimsRatio: 0.55, premiumVolume: 4800000, profitability: 0.32 },
              { ageGroup: '51-65', claimsRatio: 0.60, premiumVolume: 2500000, profitability: 0.28 },
              { ageGroup: '65+', claimsRatio: 0.75, premiumVolume: 500000, profitability: 0.15 }
            ]
          },
          
          customerMetrics: {
            totalClients: 5800,
            activeClients: 5200,
            newClients: 450,
            churnRate: 0.08,
            retentionRate: 0.92,
            avgLifetimeValue: 15600,
            satisfactionScore: 4.3,
            npsScore: 68
          },
          
          salesMetrics: {
            conversionRate: 0.68,
            avgSalesCycle: 14,
            quotesToPolicies: 0.72,
            crossSellRate: 0.35,
            upsellRate: 0.22,
            agentPerformance: [
              { agent: 'Fatima MOUMEN', premiums: 2800000, policies: 1200, conversionRate: 0.75, customerSatisfaction: 4.6 },
              { agent: 'Hassan IDRISSI', premiums: 2400000, policies: 980, conversionRate: 0.68, customerSatisfaction: 4.4 },
              { agent: 'Aicha BENNANI', premiums: 2100000, policies: 850, conversionRate: 0.72, customerSatisfaction: 4.5 },
              { agent: 'Mohammed ALAMI', premiums: 1900000, policies: 750, conversionRate: 0.65, customerSatisfaction: 4.2 }
            ]
          },
          
          complianceMetrics: {
            solvencyRatio: 1.45,
            reserveAdequacy: 0.95,
            regulatoryCapital: 25000000,
            complianceScore: 0.92,
            pendingReports: 2,
            auditFindings: 1
          }
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error);
      showToast('Erreur lors du chargement des analytics', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">Erreur lors du chargement des analytics</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec contrôles */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Assurance</h1>
          <p className="text-gray-600">Métriques et analyses spécialisées pour l'assurance</p>
        </div>
        
        <div className="flex space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
            <option value="year">Cette année</option>
          </select>
          
          <select
            value={selectedView}
            onChange={(e) => setSelectedView(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="overview">Vue d'ensemble</option>
            <option value="financial">Financier</option>
            <option value="risk">Analyse des risques</option>
            <option value="customer">Clients</option>
            <option value="compliance">Conformité</option>
          </select>
          
          <button
            onClick={loadAnalytics}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* KPIs Principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ratio S/P</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(analytics.financialMetrics.claimsRatio)}</p>
              <p className="text-xs text-gray-500">Sinistres/Primes</p>
            </div>
            <div className={`p-3 rounded-full ${analytics.financialMetrics.claimsRatio > 0.7 ? 'bg-red-100' : 'bg-green-100'}`}>
              <Shield className={`h-6 w-6 ${analytics.financialMetrics.claimsRatio > 0.7 ? 'text-red-600' : 'text-green-600'}`} />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {analytics.financialMetrics.claimsRatio > 0.7 ? (
              <TrendingUp className="h-4 w-4 text-red-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-green-500" />
            )}
            <span className={`text-sm ml-1 ${analytics.financialMetrics.claimsRatio > 0.7 ? 'text-red-600' : 'text-green-600'}`}>
              {analytics.financialMetrics.claimsRatio > 0.7 ? 'Attention' : 'Bon niveau'}
            </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Marge Bénéficiaire</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(analytics.financialMetrics.profitMargin)}</p>
              <p className="text-xs text-gray-500">Profit net</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">+2.3% vs mois dernier</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ratio de Solvabilité</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.financialMetrics.solvencyRatio.toFixed(2)}</p>
              <p className="text-xs text-gray-500">Capital/Risques</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">Conforme ACAPS</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rétention Client</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(analytics.customerMetrics.retentionRate)}</p>
              <p className="text-xs text-gray-500">Taux de fidélisation</p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">+1.2% vs trimestre</span>
          </div>
        </motion.div>
      </div>

      {/* Graphiques principaux selon la vue sélectionnée */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance par Produit */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance par Produit</h3>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={analytics.productPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="product" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === 'premiums' || name === 'claims' ? formatCurrency(value) : formatPercentage(value),
                    name === 'premiums' ? 'Primes' :
                    name === 'claims' ? 'Sinistres' :
                    name === 'claimsRatio' ? 'Ratio S/P' : name
                  ]}
                />
                <Legend />
                <Bar yAxisId="left" dataKey="premiums" fill="#0088FE" name="Primes" />
                <Bar yAxisId="left" dataKey="claims" fill="#FF8042" name="Sinistres" />
                <Line yAxisId="right" type="monotone" dataKey="claimsRatio" stroke="#00C49F" strokeWidth={3} name="Ratio S/P" />
              </ComposedChart>
            </ResponsiveContainer>
          </motion.div>

          {/* Évolution Temporelle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution des Primes et Sinistres</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analytics.timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
                <Tooltip formatter={(value: number) => [formatCurrency(value), '']} />
                <Area type="monotone" dataKey="premiums" stackId="1" stroke="#0088FE" fill="#0088FE" fillOpacity={0.6} />
                <Area type="monotone" dataKey="claims" stackId="2" stroke="#FF8042" fill="#FF8042" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </motion.div>
        </div>
      )}

      {selectedView === 'risk' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Distribution des Risques */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribution des Risques</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analytics.riskAnalysis.riskDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ category, percentage }) => `${category} ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {analytics.riskAnalysis.riskDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [value, 'Clients']} />
              </PieChart>
            </ResponsiveContainer>
          </motion.div>

          {/* Risque Géographique */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Analyse Géographique des Risques</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.riskAnalysis.geographicRisk}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="region" />
                <YAxis />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === 'avgClaimAmount' ? formatCurrency(value) : value,
                    name === 'claimsFrequency' ? 'Fréquence' :
                    name === 'avgClaimAmount' ? 'Montant Moyen' : 'Score de Risque'
                  ]}
                />
                <Bar dataKey="riskScore" fill="#FF8042" />
              </BarChart>
            </ResponsiveContainer>
          </motion.div>
        </div>
      )}

      {selectedView === 'customer' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Métriques Clients */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques Clients</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Clients Totaux</span>
                <span className="text-lg font-bold text-gray-900">{analytics.customerMetrics.totalClients.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Clients Actifs</span>
                <span className="text-lg font-bold text-green-600">{analytics.customerMetrics.activeClients.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Nouveaux Clients</span>
                <span className="text-lg font-bold text-blue-600">{analytics.customerMetrics.newClients.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Taux de Rétention</span>
                <span className="text-lg font-bold text-green-600">{formatPercentage(analytics.customerMetrics.retentionRate)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Valeur Vie Client</span>
                <span className="text-lg font-bold text-purple-600">{formatCurrency(analytics.customerMetrics.avgLifetimeValue)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Score Satisfaction</span>
                <span className="text-lg font-bold text-yellow-600">{analytics.customerMetrics.satisfactionScore}/5</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">NPS Score</span>
                <span className="text-lg font-bold text-green-600">{analytics.customerMetrics.npsScore}</span>
              </div>
            </div>
          </motion.div>

          {/* Performance des Agents */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance des Agents</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.salesMetrics.agentPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="agent" angle={-45} textAnchor="end" height={80} />
                <YAxis tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
                <Tooltip formatter={(value: number) => [formatCurrency(value), 'Primes']} />
                <Bar dataKey="premiums" fill="#0088FE" />
              </BarChart>
            </ResponsiveContainer>
          </motion.div>
        </div>
      )}

      {selectedView === 'compliance' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Métriques de Conformité */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Conformité Réglementaire</h3>
            <div className="space-y-6">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-600">Ratio de Solvabilité</span>
                  <span className="text-sm font-medium">{analytics.complianceMetrics.solvencyRatio.toFixed(2)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${Math.min((analytics.complianceMetrics.solvencyRatio / 2) * 100, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-green-600 mt-1">Conforme (min: 1.0)</p>
              </div>

              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-600">Adéquation des Réserves</span>
                  <span className="text-sm font-medium">{formatPercentage(analytics.complianceMetrics.reserveAdequacy)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${analytics.complianceMetrics.reserveAdequacy * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-blue-600 mt-1">Bon niveau</p>
              </div>

              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-600">Score de Conformité</span>
                  <span className="text-sm font-medium">{formatPercentage(analytics.complianceMetrics.complianceScore)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${analytics.complianceMetrics.complianceScore * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-green-600 mt-1">Excellent</p>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{analytics.complianceMetrics.pendingReports}</p>
                  <p className="text-xs text-gray-600">Rapports en attente</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{analytics.complianceMetrics.auditFindings}</p>
                  <p className="text-xs text-gray-600">Observations audit</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Capital Réglementaire */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Capital Réglementaire</h3>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">
                {formatCurrency(analytics.complianceMetrics.regulatoryCapital)}
              </div>
              <p className="text-gray-600 mb-6">Capital disponible</p>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Capital minimum requis</span>
                  <span className="text-sm font-medium">{formatCurrency(15000000)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Excédent de capital</span>
                  <span className="text-sm font-medium text-green-600">{formatCurrency(10000000)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Couverture</span>
                  <span className="text-sm font-medium text-green-600">167%</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-green-700 font-medium">Situation financière solide</p>
                <p className="text-xs text-green-600">Conformité ACAPS respectée</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Tableau de bord financier détaillé */}
      {selectedView === 'financial' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Tableau de Bord Financier</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-700">Revenus</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Primes brutes</span>
                  <span className="text-sm font-medium">{formatCurrency(analytics.financialMetrics.totalPremiums)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Commissions</span>
                  <span className="text-sm font-medium">{formatCurrency(analytics.financialMetrics.totalPremiums * 0.1)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Autres revenus</span>
                  <span className="text-sm font-medium">{formatCurrency(250000)}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-700">Charges</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Sinistres payés</span>
                  <span className="text-sm font-medium">{formatCurrency(analytics.financialMetrics.totalClaims)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Frais généraux</span>
                  <span className="text-sm font-medium">{formatCurrency(800000)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Réassurance</span>
                  <span className="text-sm font-medium">{formatCurrency(500000)}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-700">Résultats</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Résultat brut</span>
                  <span className="text-sm font-medium">{formatCurrency(analytics.financialMetrics.grossProfit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Résultat net</span>
                  <span className="text-sm font-medium text-green-600">{formatCurrency(analytics.financialMetrics.netProfit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Marge nette</span>
                  <span className="text-sm font-medium text-green-600">{formatPercentage(analytics.financialMetrics.profitMargin)}</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export { Analytics_Insurance as default };
