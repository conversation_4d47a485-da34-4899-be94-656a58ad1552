import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, User, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

export const Blog: React.FC = () => {
  const articles = [
    {
      id: 1,
      title: 'Nouvelle réglementation automobile au Maroc : ce qui change en 2024',
      excerpt: 'Découvrez les principales modifications de la réglementation automobile marocaine et leur impact sur votre assurance auto.',
      image: 'https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Assurance Auto',
      author: '<PERSON> MOUMEN',
      date: '2024-01-15',
      readTime: '5 min'
    },
    {
      id: 2,
      title: 'Comment bien choisir son assurance habitation ?',
      excerpt: 'Guide complet pour sélectionner la meilleure assurance habitation selon vos besoins et votre budget.',
      image: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Assurance Habitation',
      author: 'Fatima ALAMI',
      date: '2024-01-10',
      readTime: '7 min'
    },
    {
      id: 3,
      title: 'Assurance santé : les remboursements en 2024',
      excerpt: 'Tout savoir sur les taux de remboursement et les nouvelles garanties santé proposées par AXA.',
      image: 'https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Assurance Santé',
      author: 'Dr. Aicha BENALI',
      date: '2024-01-05',
      readTime: '6 min'
    },
    {
      id: 4,
      title: 'Préparer sa retraite : les solutions d\'épargne AXA',
      excerpt: 'Découvrez nos produits d\'épargne retraite pour constituer un capital et maintenir votre niveau de vie.',
      image: 'https://images.pexels.com/photos/6801648/pexels-photo-6801648.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Épargne Retraite',
      author: 'Youssef ALAOUI',
      date: '2023-12-28',
      readTime: '8 min'
    },
    {
      id: 5,
      title: 'Sinistre auto : les étapes à suivre',
      excerpt: 'Guide pratique pour bien gérer un sinistre automobile et optimiser votre indemnisation.',
      image: 'https://images.pexels.com/photos/5849579/pexels-photo-5849579.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Conseils',
      author: 'Ahmed BENALI',
      date: '2023-12-20',
      readTime: '4 min'
    },
    {
      id: 6,
      title: 'Assurance professionnelle : protégez votre activité',
      excerpt: 'L\'importance de l\'assurance professionnelle pour les entrepreneurs et les professions libérales.',
      image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800',
      category: 'Assurance Pro',
      author: 'Laila TAZI',
      date: '2023-12-15',
      readTime: '6 min'
    }
  ];

  const categories = [
    'Tous',
    'Assurance Auto',
    'Assurance Habitation',
    'Assurance Santé',
    'Épargne Retraite',
    'Assurance Pro',
    'Conseils'
  ];

  const [selectedCategory, setSelectedCategory] = React.useState('Tous');

  const filteredArticles = selectedCategory === 'Tous' 
    ? articles 
    : articles.filter(article => article.category === selectedCategory);

  return (
    <div className="py-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-axa-blue to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              Actualités & Conseils
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Restez informé des dernières actualités de l'assurance et découvrez nos conseils d'experts.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-axa-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-axa-blue text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.map((article, index) => (
              <motion.article
                key={article.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                <div className="relative overflow-hidden">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-axa-red text-white px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(article.date).toLocaleDateString('fr-FR')}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="h-4 w-4" />
                      <span>{article.author}</span>
                    </div>
                    <span>{article.readTime}</span>
                  </div>

                  <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-axa-blue transition-colors">
                    {article.title}
                  </h2>

                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {article.excerpt}
                  </p>

                  <Link
                    to={`/actualites/${article.id}`}
                    className="inline-flex items-center text-axa-blue font-semibold hover:text-axa-red transition-colors group"
                  >
                    Lire la suite
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>

          {/* Load More Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mt-12"
          >
            <button className="bg-axa-blue hover:bg-blue-800 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 inline-flex items-center group">
              Voir plus d'articles
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </motion.div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-axa-gray">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-white rounded-2xl shadow-xl p-8 text-center"
          >
            <h2 className="text-3xl font-bold text-axa-blue mb-4">
              Restez informé
            </h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Inscrivez-vous à notre newsletter pour recevoir nos derniers articles, conseils et actualités directement dans votre boîte mail.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
              />
              <button className="bg-axa-red hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                S'inscrire
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-4">
              Nous respectons votre vie privée. Désabonnement possible à tout moment.
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};