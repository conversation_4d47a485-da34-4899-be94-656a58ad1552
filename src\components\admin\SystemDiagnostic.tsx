import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw,
  Monitor,
  Database,
  Wifi,
  Shield,
  Zap
} from 'lucide-react';
import { runFullSystemTest } from '../../utils/moduleTest';

interface DiagnosticResult {
  summary: any;
  overallHealth: number;
  moduleResults: any[];
  apiResults: any[];
  componentResults: any[];
}

export const SystemDiagnostic: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<DiagnosticResult | null>(null);
  const [lastRun, setLastRun] = useState<Date | null>(null);

  const runDiagnostic = async () => {
    setIsRunning(true);
    try {
      const testResults = await runFullSystemTest();
      setResults(testResults);
      setLastRun(new Date());
    } catch (error) {
      console.error('Erreur lors du diagnostic:', error);
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    // Lancer le diagnostic automatiquement au chargement
    runDiagnostic();
  }, []);

  const getHealthColor = (health: number) => {
    if (health >= 90) return 'text-green-600';
    if (health >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthIcon = (health: number) => {
    if (health >= 90) return <CheckCircle className="h-6 w-6 text-green-600" />;
    if (health >= 70) return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
    return <XCircle className="h-6 w-6 text-red-600" />;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Activity className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Diagnostic Système</h2>
        </div>
        <button
          onClick={runDiagnostic}
          disabled={isRunning}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
          {isRunning ? 'Test en cours...' : 'Relancer le test'}
        </button>
      </div>

      {lastRun && (
        <p className="text-sm text-gray-500 mb-4">
          Dernier test: {lastRun.toLocaleString('fr-FR')}
        </p>
      )}

      {isRunning && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Diagnostic en cours...</span>
        </div>
      )}

      {results && !isRunning && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Santé globale */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getHealthIcon(results.overallHealth)}
                <div>
                  <h3 className="font-semibold text-gray-900">Santé Globale du Système</h3>
                  <p className="text-sm text-gray-600">État général de l'application</p>
                </div>
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(results.overallHealth)}`}>
                {results.overallHealth.toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Résumé par catégorie */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Modules */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-3">
                <Monitor className="h-5 w-5 text-blue-600" />
                <h4 className="font-medium text-gray-900">Modules</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total:</span>
                  <span className="font-medium">{results.summary.modules.total}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">Fonctionnels:</span>
                  <span className="font-medium text-green-600">{results.summary.modules.ok}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-red-600">Erreurs:</span>
                  <span className="font-medium text-red-600">{results.summary.modules.errors}</span>
                </div>
              </div>
            </div>

            {/* API */}
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-3">
                <Database className="h-5 w-5 text-green-600" />
                <h4 className="font-medium text-gray-900">API</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Endpoints:</span>
                  <span className="font-medium">{results.summary.api.total}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">Actifs:</span>
                  <span className="font-medium text-green-600">{results.summary.api.ok}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-red-600">Erreurs:</span>
                  <span className="font-medium text-red-600">{results.summary.api.errors}</span>
                </div>
              </div>
            </div>

            {/* Composants */}
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-3">
                <Zap className="h-5 w-5 text-purple-600" />
                <h4 className="font-medium text-gray-900">Composants</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total:</span>
                  <span className="font-medium">{results.summary.components.total}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">Fonctionnels:</span>
                  <span className="font-medium text-green-600">{results.summary.components.ok}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-red-600">Erreurs:</span>
                  <span className="font-medium text-red-600">{results.summary.components.errors}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Détails des erreurs */}
          {(results.moduleResults.some(r => r.status === 'ERROR') || 
            results.apiResults.some(r => r.status === 'ERROR') ||
            results.componentResults.some(r => r.status === 'ERROR')) && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-900 mb-3 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Problèmes Détectés
              </h4>
              
              <div className="space-y-2">
                {results.moduleResults
                  .filter(r => r.status === 'ERROR')
                  .map((result, index) => (
                    <div key={index} className="text-sm text-red-700">
                      <strong>Module {result.module}:</strong> {result.error || 'Erreur inconnue'}
                    </div>
                  ))}
                
                {results.apiResults
                  .filter(r => r.status === 'ERROR')
                  .map((result, index) => (
                    <div key={index} className="text-sm text-red-700">
                      <strong>API {result.endpoint}:</strong> {result.error || 'Erreur de connexion'}
                    </div>
                  ))}
                
                {results.componentResults
                  .filter(r => r.status === 'ERROR')
                  .map((result, index) => (
                    <div key={index} className="text-sm text-red-700">
                      <strong>Composant {result.component}:</strong> {result.error || 'Erreur de rendu'}
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Recommandations */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-3 flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Recommandations
            </h4>
            
            <div className="space-y-2 text-sm text-blue-700">
              {results.overallHealth < 90 && (
                <div>• Vérifier les modules en erreur et corriger les problèmes identifiés</div>
              )}
              {results.summary.api.errors > 0 && (
                <div>• Vérifier la connectivité réseau et les endpoints API</div>
              )}
              {results.summary.components.errors > 0 && (
                <div>• Recharger la page ou vider le cache du navigateur</div>
              )}
              {results.overallHealth >= 90 && (
                <div className="text-green-700">✅ Système en excellent état de fonctionnement</div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
