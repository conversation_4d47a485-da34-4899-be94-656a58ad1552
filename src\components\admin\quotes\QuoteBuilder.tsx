import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  PlusCircle, 
  Settings, 
  Euro,
  Calendar,
  Users,
  Calculator
} from 'lucide-react';
import type { QuoteTemplate } from '../../../types/admin';

interface QuoteBuilderProps {
  template?: QuoteTemplate;
  initialData?: Record<string, any>;
  onSave?: (data: Record<string, any>) => void;
}

export const QuoteBuilder: React.FC<QuoteBuilderProps> = ({
  template,
  initialData,
  onSave
}) => {
  const [activeSection, setActiveSection] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>(initialData || {});
  const [calculatedPrice, setCalculatedPrice] = useState<number>(0);
  const navigate = useNavigate();

  useEffect(() => {
    if (template && template.pricing) {
      let total = template.pricing.base;
      template.pricing.variables.forEach(variable => {
        const value = formData[variable.name];
        if (value) {
          switch (variable.type) {
            case 'MULTIPLIER':
              total *= value * variable.value;
              break;
            case 'FIXED':
              total += value * variable.value;
              break;
            case 'PERCENTAGE':
              total += total * (value * variable.value / 100);
              break;
          }
        }
      });
      setCalculatedPrice(total);
    }
  }, [template, formData]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateSection = (sectionIndex: number) => {
    const section = template?.sections[sectionIndex];
    if (!section) return true;

    return section.variables.every(variable => {
      if (!section.optional) {
        return formData[variable] !== undefined && formData[variable] !== '';
      }
      return true;
    });
  };

  const handleSave = () => {
    if (!template) return;

    const isValid = template.sections.every((_, index) => validateSection(index));
    if (!isValid) {
      // TODO: Afficher une erreur
      return;
    }

    const quoteData = {
      ...formData,
      templateId: template.id,
      calculatedPrice,
      validUntil: new Date(Date.now() + template.validity * 24 * 60 * 60 * 1000).toISOString(),
      terms: template.terms,
      customization: template.customization
    };

    onSave?.(quoteData);
  };

  if (!template) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Pas de modèle sélectionné</h3>
          <p className="mt-1 text-sm text-gray-500">
            Commencez par sélectionner un modèle de devis
          </p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => navigate('/admin/quotes/templates')}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
            >
              <PlusCircle className="h-5 w-5 mr-2" />
              Choisir un modèle
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {/* En-tête */}
          <div className="mb-8 border-b pb-4">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="h-6 w-6 mr-2" />
              {template.name}
            </h2>
            <p className="mt-1 text-sm text-gray-500">{template.description}</p>
          </div>

          {/* Barre de progression */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              {template.sections.map((section, index) => (
                <div
                  key={index}
                  className={`flex-1 ${
                    index < activeSection
                      ? 'bg-green-500'
                      : index === activeSection
                      ? 'bg-axa-blue'
                      : 'bg-gray-200'
                  } h-2 mx-1 rounded-full transition-all duration-200`}
                />
              ))}
            </div>
            <div className="text-sm text-gray-500 text-center">
              Section {activeSection + 1} sur {template.sections.length}
            </div>
          </div>

          {/* Section active */}
          <div className="mb-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {template.sections[activeSection].title}
              </h3>
              <div className="space-y-4">
                {template.sections[activeSection].variables.map(variable => (
                  <div key={variable} className="flex flex-col">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {variable}
                      {!template.sections[activeSection].optional && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    <input
                      type="text"
                      value={formData[variable] || ''}
                      onChange={e => handleInputChange(variable, e.target.value)}
                      className="mt-1 focus:ring-axa-blue focus:border-axa-blue block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Informations de prix */}
          <div className="mb-8 bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Euro className="h-5 w-5 mr-2" />
              Détails du prix
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <p className="text-sm text-gray-500">Prix de base</p>
                <p className="text-2xl font-bold">{template.pricing.base}€</p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <p className="text-sm text-gray-500">Variables</p>
                <p className="text-2xl font-bold">
                  {(calculatedPrice - template.pricing.base).toFixed(2)}€
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <p className="text-sm text-gray-500">Total</p>
                <p className="text-2xl font-bold text-axa-blue">
                  {calculatedPrice.toFixed(2)}€
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={() => setActiveSection(prev => Math.max(0, prev - 1))}
              disabled={activeSection === 0}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue disabled:opacity-50"
            >
              Précédent
            </button>
            {activeSection === template.sections.length - 1 ? (
              <button
                type="button"
                onClick={handleSave}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
              >
                <FileText className="h-5 w-5 mr-2" />
                Générer le devis
              </button>
            ) : (
              <button
                type="button"
                onClick={() => setActiveSection(prev => prev + 1)}
                disabled={!validateSection(activeSection)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue disabled:opacity-50"
              >
                Suivant
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteBuilder;
