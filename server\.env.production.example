# ===========================================
# CONFIGURATION PRODUCTION HOSTINGER VPS
# ===========================================

# Base de données MySQL Hostinger
# Remplacez par vos vraies informations de base de données
DATABASE_URL="mysql://your_db_user:your_db_password@localhost:3306/your_db_name?sslaccept=strict&charset=utf8mb4"

# JWT Configuration (IMPORTANT: Changez ces valeurs)
JWT_SECRET="your-super-secure-jwt-secret-for-production-change-this-immediately"
JWT_EXPIRES_IN="7d"

# Configuration Serveur
PORT=3001
NODE_ENV="production"

# URLs de production
FRONTEND_URL="https://your-domain.com"
API_URL="https://api.your-domain.com"

# Configuration Email (Hostinger SMTP ou Gmail)
# Option 1: SMTP Hostinger
SMTP_HOST="smtp.hostinger.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# Option 2: Gmail SMTP (alternative)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

FROM_EMAIL="<EMAIL>"
FROM_NAME="MOUMEN TECHNIQUE ET PREVOYANCE"

# Configuration Upload de fichiers
MAX_FILE_SIZE=10485760
UPLOAD_PATH="/var/www/html/uploads"

# Rate Limiting (plus strict en production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Logging
LOG_LEVEL="warn"
LOG_FILE="/var/log/mtp/app.log"

# Sécurité
BCRYPT_ROUNDS=14
SESSION_SECRET="your-super-secure-session-secret-change-this"

# APIs externes
AXA_API_URL="https://api.axa.ma"
AXA_API_KEY="your-production-axa-api-key"

# Monitoring (optionnel mais recommandé)
SENTRY_DSN="your-sentry-dsn-for-error-tracking"

# Configuration SSL/TLS
SSL_CERT_PATH="/etc/ssl/certs/your-domain.crt"
SSL_KEY_PATH="/etc/ssl/private/your-domain.key"

# Configuration Cache Redis (si disponible sur Hostinger)
# REDIS_URL="redis://localhost:6379"

# Configuration de sauvegarde
BACKUP_PATH="/var/backups/mtp"
BACKUP_RETENTION_DAYS=30

# Variables spécifiques Hostinger
HOSTINGER_PANEL_USER="your-hostinger-username"
HOSTINGER_DOMAIN="your-domain.com"
