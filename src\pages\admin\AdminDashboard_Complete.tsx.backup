import React, { useState, Suspense } from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  Users, 
  FileText, 
  Settings, 
  Bell,
  Search,
  Menu,
  X,
  LogOut,
  Target,
  Calculator,
  AlertTriangle,
  TrendingUp,
  Shield,
  CheckSquare,
  PieChart,
  Building,
  ChevronDown,
  ChevronRight,
  Home,
  Megaphone,
  BookOpen,
  Zap,
  Globe,
  Database,
  FileImage,
  GraduationCap,
  Lock,
  Briefcase,
  Layout,
  Wrench
} from 'lucide-react';
import { ErrorBoundary, AdminModuleWrapper } from '../../components/admin/ErrorBoundary';

// Lazy loading pour éviter les erreurs d'import - Modules Assurance Spécialisés
const DashboardOverview_Insurance = React.lazy(() => import('./modules/DashboardOverview_Insurance'));
const LeadsManagement_Insurance = React.lazy(() => import('./modules/LeadsManagement_Insurance'));
const ClientsManagement_Insurance = React.lazy(() => import('./modules/ClientsManagement_Insurance'));
const QuotesContracts_Professional = React.lazy(() => import('./modules/QuotesContracts_Professional'));
const ClaimsManagement_Professional = React.lazy(() => import('./modules/ClaimsManagement_Professional'));
const Analytics_Insurance = React.lazy(() => import('./modules/Analytics_Insurance'));
const Compliance_ACAPS = React.lazy(() => import('./modules/Compliance_ACAPS'));

// Lazy loading - Modules CRM Core
const DashboardOverview = React.lazy(() => import('./modules/DashboardOverview'));
const LeadsManagement = React.lazy(() => import('./modules/LeadsManagement'));
const ClientsManagement = React.lazy(() => import('./modules/ClientsManagement'));
const QuotesContracts = React.lazy(() => import('./modules/QuotesContracts'));
const ClaimsManagement = React.lazy(() => import('./modules/ClaimsManagement'));
const Analytics = React.lazy(() => import('./modules/Analytics'));

// Lazy loading - Modules Marketing & Communication
const DigitalMarketing = React.lazy(() => import('./modules/DigitalMarketing'));
const ContentManagement = React.lazy(() => import('./modules/ContentManagement'));
const BannerManagement = React.lazy(() => import('./modules/BannerManagement'));
const ELearning = React.lazy(() => import('./modules/ELearning'));

// Lazy loading - Modules Administration
const UserManagement = React.lazy(() => import('./modules/UserManagement'));
const DocumentsManagement = React.lazy(() => import('./modules/DocumentsManagement'));
const TasksReminders = React.lazy(() => import('./modules/TasksReminders'));
const SettingsModule = React.lazy(() => import('./modules/Settings'));
const Administration = React.lazy(() => import('./modules/Administration'));
const Security = React.lazy(() => import('./modules/Security'));

// Lazy loading - Modules Outils & Intégrations
const Integrations = React.lazy(() => import('./modules/Integrations'));
const Calculators = React.lazy(() => import('./modules/Calculators'));
const PDFGenerator = React.lazy(() => import('./modules/PDFGenerator'));
const QuoteFormManagement = React.lazy(() => import('./modules/QuoteFormManagement'));

// Composant de chargement pour les modules lazy
const ModuleLoader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
    <span className="ml-3 text-gray-600">Chargement du module...</span>
  </div>
);

// Wrapper pour les modules lazy avec Suspense
const LazyModuleWrapper: React.FC<{ children: React.ReactNode; moduleName: string }> = ({ children, moduleName }) => (
  <AdminModuleWrapper moduleName={moduleName}>
    <Suspense fallback={<ModuleLoader />}>
      {children}
    </Suspense>
  </AdminModuleWrapper>
);

// Interface pour les éléments de navigation
interface NavigationItem {
  id: string;
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  color: string;
  badge?: number;
  description?: string;
}

interface NavigationSection {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  items: NavigationItem[];
  collapsed?: boolean;
}

export const AdminDashboard: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<string[]>(['crm-core']);
  const location = useLocation();

  // Navigation complète organisée par sections logiques
  const navigationSections: NavigationSection[] = [
    {
      id: 'assurance',
      name: 'Assurance Professionnelle',
      icon: Shield,
      items: [
        { 
          id: 'dashboard-insurance', 
          name: 'Dashboard Assurance', 
          href: '/admin/dashboard-insurance', 
          icon: BarChart3, 
          color: 'text-blue-600',
          description: 'KPIs et métriques spécialisées assurance'
        },
        { 
          id: 'leads-insurance', 
          name: 'Prospects Assurance', 
          href: '/admin/leads-insurance', 
          icon: Target, 
          color: 'text-green-600',
          description: 'Pipeline commercial avec scoring intelligent'
        },
        { 
          id: 'clients-insurance', 
          name: 'Clients Assurance', 
          href: '/admin/clients-insurance', 
          icon: Users, 
          color: 'text-purple-600',
          description: 'Portefeuille clients avec profils enrichis'
        },
        { 
          id: 'quotes-pro', 
          name: 'Devis Professionnels', 
          href: '/admin/quotes-pro', 
          icon: Calculator, 
          color: 'text-orange-600',
          description: 'Calculateur de primes intégré'
        },
        { 
          id: 'claims-pro', 
          name: 'Sinistres Pro', 
          href: '/admin/claims-pro', 
          icon: AlertTriangle, 
          color: 'text-red-600',
          description: 'Workflow complet de gestion des sinistres'
        },
        { 
          id: 'analytics-insurance', 
          name: 'Analytics Assurance', 
          href: '/admin/analytics-insurance', 
          icon: PieChart, 
          color: 'text-indigo-600',
          description: 'Analyses et métriques sectorielles'
        },
        { 
          id: 'compliance-acaps', 
          name: 'Conformité ACAPS', 
          href: '/admin/compliance-acaps', 
          icon: Shield, 
          color: 'text-emerald-600',
          description: 'Reporting réglementaire automatisé'
        }
      ]
    },
    {
      id: 'marketing',
      name: 'Marketing & Communication',
      icon: Megaphone,
      items: [
        { 
          id: 'digital-marketing', 
          name: 'Marketing Digital', 
          href: '/admin/digital-marketing', 
          icon: Megaphone, 
          color: 'text-pink-600',
          description: 'Campagnes, SEO, réseaux sociaux'
        },
        { 
          id: 'content-management', 
          name: 'Gestion Contenu', 
          href: '/admin/content-management', 
          icon: FileText, 
          color: 'text-blue-500',
          description: 'Contenu du site web et blog'
        },
        { 
          id: 'banner-management', 
          name: 'Gestion Bannières', 
          href: '/admin/banner-management', 
          icon: FileImage, 
          color: 'text-yellow-600',
          description: 'Bannières et visuels promotionnels'
        },
        { 
          id: 'elearning', 
          name: 'E-Learning', 
          href: '/admin/elearning', 
          icon: GraduationCap, 
          color: 'text-green-500',
          description: 'Formations en ligne et certifications'
        }
      ]
    },
    {
      id: 'tools',
      name: 'Outils & Intégrations',
      icon: Wrench,
      items: [
        { 
          id: 'integrations', 
          name: 'Intégrations', 
          href: '/admin/integrations', 
          icon: Zap, 
          color: 'text-purple-500',
          description: 'APIs et services externes'
        },
        { 
          id: 'calculators', 
          name: 'Calculateurs', 
          href: '/admin/calculators', 
          icon: Calculator, 
          color: 'text-blue-600',
          description: 'Outils de calcul et simulation'
        },
        { 
          id: 'pdf-generator', 
          name: 'Générateur PDF', 
          href: '/admin/pdf-generator', 
          icon: FileText, 
          color: 'text-red-500',
          description: 'Génération de documents PDF'
        },
        { 
          id: 'quote-form', 
          name: 'Formulaires Devis', 
          href: '/admin/quote-form', 
          icon: Layout, 
          color: 'text-cyan-600',
          description: 'Configuration des formulaires'
        }
      ]
    },
    {
      id: 'administration',
      name: 'Administration',
      icon: Settings,
      items: [
        { 
          id: 'tasks', 
          name: 'Tâches & Rappels', 
          href: '/admin/tasks', 
          icon: CheckSquare, 
          color: 'text-blue-500',
          description: 'Gestion des tâches et rappels'
        },
        { 
          id: 'documents', 
          name: 'Documents', 
          href: '/admin/documents', 
          icon: FileText, 
          color: 'text-gray-600',
          description: 'Système de gestion documentaire'
        },
        { 
          id: 'users', 
          name: 'Utilisateurs', 
          href: '/admin/users', 
          icon: Users, 
          color: 'text-cyan-500',
          description: 'Gestion des utilisateurs et rôles'
        },
        { 
          id: 'administration', 
          name: 'Administration', 
          href: '/admin/administration', 
          icon: Briefcase, 
          color: 'text-purple-600',
          description: 'Configuration générale du système'
        },
        { 
          id: 'security', 
          name: 'Sécurité', 
          href: '/admin/security', 
          icon: Lock, 
          color: 'text-red-600',
          description: 'Sécurité et audit du système'
        },
        { 
          id: 'settings', 
          name: 'Paramètres', 
          href: '/admin/settings', 
          icon: Settings, 
          color: 'text-gray-600',
          description: 'Configuration système avancée'
        }
      ]
    },
    {
      id: 'crm-core',
      name: 'CRM Standard',
      icon: Database,
      items: [
        { 
          id: 'dashboard', 
          name: 'Dashboard Standard', 
          href: '/admin/dashboard', 
          icon: BarChart3, 
          color: 'text-blue-400',
          description: 'Dashboard CRM standard'
        },
        { 
          id: 'leads', 
          name: 'Prospects Standard', 
          href: '/admin/leads', 
          icon: Target, 
          color: 'text-green-400',
          description: 'Gestion prospects standard'
        },
        { 
          id: 'clients', 
          name: 'Clients Standard', 
          href: '/admin/clients', 
          icon: Users, 
          color: 'text-purple-400',
          description: 'Gestion clients standard'
        },
        { 
          id: 'quotes', 
          name: 'Devis Standard', 
          href: '/admin/quotes', 
          icon: Calculator, 
          color: 'text-orange-400',
          description: 'Gestion devis standard'
        },
        { 
          id: 'claims', 
          name: 'Sinistres Standard', 
          href: '/admin/claims', 
          icon: AlertTriangle, 
          color: 'text-red-400',
          description: 'Gestion sinistres standard'
        },
        { 
          id: 'analytics', 
          name: 'Analytics Standard', 
          href: '/admin/analytics', 
          icon: PieChart, 
          color: 'text-indigo-400',
          description: 'Analytics CRM standard'
        }
      ]
    }
  ];

  const isActive = (path: string) => location.pathname === path;

  const toggleSection = (sectionId: string) => {
    setCollapsedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const isSectionCollapsed = (sectionId: string) => collapsedSections.includes(sectionId);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Header */}
        <div className="flex items-center justify-between h-20 px-6 border-b border-gray-200 bg-gradient-to-r from-axa-blue to-blue-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-axa-blue" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">Moumen AXA</h1>
              <p className="text-xs text-blue-100">Console Admin Complète</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-white hover:text-blue-200 p-2"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 overflow-y-auto">
          <div className="space-y-4">
            {navigationSections.map((section) => (
              <div key={section.id} className="space-y-2">
                {/* Section Header */}
                <button
                  onClick={() => toggleSection(section.id)}
                  className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <section.icon className="h-4 w-4" />
                    <span>{section.name}</span>
                    <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                      {section.items.length}
                    </span>
                  </div>
                  {isSectionCollapsed(section.id) ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </button>

                {/* Section Items */}
                <AnimatePresence>
                  {!isSectionCollapsed(section.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="space-y-1 ml-6"
                    >
                      {section.items.map((item) => (
                        <motion.div
                          key={item.id}
                          whileHover={{ x: 4 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <a
                            href={item.href}
                            className={`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group ${
                              isActive(item.href)
                                ? 'bg-axa-blue text-white shadow-lg transform scale-105'
                                : 'text-gray-700 hover:bg-gray-100 hover:text-axa-blue'
                            }`}
                            title={item.description}
                          >
                            <item.icon className={`mr-3 h-5 w-5 ${
                              isActive(item.href) ? 'text-white' : item.color
                            } group-hover:scale-110 transition-transform`} />
                            <div className="flex-1">
                              <span className="truncate">{item.name}</span>
                              {item.badge && (
                                <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                          </a>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-axa-blue rounded-full flex items-center justify-center">
              <span className="text-white text-lg font-medium">A</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate">Admin User</div>
              <div className="text-xs text-gray-500 truncate"><EMAIL></div>
              <div className="text-xs text-green-600 font-medium">Console Complète</div>
            </div>
          </div>
          <button className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
            <LogOut className="mr-3 h-4 w-4" />
            Déconnexion
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Top Bar */}
        <div className="bg-white shadow-sm border-b border-gray-200 h-20 flex items-center px-6">
          <div className="flex items-center space-x-4 flex-1">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700 p-2"
            >
              <Menu className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                Console d'Administration Complète
              </h1>
              <p className="text-sm text-gray-500">
                CRM Professionnel • Marketing • E-Learning • Intégrations
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher dans tous les modules..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent w-96"
              />
            </div>

            <button className="relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Bell className="h-6 w-6" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                5
              </span>
            </button>

            <div className="flex items-center space-x-3">
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">Admin User</div>
                <div className="text-xs text-gray-500">Console Complète</div>
              </div>
              <div className="w-10 h-10 bg-axa-blue rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">A</span>
              </div>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="flex-1 p-6 overflow-auto">
          <ErrorBoundary>
            <Routes>
              {/* Routes Assurance Spécialisées */}
              <Route path="/dashboard-insurance" element={<LazyModuleWrapper moduleName="Dashboard Assurance"><DashboardOverview_Insurance /></LazyModuleWrapper>} />
              <Route path="/leads-insurance" element={<LazyModuleWrapper moduleName="Prospects Assurance"><LeadsManagement_Insurance /></LazyModuleWrapper>} />
              <Route path="/clients-insurance" element={<LazyModuleWrapper moduleName="Clients Assurance"><ClientsManagement_Insurance /></LazyModuleWrapper>} />
              <Route path="/quotes-pro" element={<LazyModuleWrapper moduleName="Devis Professionnels"><QuotesContracts_Professional /></LazyModuleWrapper>} />
              <Route path="/claims-pro" element={<LazyModuleWrapper moduleName="Sinistres Pro"><ClaimsManagement_Professional /></LazyModuleWrapper>} />
              <Route path="/analytics-insurance" element={<LazyModuleWrapper moduleName="Analytics Assurance"><Analytics_Insurance /></LazyModuleWrapper>} />
              <Route path="/compliance-acaps" element={<LazyModuleWrapper moduleName="Conformité ACAPS"><Compliance_ACAPS /></LazyModuleWrapper>} />

              {/* Routes Marketing & Communication */}
              <Route path="/digital-marketing" element={<LazyModuleWrapper moduleName="Marketing Digital"><DigitalMarketing /></LazyModuleWrapper>} />
              <Route path="/content-management" element={<LazyModuleWrapper moduleName="Gestion Contenu"><ContentManagement /></LazyModuleWrapper>} />
              <Route path="/banner-management" element={<LazyModuleWrapper moduleName="Gestion Bannières"><BannerManagement /></LazyModuleWrapper>} />
              <Route path="/elearning" element={<LazyModuleWrapper moduleName="E-Learning"><ELearning /></LazyModuleWrapper>} />

              {/* Routes Outils & Intégrations */}
              <Route path="/integrations" element={<LazyModuleWrapper moduleName="Intégrations"><Integrations /></LazyModuleWrapper>} />
              <Route path="/calculators" element={<LazyModuleWrapper moduleName="Calculateurs"><Calculators /></LazyModuleWrapper>} />
              <Route path="/pdf-generator" element={<LazyModuleWrapper moduleName="Générateur PDF"><PDFGenerator /></LazyModuleWrapper>} />
              <Route path="/quote-form" element={<LazyModuleWrapper moduleName="Formulaires Devis"><QuoteFormManagement /></LazyModuleWrapper>} />

              {/* Routes Administration */}
              <Route path="/tasks" element={<LazyModuleWrapper moduleName="Tâches"><TasksReminders /></LazyModuleWrapper>} />
              <Route path="/documents" element={<LazyModuleWrapper moduleName="Documents"><DocumentsManagement /></LazyModuleWrapper>} />
              <Route path="/users" element={<LazyModuleWrapper moduleName="Utilisateurs"><UserManagement /></LazyModuleWrapper>} />
              <Route path="/administration" element={<LazyModuleWrapper moduleName="Administration"><Administration /></LazyModuleWrapper>} />
              <Route path="/security" element={<LazyModuleWrapper moduleName="Sécurité"><Security /></LazyModuleWrapper>} />
              <Route path="/settings" element={<LazyModuleWrapper moduleName="Paramètres"><SettingsModule /></LazyModuleWrapper>} />

              {/* Routes CRM Standard - Maintenant activées */}
              <Route path="/dashboard" element={<LazyModuleWrapper moduleName="Dashboard Standard"><DashboardOverview /></LazyModuleWrapper>} />
              <Route path="/leads" element={<LazyModuleWrapper moduleName="Prospects Standard"><LeadsManagement /></LazyModuleWrapper>} />
              <Route path="/clients" element={<LazyModuleWrapper moduleName="Clients Standard"><ClientsManagement /></LazyModuleWrapper>} />
              <Route path="/quotes" element={<LazyModuleWrapper moduleName="Devis Standard"><QuotesContracts /></LazyModuleWrapper>} />
              <Route path="/claims" element={<LazyModuleWrapper moduleName="Sinistres Standard"><ClaimsManagement /></LazyModuleWrapper>} />
              <Route path="/analytics" element={<LazyModuleWrapper moduleName="Analytics Standard"><Analytics /></LazyModuleWrapper>} />

              {/* Redirection par défaut */}
              <Route path="/" element={<Navigate to="/admin/dashboard-insurance" replace />} />
              <Route path="*" element={<Navigate to="/admin/dashboard-insurance" replace />} />
            </Routes>
          </ErrorBoundary>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};
