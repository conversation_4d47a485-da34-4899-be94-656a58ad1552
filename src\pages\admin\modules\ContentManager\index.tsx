import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Image,
  Layers,
  Layout,
  Plus,
  Search,
  Filter,
  Grid,
  List as ListIcon,
  Edit,
  Trash2,
  Eye,
  Save,
  X,
  Upload,
  Link,
  Calendar,
  Tag,
  Settings,
  ChevronDown,
  MessageSquare,
  Check,
} from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ReactMarkdown from 'react-markdown';
import { api } from '../../../../services/api';
import { Toast } from '../../../../components/common/Toast';
import { MediaLibrary } from './MediaLibrary';
import { ContentEditor } from './ContentEditor';
import { Tabs } from './Tabs';
import { ContentList } from './ContentList';
import { SEOPanel } from './SEOPanel';
import { SchedulingPanel } from './SchedulingPanel';
import { CategoryManager } from './CategoryManager';
import { MetadataPanel } from './MetadataPanel';
import { PreviewModal } from './PreviewModal';
import { validateContent } from './validation';
import type { ContentItem, EditorState, MediaFile, ContentFilter, ViewMode } from './types';

export const ContentManager: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('all');
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<ContentItem[]>([]);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [filters, setFilters] = useState<ContentFilter>({
    search: '',
    status: 'all',
    type: 'all',
    category: 'all',
    dateRange: null,
  });

  const { handleSubmit, control, reset, formState: { errors } } = useForm({
    validate: validateContent,
    defaultValues: {
      title: '',
      slug: '',
      content: '',
      excerpt: '',
      status: 'draft',
      seo: {
        title: '',
        description: '',
        keywords: '',
      },
      publishDate: null,
      unpublishDate: null,
      category: '',
      tags: [],
      featuredImage: '',
    },
  });

  useEffect(() => {
    loadAllContent();
  }, []);

  useEffect(() => {
    filterContent();
  }, [contentItems, filters, selectedTab]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadAllContent = async () => {
    setIsLoading(true);
    try {
      const [pagesResponse, blogResponse, mediaResponse] = await Promise.all([
        api.getContentPages(),
        api.getBlogPosts(),
        api.getMediaFiles(),
      ]);

      const allItems: ContentItem[] = [];

      if (pagesResponse.success && pagesResponse.data?.pages) {
        const pages = pagesResponse.data.pages.map(page => ({
          ...page,
          type: 'page' as const,
        }));
        allItems.push(...pages);
      }

      if (blogResponse.success && blogResponse.data?.posts) {
        const posts = blogResponse.data.posts.map(post => ({
          ...post,
          type: 'blog' as const,
        }));
        allItems.push(...posts);
      }

      if (mediaResponse.success && mediaResponse.data?.files) {
        const media = mediaResponse.data.files.map(file => ({
          id: file.id,
          title: file.originalName,
          slug: file.name,
          type: 'media' as const,
          status: 'published' as const,
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
          size: file.size,
          mimeType: file.mimeType,
          url: file.url,
        }));
        allItems.push(...media);
        setMediaFiles(mediaResponse.data.files);
      }

      setContentItems(allItems);
      showToast('Contenu chargé avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      showToast('Erreur lors du chargement du contenu', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const filterContent = () => {
    let filtered = contentItems;

    // Filtre par onglet
    if (selectedTab !== 'all') {
      filtered = filtered.filter(item => item.type === selectedTab);
    }

    // Filtre par recherche
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchTerm) ||
        item.slug.toLowerCase().includes(searchTerm) ||
        (item.excerpt && item.excerpt.toLowerCase().includes(searchTerm))
      );
    }

    // Filtre par statut
    if (filters.status !== 'all') {
      filtered = filtered.filter(item => item.status === filters.status);
    }

    // Filtre par catégorie
    if (filters.category !== 'all') {
      filtered = filtered.filter(item => 
        'category' in item && item.category === filters.category
      );
    }

    // Filtre par date
    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      filtered = filtered.filter(item => {
        const date = new Date(item.createdAt);
        return date >= start && date <= end;
      });
    }

    setFilteredItems(filtered);
  };

  const onSubmit = async (data: any) => {
    try {
      let response;
      
      if (editingItem) {
        response = await api.updateContent(editingItem.type, editingItem.id, data);
      } else {
        response = await api.createContent(selectedTab, data);
      }

      if (response.success) {
        showToast(
          editingItem ? 'Contenu mis à jour avec succès' : 'Contenu créé avec succès',
          'success'
        );
        setShowEditor(false);
        loadAllContent();
      } else {
        showToast(response.message || 'Une erreur est survenue', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      showToast('Erreur lors de la sauvegarde', 'error');
    }
  };

  const handleDeleteContent = async (item: ContentItem) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer "${item.title}" ?`)) {
      return;
    }

    try {
      const response = await api.deleteContent(item.type, item.id);
      
      if (response.success) {
        showToast('Contenu supprimé avec succès', 'success');
        loadAllContent();
      } else {
        showToast(response.message || 'Erreur lors de la suppression', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      showToast('Erreur lors de la suppression', 'error');
    }
  };

  const openEditor = (item?: ContentItem) => {
    if (item) {
      reset({
        title: item.title,
        slug: item.slug,
        content: item.content,
        excerpt: item.excerpt,
        status: item.status,
        seo: item.seo,
        publishDate: item.publishDate,
        unpublishDate: item.unpublishDate,
        category: item.category,
        tags: item.tags,
        featuredImage: item.featuredImage,
      });
      setEditingItem(item);
    } else {
      reset();
      setEditingItem(null);
    }
    setShowEditor(true);
  };

  const tabs = [
    { id: 'all', name: 'Tout', icon: Layers, count: contentItems.length },
    { id: 'page', name: 'Pages', icon: Layout, count: contentItems.filter(i => i.type === 'page').length },
    { id: 'blog', name: 'Blog', icon: FileText, count: contentItems.filter(i => i.type === 'blog').length },
    { id: 'media', name: 'Médias', icon: Image, count: contentItems.filter(i => i.type === 'media').length },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion de Contenu</h1>
          <p className="text-gray-600 mt-2">
            Gérez tous vos contenus depuis une interface unifiée
          </p>
        </div>
        <div className="flex space-x-3">
          <label className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors cursor-pointer">
            <Upload className="h-4 w-4 mr-2" />
            Import Media
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx"
              onChange={(e) => e.target.files && handleMediaUpload(e.target.files)}
              className="hidden"
            />
          </label>
          <button
            onClick={() => openEditor()}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Contenu
          </button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs
        tabs={tabs}
        selectedTab={selectedTab}
        onTabChange={setSelectedTab}
      />

      {/* Filters */}
      <div className="flex justify-between items-center space-x-4 bg-white p-4 rounded-lg shadow">
        <div className="flex-1 flex space-x-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Rechercher..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
            <Search className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
          </div>
          <select
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="draft">Brouillon</option>
            <option value="published">Publié</option>
            <option value="scheduled">Programmé</option>
            <option value="archived">Archivé</option>
          </select>
          {(selectedTab === 'blog' || selectedTab === 'all') && (
            <select
              value={filters.category}
              onChange={(e) => setFilters({ ...filters, category: e.target.value })}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            >
              <option value="all">Toutes les catégories</option>
              <option value="actualites">Actualités</option>
              <option value="conseils">Conseils</option>
              <option value="guides">Guides</option>
            </select>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${
              viewMode === 'grid'
                ? 'bg-axa-blue text-white'
                : 'text-gray-400 hover:bg-gray-100'
            }`}
          >
            <Grid className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${
              viewMode === 'list'
                ? 'bg-axa-blue text-white'
                : 'text-gray-400 hover:bg-gray-100'
            }`}
          >
            <ListIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Content List */}
      <ContentList
        items={filteredItems}
        viewMode={viewMode}
        onEdit={openEditor}
        onDelete={handleDeleteContent}
        onPreview={(item) => {
          setEditingItem(item);
          setShowPreview(true);
        }}
      />

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={() => setShowEditor(false)}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="inline-block w-full max-w-6xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="flex h-[calc(100vh-8rem)]">
                  {/* Main Content */}
                  <div className="flex-1 flex flex-col">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-gray-900">
                          {editingItem ? 'Modifier le contenu' : 'Nouveau contenu'}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <button
                            type="button"
                            onClick={() => setShowPreview(true)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Prévisualiser
                          </button>
                          <button
                            type="submit"
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                          >
                            <Save className="h-4 w-4 mr-2" />
                            Sauvegarder
                          </button>
                          <button
                            type="button"
                            onClick={() => setShowEditor(false)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1 overflow-y-auto">
                      <div className="p-6 space-y-6">
                        <div className="grid grid-cols-2 gap-6">
                          <Controller
                            name="title"
                            control={control}
                            render={({ field }) => (
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Titre
                                </label>
                                <input
                                  {...field}
                                  type="text"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                                  placeholder="Titre du contenu"
                                />
                                {errors.title && (
                                  <p className="mt-1 text-sm text-red-600">
                                    {errors.title.message as string}
                                  </p>
                                )}
                              </div>
                            )}
                          />

                          <Controller
                            name="slug"
                            control={control}
                            render={({ field }) => (
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Slug
                                </label>
                                <input
                                  {...field}
                                  type="text"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                                  placeholder="slug-du-contenu"
                                />
                                {errors.slug && (
                                  <p className="mt-1 text-sm text-red-600">
                                    {errors.slug.message as string}
                                  </p>
                                )}
                              </div>
                            )}
                          />
                        </div>

                        <Controller
                          name="content"
                          control={control}
                          render={({ field }) => (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Contenu
                              </label>
                              <ContentEditor
                                value={field.value}
                                onChange={field.onChange}
                                onMediaSelect={(url) => {
                                  const textarea = document.querySelector('textarea');
                                  if (textarea) {
                                    const start = textarea.selectionStart;
                                    const end = textarea.selectionEnd;
                                    const text = textarea.value;
                                    const before = text.substring(0, start);
                                    const after = text.substring(end);
                                    field.onChange(`${before}![](${url})${after}`);
                                  }
                                }}
                              />
                              {errors.content && (
                                <p className="mt-1 text-sm text-red-600">
                                  {errors.content.message as string}
                                </p>
                              )}
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Sidebar */}
                  <div className="w-80 border-l border-gray-200 bg-gray-50">
                    <div className="h-full overflow-y-auto">
                      <div className="p-6 space-y-6">
                        <Controller
                          name="status"
                          control={control}
                          render={({ field }) => (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Statut
                              </label>
                              <select
                                {...field}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                              >
                                <option value="draft">Brouillon</option>
                                <option value="published">Publié</option>
                                <option value="scheduled">Programmé</option>
                                <option value="archived">Archivé</option>
                              </select>
                            </div>
                          )}
                        />

                        <SchedulingPanel control={control} errors={errors} />
                        <SEOPanel control={control} errors={errors} />
                        <CategoryManager control={control} errors={errors} />
                        <MetadataPanel control={control} errors={errors} />
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </motion.div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && (
        <PreviewModal
          content={editingItem}
          onClose={() => setShowPreview(false)}
        />
      )}

      {/* Media Library Modal */}
      {showMediaLibrary && (
        <MediaLibrary
          files={mediaFiles}
          onSelect={(file) => {
            // Handle media selection
          }}
          onClose={() => setShowMediaLibrary(false)}
          onUpload={handleMediaUpload}
        />
      )}

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default ContentManager;
