import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Car, 
  Home as HomeIcon, 
  Heart, 
  Shield, 
  PiggyBank, 
  Building2,
  ArrowRight,
  CheckCircle,
  Calculator,
  FileText,
  Phone
} from 'lucide-react';
import { Link } from 'react-router-dom';

export const Products: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('auto');

  const categories = [
    { id: 'auto', name: 'Assurance Auto', icon: Car },
    { id: 'habitation', name: 'Assurance Habitation', icon: HomeIcon },
    { id: 'sante', name: 'Assurance Santé', icon: Heart },
    { id: 'prevoyance', name: 'Prévoyance', icon: Shield },
    { id: 'epargne', name: 'Épargne Retraite', icon: PiggyBank },
    { id: 'pro', name: 'Assurance Pro', icon: Building2 }
  ];

  const products = {
    auto: {
      title: 'Assurance Auto',
      subtitle: 'Protection complète pour votre véhicule',
      description: 'Roulez en toute sérénité avec nos formules d\'assurance auto adaptées à tous les profils de conducteurs.',
      features: [
        'Responsabilité civile obligatoire',
        'Dommages tous accidents',
        'Vol et incendie',
        'Bris de glace',
        'Assistance 24h/24',
        'Véhicule de remplacement',
        'Protection juridique',
        'Garantie du conducteur'
      ],
      formulas: [
        {
          name: 'Essentielle',
          price: 'À partir de 1 200 DH/an',
          features: ['RC obligatoire', 'Assistance de base', 'Protection juridique']
        },
        {
          name: 'Confort',
          price: 'À partir de 2 400 DH/an',
          features: ['Tous risques', 'Vol/Incendie', 'Bris de glace', 'Assistance étendue']
        },
        {
          name: 'Premium',
          price: 'À partir de 3 600 DH/an',
          features: ['Couverture maximale', 'Véhicule de remplacement', 'Garantie valeur à neuf']
        }
      ]
    },
    habitation: {
      title: 'Assurance Habitation',
      subtitle: 'Protégez votre foyer et vos biens',
      description: 'Une protection complète pour votre logement, vos biens et votre famille contre tous les risques du quotidien.',
      features: [
        'Incendie et explosion',
        'Dégâts des eaux',
        'Vol et vandalisme',
        'Catastrophes naturelles',
        'Responsabilité civile vie privée',
        'Bris de glace',
        'Assistance habitation 24h/24',
        'Relogement temporaire'
      ],
      formulas: [
        {
          name: 'Basique',
          price: 'À partir de 800 DH/an',
          features: ['Risques essentiels', 'RC vie privée', 'Assistance de base']
        },
        {
          name: 'Confort',
          price: 'À partir de 1 500 DH/an',
          features: ['Tous risques', 'Vol', 'Assistance étendue', 'Relogement']
        },
        {
          name: 'Premium',
          price: 'À partir de 2 500 DH/an',
          features: ['Couverture maximale', 'Objets de valeur', 'Jardin', 'Piscine']
        }
      ]
    },
    sante: {
      title: 'Assurance Santé',
      subtitle: 'Votre santé, notre priorité',
      description: 'Bénéficiez d\'une couverture santé complète pour vous et votre famille avec un accès aux meilleurs soins.',
      features: [
        'Consultations médicales',
        'Hospitalisation',
        'Pharmacie',
        'Analyses et examens',
        'Soins dentaires',
        'Optique',
        'Médecines douces',
        'Assistance rapatriement'
      ],
      formulas: [
        {
          name: 'Essentielle',
          price: 'À partir de 150 DH/mois',
          features: ['Soins de base', 'Hospitalisation', 'Pharmacie essentielle']
        },
        {
          name: 'Confort',
          price: 'À partir de 300 DH/mois',
          features: ['Soins étendus', 'Dentaire', 'Optique', 'Médecines douces']
        },
        {
          name: 'Premium',
          price: 'À partir de 500 DH/mois',
          features: ['Couverture maximale', 'Cliniques privées', 'Soins à l\'étranger']
        }
      ]
    },
    prevoyance: {
      title: 'Prévoyance',
      subtitle: 'Protégez votre famille et votre avenir',
      description: 'Assurez la sécurité financière de vos proches en cas d\'imprévu avec nos solutions de prévoyance.',
      features: [
        'Capital décès',
        'Rente éducation',
        'Incapacité temporaire',
        'Invalidité permanente',
        'Rente de conjoint',
        'Obsèques',
        'Assistance famille',
        'Soutien psychologique'
      ],
      formulas: [
        {
          name: 'Protection Famille',
          price: 'À partir de 200 DH/mois',
          features: ['Capital décès', 'Rente éducation', 'Assistance famille']
        },
        {
          name: 'Sécurité Plus',
          price: 'À partir de 400 DH/mois',
          features: ['Couverture étendue', 'Incapacité', 'Invalidité', 'Obsèques']
        },
        {
          name: 'Protection Totale',
          price: 'À partir de 600 DH/mois',
          features: ['Garanties maximales', 'Rente de conjoint', 'Soutien psychologique']
        }
      ]
    },
    epargne: {
      title: 'Épargne Retraite',
      subtitle: 'Préparez sereinement votre retraite',
      description: 'Constituez un capital ou une rente pour maintenir votre niveau de vie à la retraite.',
      features: [
        'Versements libres ou programmés',
        'Gestion pilotée ou libre',
        'Avantages fiscaux',
        'Sortie en capital ou rente',
        'Garantie plancher',
        'Participation aux bénéfices',
        'Rachat partiel possible',
        'Transmission du capital'
      ],
      formulas: [
        {
          name: 'Épargne Liberté',
          price: 'À partir de 500 DH/mois',
          features: ['Versements libres', 'Gestion pilotée', 'Garantie plancher']
        },
        {
          name: 'Retraite Confort',
          price: 'À partir de 1 000 DH/mois',
          features: ['Gestion libre', 'Avantages fiscaux', 'Participation bénéfices']
        },
        {
          name: 'Patrimoine Plus',
          price: 'À partir de 2 000 DH/mois',
          features: ['Gestion premium', 'Transmission optimisée', 'Conseil dédié']
        }
      ]
    },
    pro: {
      title: 'Assurance Professionnelle',
      subtitle: 'Solutions sur mesure pour votre entreprise',
      description: 'Protégez votre activité professionnelle avec nos assurances adaptées à tous les secteurs.',
      features: [
        'Responsabilité civile professionnelle',
        'Multirisque professionnelle',
        'Perte d\'exploitation',
        'Cyber-risques',
        'Flotte automobile',
        'Homme clé',
        'Protection juridique',
        'Assistance entreprise'
      ],
      formulas: [
        {
          name: 'TPE/PME',
          price: 'À partir de 100 DH/mois',
          features: ['RC professionnelle', 'Multirisque de base', 'Protection juridique']
        },
        {
          name: 'Entreprise',
          price: 'À partir de 500 DH/mois',
          features: ['Couverture étendue', 'Perte d\'exploitation', 'Cyber-risques']
        },
        {
          name: 'Corporate',
          price: 'Sur devis',
          features: ['Solutions sur mesure', 'Accompagnement dédié', 'Gestion des risques']
        }
      ]
    }
  };

  const currentProduct = products[activeCategory as keyof typeof products];

  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-5xl font-bold text-axa-blue mb-6">
            Nos Solutions d'Assurance
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez notre gamme complète de produits d'assurance AXA, conçus pour répondre à tous vos besoins de protection.
          </p>
        </motion.div>

        {/* Category Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-axa-blue text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
              }`}
            >
              <category.icon className="h-5 w-5" />
              <span>{category.name}</span>
            </button>
          ))}
        </div>

        {/* Product Details */}
        <motion.div
          key={activeCategory}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16"
        >
          <div>
            <h2 className="text-3xl font-bold text-axa-blue mb-4">
              {currentProduct.title}
            </h2>
            <p className="text-xl text-axa-red mb-6">
              {currentProduct.subtitle}
            </p>
            <p className="text-gray-600 mb-8 leading-relaxed">
              {currentProduct.description}
            </p>

            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Garanties incluses :
              </h3>
              {currentProduct.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Nos Formules
            </h3>
            {currentProduct.formulas.map((formula, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="flex justify-between items-start mb-4">
                  <h4 className="text-xl font-semibold text-axa-blue">
                    {formula.name}
                  </h4>
                  <span className="text-lg font-bold text-axa-red">
                    {formula.price}
                  </span>
                </div>
                <ul className="space-y-2">
                  {formula.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-axa-gray rounded-2xl p-8 text-center"
        >
          <h3 className="text-2xl font-bold text-axa-blue mb-6">
            Intéressé par {currentProduct.title} ?
          </h3>
          <p className="text-gray-600 mb-8">
            Obtenez votre devis personnalisé ou contactez nos experts pour plus d'informations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/devis"
              className="bg-axa-red hover:bg-red-600 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 inline-flex items-center justify-center group"
            >
              <Calculator className="mr-2 h-5 w-5" />
              Calculer mon devis
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link
              to="/contact"
              className="bg-axa-blue hover:bg-blue-800 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 inline-flex items-center justify-center"
            >
              <Phone className="mr-2 h-5 w-5" />
              Parler à un expert
            </Link>
            <button className="border-2 border-axa-blue text-axa-blue hover:bg-axa-blue hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 inline-flex items-center justify-center">
              <FileText className="mr-2 h-5 w-5" />
              Documentation
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};