import React from 'react';
import {
  Sparklines,
  SparklinesLine,
  SparklinesSpots
} from 'react-sparklines';
import {
  Brain,
  TrendingUp,
  AlertCircle,
  Target,
  BarChart2,
  DollarSign,
  Users,
  Calendar
} from 'lucide-react';

interface InsightCard {
  title: string;
  value: string | number;
  trend: number;
  sparklineData: number[];
  icon: JSX.Element;
  color: string;
  prediction?: {
    value: number;
    confidence: number;
  };
}

interface AIMetricsProps {
  insights: InsightCard[];
  recommendations: {
    title: string;
    description: string;
    impact: number;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
  }[];
  anomalies: {
    metric: string;
    description: string;
    severity: 'HIGH' | 'MEDIUM' | 'LOW';
    suggestedAction: string;
  }[];
}

const AIMetricsDashboard: React.FC<AIMetricsProps> = ({
  insights,
  recommendations,
  anomalies
}) => {
  const getPriorityColor = (priority: 'HIGH' | 'MEDIUM' | 'LOW') => {
    switch (priority) {
      case 'HIGH':
        return 'text-red-600 bg-red-100';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100';
      case 'LOW':
        return 'text-green-600 bg-green-100';
    }
  };

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      {/* En-tête */}
      <div className="md:flex md:items-center md:justify-between mb-8">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate flex items-center">
            <Brain className="h-8 w-8 mr-3 text-axa-blue" />
            Intelligence Artificielle - Tableau de bord
          </h2>
        </div>
      </div>

      {/* Grille des métriques */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {insights.map((insight, index) => (
          <div
            key={index}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {insight.icon}
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {insight.title}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {insight.value}
                      </div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          insight.trend > 0 ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        <TrendingUp
                          className={`self-center flex-shrink-0 h-5 w-5 ${
                            insight.trend > 0 ? 'text-green-500' : 'text-red-500'
                          }`}
                        />
                        <span className="sr-only">
                          {insight.trend > 0 ? 'Augmentation' : 'Diminution'}
                        </span>
                        {Math.abs(insight.trend)}%
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-5 py-3">
              <div className="h-16">
                <Sparklines data={insight.sparklineData} margin={6}>
                  <SparklinesLine
                    style={{
                      stroke: insight.color,
                      fill: 'none',
                      strokeWidth: 3
                    }}
                  />
                  <SparklinesSpots
                    size={4}
                    style={{
                      stroke: insight.color,
                      strokeWidth: 3,
                      fill: 'white'
                    }}
                  />
                </Sparklines>
              </div>
              {insight.prediction && (
                <div className="mt-2 text-xs text-gray-500">
                  Prédiction: {insight.prediction.value}
                  <span className="ml-1 text-gray-400">
                    ({insight.prediction.confidence}% confiance)
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Recommandations */}
      <div className="bg-white shadow sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <Target className="h-5 w-5 mr-2 text-axa-blue" />
            Recommandations IA
          </h3>
          <div className="mt-5 space-y-4">
            {recommendations.map((recommendation, index) => (
              <div
                key={index}
                className="rounded-lg bg-gray-50 p-4 border border-gray-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {recommendation.title}
                    </h4>
                    <p className="mt-1 text-sm text-gray-500">
                      {recommendation.description}
                    </p>
                  </div>
                  <div className="ml-4">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(
                        recommendation.priority
                      )}`}
                    >
                      Impact: {recommendation.impact}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Anomalies */}
      {anomalies.length > 0 && (
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
              Anomalies Détectées
            </h3>
            <div className="mt-5 space-y-4">
              {anomalies.map((anomaly, index) => (
                <div
                  key={index}
                  className="rounded-lg bg-red-50 p-4 border border-red-200"
                >
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        {anomaly.metric}
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{anomaly.description}</p>
                      </div>
                      <div className="mt-3">
                        <div className="-mx-2 -my-1.5 flex">
                          <button
                            type="button"
                            className="px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            {anomaly.suggestedAction}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIMetricsDashboard;
