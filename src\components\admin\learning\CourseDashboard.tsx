import React, { useState } from 'react';
import {
  Book,
  PlayCircle,
  CheckCircle,
  Clock,
  Award,
  BarChart,
  FileText,
  Users
} from 'lucide-react';
import type { Course } from '../../../types/admin';

interface CourseDashboardProps {
  course: Course;
  onModuleComplete?: (moduleId: string) => void;
  onCertificationRequest?: () => void;
}

export const CourseDashboard: React.FC<CourseDashboardProps> = ({
  course,
  onModuleComplete,
  onCertificationRequest
}) => {
  const [activeModule, setActiveModule] = useState(0);

  const totalProgress = course.progress?.completed || 0;
  const canRequestCertification = course.certification?.available &&
    totalProgress >= (course.certification.requirements.minScore || 0) &&
    course.certification.requirements.requiredModules.every(moduleId =>
      course.modules.find(m => m.id === moduleId)?.progress?.completed
    );

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      {/* En-tête du cours */}
      <div className="bg-white shadow sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:p-6">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate flex items-center">
                <Book className="h-8 w-8 mr-3 text-axa-blue" />
                {course.title}
              </h2>
              <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <Clock className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                  {course.duration} minutes
                </div>
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <Users className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                  Niveau {course.level.toLowerCase()}
                </div>
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <BarChart className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                  {totalProgress}% complété
                </div>
              </div>
            </div>
          </div>

          {/* Barre de progression globale */}
          <div className="mt-6">
            <div className="relative pt-1">
              <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                <div
                  style={{ width: `${totalProgress}%` }}
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-axa-blue"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="lg:grid lg:grid-cols-12 lg:gap-x-5">
        {/* Liste des modules */}
        <aside className="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
          <nav className="space-y-1">
            {course.modules.map((module, index) => (
              <button
                key={module.id}
                onClick={() => setActiveModule(index)}
                className={`${
                  activeModule === index
                    ? 'bg-gray-50 border-axa-blue text-axa-blue'
                    : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                } group w-full flex items-center px-3 py-2 text-sm font-medium border-l-4`}
              >
                <span className="truncate">
                  {module.progress?.completed ? (
                    <CheckCircle className="mr-3 h-5 w-5 text-green-500" />
                  ) : (
                    <PlayCircle className="mr-3 h-5 w-5 text-gray-400" />
                  )}
                  {module.title}
                </span>
              </button>
            ))}
          </nav>
        </aside>

        {/* Contenu du module */}
        <div className="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                {course.modules[activeModule].title}
              </h3>
              <div className="mt-2 max-w-xl text-sm text-gray-500">
                <p>{course.modules[activeModule].description}</p>
              </div>

              {/* Contenu du module */}
              <div className="mt-5 space-y-6">
                {course.modules[activeModule].content.map((content, index) => (
                  <div key={index} className="border-t border-gray-200 pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {content.type === 'VIDEO' && (
                          <PlayCircle className="h-5 w-5 text-gray-400 mr-2" />
                        )}
                        {content.type === 'TEXT' && (
                          <FileText className="h-5 w-5 text-gray-400 mr-2" />
                        )}
                        {content.type === 'QUIZ' && (
                          <Award className="h-5 w-5 text-gray-400 mr-2" />
                        )}
                        <span className="text-sm font-medium text-gray-900">
                          {content.type}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {content.duration} min
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Actions du module */}
              <div className="mt-6 flex justify-between items-center">
                <button
                  type="button"
                  onClick={() => setActiveModule(prev => Math.max(0, prev - 1))}
                  disabled={activeModule === 0}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue disabled:opacity-50"
                >
                  Module précédent
                </button>
                {activeModule === course.modules.length - 1 ? (
                  canRequestCertification && (
                    <button
                      type="button"
                      onClick={() => onCertificationRequest?.()}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                    >
                      <Award className="h-5 w-5 mr-2" />
                      Demander la certification
                    </button>
                  )
                ) : (
                  <button
                    type="button"
                    onClick={() => {
                      onModuleComplete?.(course.modules[activeModule].id);
                      setActiveModule(prev => prev + 1);
                    }}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                  >
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Terminer et continuer
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDashboard;
