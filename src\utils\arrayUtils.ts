// Utilitaires pour s'assurer que les données sont toujours des tableaux

/**
 * S'assure qu'une valeur est un tableau
 * @param value - La valeur à vérifier
 * @param fallback - Valeur de fallback si ce n'est pas un tableau
 * @returns Un tableau valide
 */
export const ensureArray = <T>(value: any, fallback: T[] = []): T[] => {
  if (Array.isArray(value)) {
    return value;
  }
  
  console.warn('Valeur non-tableau détectée:', value, 'Utilisation du fallback:', fallback);
  return fallback;
};

/**
 * Nettoie les données localStorage corrompues
 * @param key - Clé localStorage
 * @param fallback - Valeur de fallback
 * @returns Données nettoyées
 */
export const cleanLocalStorageArray = <T>(key: string, fallback: T[] = []): T[] => {
  try {
    const stored = localStorage.getItem(key);
    if (!stored) {
      return fallback;
    }
    
    const parsed = JSON.parse(stored);
    if (Array.isArray(parsed)) {
      return parsed;
    }
    
    // Si ce n'est pas un tableau, nettoyer localStorage
    console.warn(`Données corrompues dans localStorage pour la clé "${key}". Nettoyage...`);
    localStorage.removeItem(key);
    return fallback;
  } catch (error) {
    console.error(`Erreur lors de la lecture de localStorage pour la clé "${key}":`, error);
    localStorage.removeItem(key);
    return fallback;
  }
};

/**
 * Sauvegarde sécurisée dans localStorage
 * @param key - Clé localStorage
 * @param data - Données à sauvegarder
 */
export const safeLocalStorageSet = <T>(key: string, data: T[]): void => {
  try {
    if (!Array.isArray(data)) {
      console.error(`Tentative de sauvegarde de données non-tableau pour la clé "${key}":`, data);
      return;
    }
    
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Erreur lors de la sauvegarde dans localStorage pour la clé "${key}":`, error);
  }
};

/**
 * Hook personnalisé pour gérer les tableaux dans localStorage
 * @param key - Clé localStorage
 * @param initialValue - Valeur initiale
 * @returns [data, setData]
 */
export const useLocalStorageArray = <T>(key: string, initialValue: T[] = []) => {
  const [data, setDataState] = React.useState<T[]>(() => 
    cleanLocalStorageArray(key, initialValue)
  );
  
  const setData = React.useCallback((newData: T[] | ((prev: T[]) => T[])) => {
    const updatedData = typeof newData === 'function' 
      ? newData(data) 
      : newData;
    
    const arrayData = ensureArray(updatedData, initialValue);
    setDataState(arrayData);
    safeLocalStorageSet(key, arrayData);
  }, [key, data, initialValue]);
  
  return [data, setData] as const;
};

/**
 * Valide et nettoie les données d'API
 * @param response - Réponse API
 * @param dataKey - Clé des données dans la réponse
 * @param fallback - Valeur de fallback
 * @returns Données nettoyées
 */
export const cleanAPIResponse = <T>(
  response: any, 
  dataKey: string = 'data', 
  fallback: T[] = []
): T[] => {
  if (!response || !response.success) {
    return fallback;
  }
  
  const data = response[dataKey];
  
  // Si data est directement un tableau
  if (Array.isArray(data)) {
    return data;
  }
  
  // Si data est un objet avec une propriété tableau
  if (data && typeof data === 'object') {
    // Chercher la première propriété qui est un tableau
    for (const key in data) {
      if (Array.isArray(data[key])) {
        return data[key];
      }
    }
  }
  
  console.warn('Réponse API ne contient pas de tableau valide:', response);
  return fallback;
};

/**
 * Middleware pour protéger les opérations map/filter/reduce
 * @param array - Tableau à protéger
 * @param fallback - Valeur de fallback
 * @returns Proxy protégé
 */
export const safeArray = <T>(array: any, fallback: T[] = []): T[] => {
  const safeArr = ensureArray(array, fallback);
  
  return new Proxy(safeArr, {
    get(target, prop) {
      // Protéger les méthodes de tableau
      if (prop === 'map' || prop === 'filter' || prop === 'reduce' || prop === 'forEach') {
        return function(...args: any[]) {
          if (!Array.isArray(target)) {
            console.warn(`Tentative d'utilisation de ${String(prop)} sur un non-tableau:`, target);
            return fallback;
          }
          return (target as any)[prop](...args);
        };
      }
      
      return (target as any)[prop];
    }
  });
};

// Import React pour le hook
import React from 'react';
