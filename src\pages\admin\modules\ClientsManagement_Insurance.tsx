import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Shield,
  Car,
  Home,
  Heart,
  Building,
  DollarSign,
  Clock,
  Star,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  MoreHorizontal,
  Search,
  Filter,
  Plus,
  FileText,
  Activity,
  Award,
  Target,
  Briefcase,
  CreditCard,
  RefreshCw,
  Download,
  Upload,
  Bell,
  Settings,
  History,
  Calculator,
  PieChart,
  BarChart3
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface InsuranceClient {
  id: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: 'M' | 'F';
    nationality: string;
    maritalStatus: 'SINGLE' | 'MARRIED' | 'DIVORCED' | 'WIDOWED';
    children: number;
    profession: string;
    employer?: string;
    income?: number;
  };
  contactInfo: {
    email: string;
    phone: string;
    mobile?: string;
    address: {
      street: string;
      city: string;
      postalCode: string;
      region: string;
      country: string;
    };
    preferredContact: 'EMAIL' | 'PHONE' | 'SMS' | 'MAIL';
  };
  clientStatus: 'PROSPECT' | 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'VIP';
  clientType: 'INDIVIDUAL' | 'PROFESSIONAL' | 'CORPORATE';
  registrationDate: string;
  lastActivityDate: string;
  assignedAgent: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  policies: Array<{
    id: string;
    policyNumber: string;
    productType: 'AUTO' | 'HOME' | 'HEALTH' | 'LIFE' | 'BUSINESS' | 'TRAVEL';
    productName: string;
    status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED';
    startDate: string;
    endDate: string;
    premium: number;
    paymentFrequency: 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL';
    coverage: {
      amount: number;
      deductible: number;
      details: string[];
    };
    lastPaymentDate?: string;
    nextPaymentDate?: string;
    renewalDate: string;
    autoRenewal: boolean;
  }>;
  claims: Array<{
    id: string;
    policyId: string;
    incidentDate: string;
    reportDate: string;
    status: 'OPEN' | 'INVESTIGATING' | 'APPROVED' | 'PAID' | 'REJECTED' | 'CLOSED';
    amount: number;
    paidAmount?: number;
    description: string;
  }>;
  payments: Array<{
    id: string;
    policyId: string;
    amount: number;
    date: string;
    method: 'CASH' | 'CHECK' | 'BANK_TRANSFER' | 'CARD' | 'DIRECT_DEBIT';
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
    reference: string;
  }>;
  interactions: Array<{
    id: string;
    date: string;
    type: 'CALL' | 'EMAIL' | 'MEETING' | 'SMS' | 'CLAIM' | 'PAYMENT' | 'RENEWAL';
    description: string;
    agent: string;
    outcome?: string;
  }>;
  riskProfile: {
    score: number; // 1-100
    category: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
    factors: string[];
    lastAssessment: string;
  };
  financialInfo: {
    totalPremiums: number;
    totalClaims: number;
    claimsRatio: number;
    lifetimeValue: number;
    creditScore?: number;
    paymentHistory: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  };
  preferences: {
    language: 'FR' | 'AR' | 'EN';
    communicationFrequency: 'HIGH' | 'MEDIUM' | 'LOW';
    marketingConsent: boolean;
    digitalPreference: boolean;
  };
  documents: Array<{
    id: string;
    name: string;
    type: 'ID' | 'PASSPORT' | 'DRIVING_LICENSE' | 'PROOF_OF_ADDRESS' | 'INCOME_PROOF' | 'OTHER';
    url: string;
    uploadDate: string;
    expiryDate?: string;
    verified: boolean;
  }>;
  tags: string[];
  notes: string;
  createdAt: string;
  updatedAt: string;
}

export const ClientsManagement_Insurance: React.FC = () => {
  const [clients, setClients] = useState<InsuranceClient[]>([]);
  const [selectedClient, setSelectedClient] = useState<InsuranceClient | null>(null);
  const [showClientDetails, setShowClientDetails] = useState(false);
  const [showNewClientModal, setShowNewClientModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [agentFilter, setAgentFilter] = useState('ALL');
  const [riskFilter, setRiskFilter] = useState('ALL');
  const [toast, setToast] = useState<any>(null);

  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    try {
      setIsLoading(true);
      const response = await api.getClients();
      if (response.success) {
        setClients(response.data?.clients || []);
      } else {
        // Données mock spécifiques à l'assurance
        setClients([
          {
            id: 'CLIENT-2024-001',
            personalInfo: {
              firstName: 'Mohammed',
              lastName: 'ALAMI',
              dateOfBirth: '1980-05-15',
              gender: 'M',
              nationality: 'Marocaine',
              maritalStatus: 'MARRIED',
              children: 2,
              profession: 'Ingénieur',
              employer: 'OCP Group',
              income: 18000
            },
            contactInfo: {
              email: '<EMAIL>',
              phone: '+212 5XX-XXXXXX',
              mobile: '+212 6XX-XXXXXX',
              address: {
                street: '123 Avenue Hassan II',
                city: 'Casablanca',
                postalCode: '20000',
                region: 'Grand Casablanca',
                country: 'Maroc'
              },
              preferredContact: 'EMAIL'
            },
            clientStatus: 'ACTIVE',
            clientType: 'INDIVIDUAL',
            registrationDate: '2020-03-15',
            lastActivityDate: '2024-01-10',
            assignedAgent: {
              id: 'AGENT-001',
              name: 'Fatima MOUMEN',
              email: '<EMAIL>',
              phone: '+212 5XX-XXXXXX'
            },
            policies: [
              {
                id: 'POL-001',
                policyNumber: 'AUTO-2023-001234',
                productType: 'AUTO',
                productName: 'Assurance Auto Tous Risques',
                status: 'ACTIVE',
                startDate: '2023-03-15',
                endDate: '2024-03-15',
                premium: 3600,
                paymentFrequency: 'ANNUAL',
                coverage: {
                  amount: 500000,
                  deductible: 2000,
                  details: ['RC', 'Tous risques', 'Assistance', 'Protection juridique']
                },
                lastPaymentDate: '2023-03-15',
                nextPaymentDate: '2024-03-15',
                renewalDate: '2024-03-15',
                autoRenewal: true
              },
              {
                id: 'POL-002',
                policyNumber: 'HOME-2023-001234',
                productType: 'HOME',
                productName: 'Assurance Habitation Multirisque',
                status: 'ACTIVE',
                startDate: '2023-06-01',
                endDate: '2024-06-01',
                premium: 1800,
                paymentFrequency: 'ANNUAL',
                coverage: {
                  amount: 300000,
                  deductible: 1000,
                  details: ['Incendie', 'Dégâts des eaux', 'Vol', 'RC']
                },
                lastPaymentDate: '2023-06-01',
                nextPaymentDate: '2024-06-01',
                renewalDate: '2024-06-01',
                autoRenewal: true
              }
            ],
            claims: [
              {
                id: 'CLAIM-001',
                policyId: 'POL-001',
                incidentDate: '2023-12-10',
                reportDate: '2023-12-11',
                status: 'PAID',
                amount: 8500,
                paidAmount: 6500,
                description: 'Collision avec un autre véhicule'
              }
            ],
            payments: [
              {
                id: 'PAY-001',
                policyId: 'POL-001',
                amount: 3600,
                date: '2023-03-15',
                method: 'BANK_TRANSFER',
                status: 'COMPLETED',
                reference: 'TRF-2023-001234'
              },
              {
                id: 'PAY-002',
                policyId: 'POL-002',
                amount: 1800,
                date: '2023-06-01',
                method: 'DIRECT_DEBIT',
                status: 'COMPLETED',
                reference: 'DD-2023-001234'
              }
            ],
            interactions: [
              {
                id: 'INT-001',
                date: '2024-01-10',
                type: 'CALL',
                description: 'Demande d\'information sur renouvellement',
                agent: 'Fatima MOUMEN',
                outcome: 'Renouvellement confirmé'
              }
            ],
            riskProfile: {
              score: 25,
              category: 'LOW',
              factors: ['Bon historique de paiement', 'Aucun sinistre majeur', 'Profil stable'],
              lastAssessment: '2024-01-01'
            },
            financialInfo: {
              totalPremiums: 5400,
              totalClaims: 6500,
              claimsRatio: 1.2,
              lifetimeValue: 21600,
              creditScore: 750,
              paymentHistory: 'EXCELLENT'
            },
            preferences: {
              language: 'FR',
              communicationFrequency: 'MEDIUM',
              marketingConsent: true,
              digitalPreference: true
            },
            documents: [
              {
                id: 'DOC-001',
                name: 'Carte d\'identité',
                type: 'ID',
                url: '/documents/id-card.pdf',
                uploadDate: '2020-03-15',
                expiryDate: '2030-03-15',
                verified: true
              },
              {
                id: 'DOC-002',
                name: 'Permis de conduire',
                type: 'DRIVING_LICENSE',
                url: '/documents/driving-license.pdf',
                uploadDate: '2020-03-15',
                expiryDate: '2025-03-15',
                verified: true
              }
            ],
            tags: ['Client fidèle', 'Multi-produits', 'Bon payeur'],
            notes: 'Client exemplaire depuis 4 ans. Très satisfait des services.',
            createdAt: '2020-03-15T10:00:00Z',
            updatedAt: '2024-01-10T15:30:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
      showToast('Erreur lors du chargement des clients', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PROSPECT': return 'bg-blue-100 text-blue-800';
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800';
      case 'SUSPENDED': return 'bg-red-100 text-red-800';
      case 'VIP': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (category: string) => {
    switch (category) {
      case 'LOW': return 'bg-green-100 text-green-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'VERY_HIGH': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentHistoryColor = (history: string) => {
    switch (history) {
      case 'EXCELLENT': return 'text-green-600';
      case 'GOOD': return 'text-blue-600';
      case 'FAIR': return 'text-yellow-600';
      case 'POOR': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'PROSPECT': 'Prospect',
      'ACTIVE': 'Actif',
      'INACTIVE': 'Inactif',
      'SUSPENDED': 'Suspendu',
      'VIP': 'VIP'
    };
    return statusMap[status] || status;
  };

  const getTypeText = (type: string) => {
    const typeMap = {
      'INDIVIDUAL': 'Particulier',
      'PROFESSIONAL': 'Professionnel',
      'CORPORATE': 'Entreprise'
    };
    return typeMap[type] || type;
  };

  const filteredClients = Array.isArray(clients) ? clients.filter(client => {
    const matchesSearch = client.personalInfo.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.personalInfo.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactInfo.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactInfo.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'ALL' || client.clientStatus === statusFilter;
    const matchesType = typeFilter === 'ALL' || client.clientType === typeFilter;
    const matchesAgent = agentFilter === 'ALL' || client.assignedAgent.id === agentFilter;
    const matchesRisk = riskFilter === 'ALL' || client.riskProfile.category === riskFilter;
    
    return matchesSearch && matchesStatus && matchesType && matchesAgent && matchesRisk;
  }) : [];

  const clientsStats = {
    total: clients.length,
    active: clients.filter(c => c.clientStatus === 'ACTIVE').length,
    vip: clients.filter(c => c.clientStatus === 'VIP').length,
    totalPremiums: clients.reduce((sum, c) => sum + c.financialInfo.totalPremiums, 0),
    totalClaims: clients.reduce((sum, c) => sum + c.financialInfo.totalClaims, 0),
    avgLifetimeValue: clients.length > 0 ? Math.round(clients.reduce((sum, c) => sum + c.financialInfo.lifetimeValue, 0) / clients.length) : 0,
    retentionRate: 95.2, // Mock data
    satisfactionScore: 4.6 // Mock data
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Clients</h1>
          <p className="text-gray-600">Portefeuille client et relation client</p>
        </div>
        
        <button
          onClick={() => setShowNewClientModal(true)}
          className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau Client
        </button>
      </div>

      {/* Statistiques du portefeuille client */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-bold text-gray-900">{clientsStats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Actifs</p>
              <p className="text-lg font-bold text-gray-900">{clientsStats.active}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">VIP</p>
              <p className="text-lg font-bold text-gray-900">{clientsStats.vip}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Primes</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(clientsStats.totalPremiums)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Sinistres</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(clientsStats.totalClaims)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">LTV Moy.</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(clientsStats.avgLifetimeValue)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Target className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Rétention</p>
              <p className="text-lg font-bold text-gray-900">{clientsStats.retentionRate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Star className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Satisfaction</p>
              <p className="text-lg font-bold text-gray-900">{clientsStats.satisfactionScore}/5</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les statuts</option>
            <option value="PROSPECT">Prospect</option>
            <option value="ACTIVE">Actif</option>
            <option value="INACTIVE">Inactif</option>
            <option value="SUSPENDED">Suspendu</option>
            <option value="VIP">VIP</option>
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les types</option>
            <option value="INDIVIDUAL">Particulier</option>
            <option value="PROFESSIONAL">Professionnel</option>
            <option value="CORPORATE">Entreprise</option>
          </select>

          <select
            value={riskFilter}
            onChange={(e) => setRiskFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les risques</option>
            <option value="LOW">Faible</option>
            <option value="MEDIUM">Moyen</option>
            <option value="HIGH">Élevé</option>
            <option value="VERY_HIGH">Très élevé</option>
          </select>

          <select
            value={agentFilter}
            onChange={(e) => setAgentFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les agents</option>
            <option value="AGENT-001">Fatima MOUMEN</option>
            <option value="AGENT-002">Hassan IDRISSI</option>
            <option value="AGENT-003">Aicha BENNANI</option>
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
            <Filter className="h-4 w-4 mr-2" />
            Filtres avancés
          </button>
        </div>
      </div>

      {/* Liste des clients avec tableau enrichi */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Polices
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risque
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClients.map((client) => (
                <motion.tr
                  key={client.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedClient(client);
                    setShowClientDetails(true);
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-axa-blue flex items-center justify-center">
                          <span className="text-white font-medium">
                            {client.personalInfo.firstName.charAt(0)}{client.personalInfo.lastName.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {client.personalInfo.firstName} {client.personalInfo.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{client.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{client.contactInfo.email}</div>
                    <div className="text-sm text-gray-500">{client.contactInfo.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-1">
                      {client.policies.slice(0, 3).map((policy, index) => {
                        const Icon = policy.productType === 'AUTO' ? Car :
                                   policy.productType === 'HOME' ? Home :
                                   policy.productType === 'HEALTH' ? Heart :
                                   policy.productType === 'LIFE' ? Shield :
                                   policy.productType === 'BUSINESS' ? Building : FileText;
                        return (
                          <div key={index} className="flex items-center space-x-1">
                            <Icon className="h-4 w-4 text-gray-600" />
                            <span className="text-xs text-gray-600">{policy.productType}</span>
                          </div>
                        );
                      })}
                      {client.policies.length > 3 && (
                        <span className="text-xs text-gray-500">+{client.policies.length - 3}</span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {client.policies.length} police{client.policies.length > 1 ? 's' : ''}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(client.clientStatus)}`}>
                      {getStatusText(client.clientStatus)}
                    </span>
                    <div className="text-xs text-gray-500 mt-1">{getTypeText(client.clientType)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskColor(client.riskProfile.category)}`}>
                      {client.riskProfile.category}
                    </span>
                    <div className="text-xs text-gray-500 mt-1">Score: {client.riskProfile.score}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{formatCurrency(client.financialInfo.lifetimeValue)}</div>
                      <div className="text-xs text-gray-500">
                        Primes: {formatCurrency(client.financialInfo.totalPremiums)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{client.assignedAgent.name}</div>
                      <div className="text-xs text-gray-500">{client.assignedAgent.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedClient(client);
                          setShowClientDetails(true);
                        }}
                        className="text-axa-blue hover:text-blue-800"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Logique d'édition
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Menu d'actions
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredClients.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Aucun client trouvé</p>
          </div>
        )}
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default ClientsManagement_Insurance;
