interface ValidationError {
  path: string;
  message: string;
}

interface ValidationResult {
  success: boolean;
  errors: ValidationError[];
}

const validateString = (value: string, field: string, min: number, max: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!value) {
    errors.push({ path: field, message: `Le champ ${field} est requis` });
  } else {
    if (value.length < min) {
      errors.push({ path: field, message: `Le champ ${field} doit contenir au moins ${min} caractères` });
    }
    if (value.length > max) {
      errors.push({ path: field, message: `Le champ ${field} ne doit pas dépasser ${max} caractères` });
    }
  }
  
  return errors;
};

export const validateContent = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  // Validation du titre
  errors.push(...validateString(data.title, 'titre', 3, 200));
  
  // Validation du slug
  errors.push(...validateString(data.slug, 'slug', 3, 200));
  
  // Validation de l'extrait
  if (data.excerpt) {
    errors.push(...validateString(data.excerpt, 'extrait', 10, 500));
  }
  
  // Validation du contenu
  errors.push(...validateString(data.content, 'contenu', 50, 50000));
  
  // Validation de la catégorie
  if (data.category) {
    errors.push(...validateString(data.category, 'catégorie', 2, 50));
  }
  
  // Validation des tags
  if (data.tags) {
    if (!Array.isArray(data.tags)) {
      errors.push({ path: 'tags', message: 'Les tags doivent être un tableau' });
    } else {
      data.tags.forEach((tag: string, index: number) => {
        if (typeof tag !== 'string') {
          errors.push({ path: `tags[${index}]`, message: 'Chaque tag doit être une chaîne de caractères' });
        } else if (tag.length < 2 || tag.length > 30) {
          errors.push({ path: `tags[${index}]`, message: 'Chaque tag doit contenir entre 2 et 30 caractères' });
        }
      });
    }
  }
  
  // Validation de l'image à la une
  if (data.featuredImage && typeof data.featuredImage !== 'string') {
    errors.push({ path: 'featuredImage', message: "L'URL de l'image à la une doit être une chaîne de caractères" });
  }
  
  return {
    success: errors.length === 0,
    errors
  };
};

export const validateMedia = (file: File): ValidationResult => {
  const errors: ValidationError[] = [];
  
  // Validation de la taille
  if (file.size > 10 * 1024 * 1024) {
    errors.push({ path: 'file', message: 'Le fichier ne doit pas dépasser 10MB' });
  }
  
  // Validation du type
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];
  
  if (!allowedTypes.includes(file.type)) {
    errors.push({ path: 'file', message: 'Format de fichier non supporté' });
  }
  
  return {
    success: errors.length === 0,
    errors
  };
};
