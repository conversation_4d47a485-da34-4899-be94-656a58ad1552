import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Shield, AlertTriangle, Info, Lock, AlertOctagon } from 'lucide-react';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useAdminLogger } from '../../hooks/useAdminLogger';
import type { AdminModule, AdminPermission } from '../../types/admin';
import { PermissionsOverlay } from './PermissionsOverlay';
import securityService from '../../services/securityService';

interface SecureRouteProps {
  children: React.ReactNode;
  module?: AdminModule;
  requiredPermissions?: AdminPermission[];
}

export const SecureAdminRoute: React.FC<SecureRouteProps> = ({ 
  children, 
  module,
  requiredPermissions
}) => {
  const location = useLocation();
  const [showPermissions, setShowPermissions] = useState(false);
  const { isLoading, error, canAccess } = useAdminPermissions(module, requiredPermissions);
  const { logSecurity, logError } = useAdminLogger(module);

  const [additionalAuthRequired, setAdditionalAuthRequired] = useState(false);
  const [securityAlert, setSecurityAlert] = useState<{
    type: 'WARNING' | 'ERROR' | 'CRITICAL';
    message: string;
  } | null>(null);

  useEffect(() => {
    const checkSecurity = async () => {
      try {
        const securityCheck = await securityService.checkRouteAccess(location.pathname);
        
        if (securityCheck.threatLevel > 0) {
          setSecurityAlert({
            type: securityCheck.threatLevel === 1 ? 'WARNING' :
                  securityCheck.threatLevel === 2 ? 'ERROR' : 'CRITICAL',
            message: securityCheck.message
          });
          
          if (securityCheck.requiresAdditionalAuth) {
            setAdditionalAuthRequired(true);
            logSecurity('Additional authentication required', {
              path: location.pathname,
              threatLevel: securityCheck.threatLevel
            });
          }
        } else {
          setSecurityAlert(null);
          setAdditionalAuthRequired(false);
        }
      } catch (error) {
        logError('Security check failed', error);
        setSecurityAlert({
          type: 'ERROR',
          message: 'Une erreur est survenue lors de la vérification de sécurité'
        });
      }
    };

    checkSecurity();
  }, [location.pathname, logError, logSecurity]);

  useEffect(() => {
    const validateSecurity = async () => {
      if (module && !canAccess(module)) {
        // Journaliser l'événement de sécurité
        logSecurity(
          module,
          {
            type: 'ACCESS_DENIED',
            requiredPermissions,
            path: location.pathname
          },
          `Tentative d'accès non autorisé au module ${module}`
        );

        // Traiter l'événement de sécurité
        const securityEvent = {
          timestamp: new Date().toISOString(),
          type: 'ACCESS_ATTEMPT',
          severity: 'MEDIUM',
          module,
          userId: localStorage.getItem('userId'),
          ip: await fetch('https://api.ipify.org?format=json').then(r => r.json()).then(data => data.ip),
          details: {
            authorized: false,
            requiredPermissions,
            path: location.pathname
          },
          metadata: {
            userAgent: navigator.userAgent,
            location: await fetch('https://ipapi.co/json/').then(r => r.json()),
            device: {
              type: /mobile/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
              os: navigator.platform,
              browser: navigator.userAgent.split(' ')[0]
            }
          }
        };

        await securityService.processSecurityEvent(securityEvent);

        // Vérifier si une authentification supplémentaire est requise
        const securityCheck = await securityService.validateAccess(
          localStorage.getItem('userId') || '',
          module,
          requiredPermissions
        );

        if (securityCheck.additionalChecksRequired) {
          setAdditionalAuthRequired(true);
          setSecurityAlert({
            type: 'WARNING',
            message: 'Une vérification de sécurité supplémentaire est requise'
          });
        } else if (!securityCheck.granted) {
          setSecurityAlert({
            type: 'ERROR',
            message: securityCheck.reason || 'Accès refusé pour des raisons de sécurité'
          });
        }
      }
    };

    validateSecurity();
  }, [module, canAccess, logSecurity, location.pathname, requiredPermissions]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  if (!module || (module && !canAccess(module))) {
    return (
      <div className="min-h-screen bg-gray-50 px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
        <div className="max-w-max mx-auto">
          <main className="sm:flex">
            <p className="text-4xl font-extrabold text-axa-blue sm:text-5xl">
              <Shield className="h-12 w-12 text-axa-blue animate-pulse" />
            </p>
            <div className="sm:ml-6">
              <div className="sm:border-l sm:border-gray-200 sm:pl-6">
                <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
                  Accès non autorisé
                </h1>
                <p className="mt-1 text-base text-gray-500">
                  {error || `Vous n'avez pas les droits nécessaires pour accéder à ${module || 'cette section'}.`}
                </p>
              </div>
              <div className="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
                <a
                  href="/admin/dashboard"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                >
                  Retour au tableau de bord
                </a>
                <a
                  href="/contact"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-axa-blue bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                >
                  Contacter le support
                </a>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-yellow-50 p-4 m-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Erreur de vérification des permissions
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {securityAlert && (
        <div className={`fixed top-4 right-4 w-96 rounded-lg shadow-lg p-4 ${
          securityAlert.type === 'WARNING' ? 'bg-yellow-50' :
          securityAlert.type === 'ERROR' ? 'bg-red-50' : 'bg-red-100'
        }`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {securityAlert.type === 'WARNING' ? (
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
              ) : securityAlert.type === 'ERROR' ? (
                <AlertOctagon className="h-5 w-5 text-red-400" />
              ) : (
                <Lock className="h-5 w-5 text-red-500" />
              )}
            </div>
            <div className="ml-3">
              <h3 className={`text-sm font-medium ${
                securityAlert.type === 'WARNING' ? 'text-yellow-800' :
                securityAlert.type === 'ERROR' ? 'text-red-800' : 'text-red-900'
              }`}>
                Alerte de sécurité
              </h3>
              <div className="mt-2 text-sm text-gray-700">
                <p>{securityAlert.message}</p>
              </div>
              {additionalAuthRequired && (
                <div className="mt-4">
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <Lock className="h-4 w-4 mr-2" />
                    Vérification supplémentaire
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {children}
      
      <div className="fixed bottom-4 right-4 space-y-2">
        <button
          onClick={() => setShowPermissions(true)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
        >
          <Info className="h-4 w-4 mr-2" />
          Voir les permissions
        </button>
      </div>

      <PermissionsOverlay
        isOpen={showPermissions}
        onClose={() => setShowPermissions(false)}
      />
    </>
  );
};

export default SecureAdminRoute;
