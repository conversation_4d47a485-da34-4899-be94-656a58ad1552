import { Lead } from '../types/admin';
import NotificationManager from './notificationManager';
import LeadAutomationService from './leadAutomationService';

interface ScoringCriteria {
  name: string;
  weight: number;
  evaluate: (lead: Lead) => number; // Retourne un score entre 0 et 100
}

interface ScoringHistory {
  leadId: string;
  date: string;
  oldScore: number;
  newScore: number;
  reason: string;
}

class LeadScoringService {
  private static instance: LeadScoringService;
  private scoringHistory: ScoringHistory[] = [];
  private notificationManager: NotificationManager;
  private readonly SCORE_CHANGE_THRESHOLD = 20; // Seuil de changement significatif
  
  private scoringCriteria: ScoringCriteria[] = [
    {
      name: 'Engagement',
      weight: 0.3,
      evaluate: (lead: Lead) => {
        let score = 0;
        // Nombre d'interactions
        if (lead.interactions?.length) {
          score += Math.min(lead.interactions.length * 10, 50);
        }
        // Réactivité (basé sur la dernière interaction)
        const lastInteraction = lead.interactions?.[lead.interactions.length - 1];
        if (lastInteraction) {
          const daysSinceLastInteraction = Math.floor(
            (Date.now() - new Date(lastInteraction.date).getTime()) / (1000 * 60 * 60 * 24)
          );
          score += Math.max(0, 50 - daysSinceLastInteraction * 5);
        }
        return score;
      }
    },
    {
      name: 'Potentiel Commercial',
      weight: 0.4,
      evaluate: (lead: Lead) => {
        let score = 0;
        // Valeur estimée
        if (lead.estimatedValue > 10000) score += 100;
        else if (lead.estimatedValue > 5000) score += 75;
        else if (lead.estimatedValue > 2000) score += 50;
        else score += 25;
        
        // Nombre de produits d'intérêt
        if (lead.interestedProducts?.length) {
          score = (score + Math.min(lead.interestedProducts.length * 20, 100)) / 2;
        }
        return score;
      }
    },
    {
      name: 'Qualification',
      weight: 0.3,
      evaluate: (lead: Lead) => {
        let score = 0;
        // Complétude des informations
        const requiredFields = ['email', 'phone', 'city'];
        const completedFields = requiredFields.filter(field => lead[field]);
        score += (completedFields.length / requiredFields.length) * 50;

        // Statut actuel
        switch (lead.status) {
          case 'QUALIFIED':
            score += 50;
            break;
          case 'CONTACTED':
            score += 30;
            break;
          case 'NEW':
            score += 10;
            break;
          default:
            break;
        }
        return score;
      }
    }
  ];

  private constructor() {}

  public static getInstance(): LeadScoringService {
    if (!LeadScoringService.instance) {
      LeadScoringService.instance = new LeadScoringService();
    }
    return LeadScoringService.instance;
  }

  public calculateScore(lead: Lead): number {
    let totalScore = 0;
    let totalWeight = 0;

    this.scoringCriteria.forEach(criteria => {
      const criteriaScore = criteria.evaluate(lead);
      totalScore += criteriaScore * criteria.weight;
      totalWeight += criteria.weight;
    });

    return Math.round(totalScore / totalWeight);
  }

  public updateLeadScore(lead: Lead): void {
    const oldScore = lead.score;
    const newScore = this.calculateScore(lead);
    
    if (Math.abs(newScore - oldScore) >= 10) {
      this.scoringHistory.push({
        leadId: lead.id,
        date: new Date().toISOString(),
        oldScore,
        newScore,
        reason: this.getScoreChangeReason(oldScore, newScore)
      });

      const scoreDelta = newScore - oldScore;
      NotificationManager.getInstance().addNotification({
        type: 'LEAD_SCORE_CHANGE',
        title: 'Changement de score significatif',
        message: `Le score de ${lead.name} est passé de ${oldScore} à ${newScore}`,
        date: new Date().toISOString(),
        priority: Math.abs(scoreDelta) >= 20 ? 'HIGH' : 'MEDIUM',
        data: { leadId: lead.id, scoreDelta }
      });

      // Déclencher les automatisations basées sur le changement de score
      LeadAutomationService.getInstance().processLeadUpdate(lead, scoreDelta);
    }
  }

  private getScoreChangeReason(oldScore: number, newScore: number): string {
    const delta = newScore - oldScore;
    if (delta > 0) {
      return 'Amélioration de l\'engagement et de la qualification';
    } else {
      return 'Baisse de l\'engagement ou risque de désintérêt';
    }
  }

  public getLeadScoreHistory(leadId: string): ScoringHistory[] {
    return this.scoringHistory.filter(history => history.leadId === leadId);
  }

  public getDetailedScoring(lead: Lead) {
    return this.scoringCriteria.map(criteria => ({
      name: criteria.name,
      score: criteria.evaluate(lead),
      weight: criteria.weight
    }));
  }
}

export default LeadScoringService;
