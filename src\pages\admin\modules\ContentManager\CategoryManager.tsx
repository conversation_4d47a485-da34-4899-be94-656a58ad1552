import React from 'react';
import { Controller } from 'react-hook-form';
import { Tag as TagIcon } from 'lucide-react';

interface CategoryManagerProps {
  control: any;
  errors: any;
}

export const CategoryManager: React.FC<CategoryManagerProps> = ({ control, errors }) => {
  const categories = [
    'Actualités',
    'Conseils',
    'Guides',
    'Témoignages',
    'Événements',
    'Produits',
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Catégorisation</h4>
      
      <Controller
        name="category"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Catégorie
            </label>
            <select
              {...field}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            >
              <option value="">Sélectionnez une catégorie</option>
              {categories.map((category) => (
                <option key={category} value={category.toLowerCase()}>
                  {category}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">
                {errors.category.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="tags"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <div className="relative">
              <input
                type="text"
                value={field.value?.join(', ') || ''}
                onChange={(e) => {
                  const tags = e.target.value
                    .split(',')
                    .map((tag) => tag.trim())
                    .filter(Boolean);
                  field.onChange(tags);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent pl-10"
                placeholder="Tags séparés par des virgules"
              />
              <TagIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Exemple: assurance, auto, conseils
            </p>
            {errors.tags && (
              <p className="mt-1 text-sm text-red-600">
                {errors.tags.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default CategoryManager;
