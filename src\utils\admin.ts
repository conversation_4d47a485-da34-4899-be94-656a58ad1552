// Utilitaires complets pour l'administration

import { ValidationError, ValidationResult } from '../types/admin';

// ===== FORMATAGE =====
export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('fr-FR');
};

export const formatDateTime = (date: string | Date): string => {
  return new Date(date).toLocaleString('fr-FR');
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'MAD'
  }).format(amount);
};

export const formatNumber = (number: number): string => {
  return new Intl.NumberFormat('fr-FR').format(number);
};

export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// ===== GÉNÉRATION =====
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

export const generateReference = (prefix: string): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${year}${month}${day}-${random}`;
};

// ===== VALIDATION =====
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+212|0)[5-7][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

export const validateRequired = (value: any): boolean => {
  if (typeof value === 'string') {
    return value.trim().length > 0;
  }
  return value !== null && value !== undefined;
};

export const validateMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

export const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

export const validateNumeric = (value: string): boolean => {
  return !isNaN(Number(value)) && isFinite(Number(value));
};

export const validatePositiveNumber = (value: number): boolean => {
  return value > 0;
};

export const validateDateRange = (startDate: string, endDate: string): boolean => {
  return new Date(startDate) <= new Date(endDate);
};

// ===== VALIDATION COMPLEXE =====
export const validateClaim = (claim: any): ValidationResult => {
  const errors: ValidationError[] = [];

  if (!validateRequired(claim.type)) {
    errors.push({ field: 'type', message: 'Le type de sinistre est requis' });
  }

  if (!validateRequired(claim.client)) {
    errors.push({ field: 'client', message: 'Le client est requis' });
  }

  if (!validateRequired(claim.description)) {
    errors.push({ field: 'description', message: 'La description est requise' });
  } else if (!validateMinLength(claim.description, 10)) {
    errors.push({ field: 'description', message: 'La description doit contenir au moins 10 caractères' });
  }

  if (claim.amount && !validatePositiveNumber(Number(claim.amount))) {
    errors.push({ field: 'amount', message: 'Le montant doit être positif' });
  }

  if (!validateRequired(claim.incidentDate)) {
    errors.push({ field: 'incidentDate', message: 'La date d\'incident est requise' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateLead = (lead: any): ValidationResult => {
  const errors: ValidationError[] = [];

  if (!validateRequired(lead.firstName)) {
    errors.push({ field: 'firstName', message: 'Le prénom est requis' });
  }

  if (!validateRequired(lead.lastName)) {
    errors.push({ field: 'lastName', message: 'Le nom est requis' });
  }

  if (!validateRequired(lead.email)) {
    errors.push({ field: 'email', message: 'L\'email est requis' });
  } else if (!validateEmail(lead.email)) {
    errors.push({ field: 'email', message: 'L\'email n\'est pas valide' });
  }

  if (!validateRequired(lead.phone)) {
    errors.push({ field: 'phone', message: 'Le téléphone est requis' });
  } else if (!validatePhone(lead.phone)) {
    errors.push({ field: 'phone', message: 'Le numéro de téléphone n\'est pas valide' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateClient = (client: any): ValidationResult => {
  const errors: ValidationError[] = [];

  if (!validateRequired(client.firstName)) {
    errors.push({ field: 'firstName', message: 'Le prénom est requis' });
  }

  if (!validateRequired(client.lastName)) {
    errors.push({ field: 'lastName', message: 'Le nom est requis' });
  }

  if (!validateRequired(client.email)) {
    errors.push({ field: 'email', message: 'L\'email est requis' });
  } else if (!validateEmail(client.email)) {
    errors.push({ field: 'email', message: 'L\'email n\'est pas valide' });
  }

  if (!validateRequired(client.phone)) {
    errors.push({ field: 'phone', message: 'Le téléphone est requis' });
  } else if (!validatePhone(client.phone)) {
    errors.push({ field: 'phone', message: 'Le numéro de téléphone n\'est pas valide' });
  }

  if (client.dateOfBirth && new Date(client.dateOfBirth) > new Date()) {
    errors.push({ field: 'dateOfBirth', message: 'La date de naissance ne peut pas être dans le futur' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
