const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'mtp_secret_key_2024';

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configuration de la base de données
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'mtp_user',
  password: process.env.DB_PASSWORD || 'mtp_password_2024!',
  database: process.env.DB_NAME || 'mtp_development',
  charset: 'utf8mb4'
};

// Configuration multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Type de fichier non autorisé'));
    }
  }
});

// Middleware d'authentification
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Token d\'accès requis' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: 'Token invalide' });
    }
    req.user = user;
    next();
  });
};

// Fonction utilitaire pour la base de données
const executeQuery = async (query, params = []) => {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('Erreur base de données:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Routes d'authentification
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Utilisateur admin par défaut
    if (email === '<EMAIL>' && password === 'Admin123!') {
      const token = jwt.sign(
        { 
          id: 1, 
          email: '<EMAIL>', 
          role: 'SUPER_ADMIN',
          name: 'Administrateur Principal'
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      return res.json({
        success: true,
        data: {
          token,
          user: {
            id: 1,
            email: '<EMAIL>',
            name: 'Administrateur Principal',
            role: 'SUPER_ADMIN'
          }
        }
      });
    }

    // Vérification en base de données
    try {
      const users = await executeQuery(
        'SELECT * FROM users WHERE email = ? AND is_active = 1',
        [email]
      );

      if (users.length === 0) {
        return res.status(401).json({
          success: false,
          message: 'Email ou mot de passe incorrect'
        });
      }

      const user = users[0];
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          message: 'Email ou mot de passe incorrect'
        });
      }

      const token = jwt.sign(
        { 
          id: user.id, 
          email: user.email, 
          role: user.role,
          name: user.name
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          }
        }
      });
    } catch (dbError) {
      console.log('Erreur DB, utilisation du mode fallback');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
  } catch (error) {
    console.error('Erreur login:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur'
    });
  }
});

// Routes pour les leads
app.get('/api/leads', authenticateToken, async (req, res) => {
  try {
    const mockLeads = [
      {
        id: '1',
        name: 'Hassan Benjelloun',
        email: '<EMAIL>',
        phone: '+212 6 11 22 33 44',
        company: 'Entreprise Benjelloun',
        source: 'Site Web',
        status: 'NOUVEAU',
        priority: 'HAUTE',
        assignedTo: 'Conseiller 1',
        estimatedValue: 5000,
        notes: 'Intéressé par assurance auto premium',
        createdAt: '2024-01-15T10:00:00Z',
        lastContact: '2024-01-15T14:30:00Z'
      },
      {
        id: '2',
        name: 'Fatima Zahra',
        email: '<EMAIL>',
        phone: '+212 6 55 44 33 22',
        company: null,
        source: 'Recommandation',
        status: 'QUALIFIE',
        priority: 'MOYENNE',
        assignedTo: 'Conseiller 2',
        estimatedValue: 3000,
        notes: 'Recherche assurance habitation',
        createdAt: '2024-01-14T15:30:00Z',
        lastContact: '2024-01-15T09:15:00Z'
      }
    ];

    res.json({
      success: true,
      data: { leads: mockLeads }
    });
  } catch (error) {
    console.error('Erreur leads:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des leads'
    });
  }
});

app.post('/api/leads', authenticateToken, async (req, res) => {
  try {
    const leadData = req.body;
    
    // Simulation de création
    const newLead = {
      id: Date.now().toString(),
      ...leadData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: { lead: newLead },
      message: 'Lead créé avec succès'
    });
  } catch (error) {
    console.error('Erreur création lead:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du lead'
    });
  }
});

// Routes pour les clients
app.get('/api/clients', authenticateToken, async (req, res) => {
  try {
    const mockClients = [
      {
        id: '1',
        name: 'Hassan Benjelloun',
        email: '<EMAIL>',
        phone: '+212 6 11 22 33 44',
        type: 'INDIVIDUAL',
        status: 'ACTIF',
        registrationDate: '2024-01-10T00:00:00Z',
        totalPremium: 2500,
        contractsCount: 2,
        claimsCount: 1,
        lastContact: '2024-01-15T14:30:00Z'
      },
      {
        id: '2',
        name: 'Entreprise ABC SARL',
        email: '<EMAIL>',
        phone: '+212 5 22 33 44 55',
        type: 'BUSINESS',
        status: 'ACTIF',
        registrationDate: '2024-01-05T00:00:00Z',
        totalPremium: 15000,
        contractsCount: 5,
        claimsCount: 2,
        lastContact: '2024-01-14T16:00:00Z'
      }
    ];

    res.json({
      success: true,
      data: { clients: mockClients }
    });
  } catch (error) {
    console.error('Erreur clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des clients'
    });
  }
});

// Routes pour les devis
app.get('/api/quotes', authenticateToken, async (req, res) => {
  try {
    const mockQuotes = [
      {
        id: '1',
        quoteNumber: 'DEV-2024-001',
        clientId: '1',
        clientName: 'Hassan Benjelloun',
        product: 'Assurance Auto',
        premium: 2500,
        status: 'ENVOYE',
        validUntil: '2024-02-15T00:00:00Z',
        createdAt: '2024-01-15T10:00:00Z',
        createdBy: 'Conseiller 1'
      },
      {
        id: '2',
        quoteNumber: 'DEV-2024-002',
        clientId: '2',
        clientName: 'Entreprise ABC SARL',
        product: 'Assurance Flotte',
        premium: 15000,
        status: 'BROUILLON',
        validUntil: '2024-02-20T00:00:00Z',
        createdAt: '2024-01-14T15:30:00Z',
        createdBy: 'Conseiller 2'
      }
    ];

    res.json({
      success: true,
      data: { quotes: mockQuotes }
    });
  } catch (error) {
    console.error('Erreur devis:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des devis'
    });
  }
});

// Routes pour les sinistres
app.get('/api/claims', authenticateToken, async (req, res) => {
  try {
    const mockClaims = [
      {
        id: '1',
        claimNumber: 'SIN-2024-001',
        clientId: '1',
        client: {
          id: '1',
          name: 'Hassan Benjelloun',
          email: '<EMAIL>',
          phone: '+212 6 11 22 33 44',
          type: 'INDIVIDUAL'
        },
        type: 'AUTO',
        category: 'Accident de la circulation',
        title: 'Collision avec un autre véhicule',
        description: 'Accident survenu au carrefour de la rue Mohammed V',
        incidentDate: '2024-01-10T15:30:00Z',
        reportedDate: '2024-01-10T16:45:00Z',
        estimatedAmount: 15000,
        approvedAmount: 12500,
        status: 'APPROVED',
        priority: 'HIGH',
        assignedTo: '2',
        createdAt: '2024-01-10T16:45:00Z',
        updatedAt: '2024-01-14T10:30:00Z'
      }
    ];

    res.json({
      success: true,
      data: { claims: mockClaims }
    });
  } catch (error) {
    console.error('Erreur sinistres:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des sinistres'
    });
  }
});

// Routes pour les analytics
app.get('/api/analytics/dashboard', authenticateToken, async (req, res) => {
  try {
    const mockStats = {
      totalLeads: 247,
      leadsGrowth: 18.5,
      totalClients: 156,
      clientsGrowth: 12.3,
      totalRevenue: 1250000,
      revenueGrowth: 25.7,
      conversionRate: 27.4,
      conversionGrowth: 8.2
    };

    res.json({
      success: true,
      data: mockStats
    });
  } catch (error) {
    console.error('Erreur analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du chargement des analytics'
    });
  }
});

// Route pour l'upload de fichiers
app.post('/api/upload', authenticateToken, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier uploadé'
      });
    }

    res.json({
      success: true,
      data: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        path: `/uploads/${req.file.filename}`
      },
      message: 'Fichier uploadé avec succès'
    });
  } catch (error) {
    console.error('Erreur upload:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'upload'
    });
  }
});

// Servir les fichiers statiques
app.use('/uploads', express.static('uploads'));

// Route de test
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API MTP fonctionnelle',
    timestamp: new Date().toISOString()
  });
});

// Gestion des erreurs
app.use((error, req, res, next) => {
  console.error('Erreur serveur:', error);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur'
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route non trouvée'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur API MTP démarré sur le port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Login endpoint: http://localhost:${PORT}/api/auth/login`);
});

module.exports = app;
