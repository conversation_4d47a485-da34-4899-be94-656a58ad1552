import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  noIndex?: boolean;
  structuredData?: object;
}

/**
 * Composant pour gérer les meta tags SEO de manière dynamique
 */
export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'MOUMEN TECHNIQUE ET PREVOYANCE - Agent Général AXA Maroc',
  description = 'Votre agent général AXA au Maroc depuis 15 ans. Assurance auto, habitation, santé, prévoyance et épargne. Devis gratuit en ligne.',
  keywords = 'assurance maroc, axa maroc, agent général axa, assurance auto maroc, assurance habitation maroc',
  image = '/logo-mtp-official.png',
  url = 'https://moumen-axa.ma',
  type = 'website',
  noIndex = false,
  structuredData
}) => {
  const fullTitle = title.includes('MOUMEN') ? title : `${title} | MOUMEN TECHNIQUE ET PREVOYANCE`;
  const fullImageUrl = image.startsWith('http') ? image : `${url}${image}`;
  const fullUrl = url.startsWith('http') ? url : `https://moumen-axa.ma${url}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? 'noindex, nofollow' : 'index, follow'} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:site_name" content="MOUMEN TECHNIQUE ET PREVOYANCE" />
      <meta property="og:locale" content="fr_MA" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

/**
 * Données structurées prédéfinies pour différents types de pages
 */
export const structuredDataTemplates = {
  homepage: {
    "@context": "https://schema.org",
    "@type": "InsuranceAgency",
    "name": "MOUMEN TECHNIQUE ET PREVOYANCE",
    "description": "Agent Général AXA au Maroc spécialisé dans l'assurance auto, habitation, santé, prévoyance et épargne",
    "url": "https://moumen-axa.ma",
    "logo": "https://moumen-axa.ma/logo-mtp-official.png",
    "telephone": "+212 5XX-XXXXXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Boulevard Mohammed V",
      "addressLocality": "Casablanca",
      "addressCountry": "MA"
    },
    "openingHours": "Mo-Fr 08:30-18:00, Sa 09:00-13:00",
    "areaServed": "Morocco",
    "serviceType": ["Assurance Auto", "Assurance Habitation", "Assurance Santé", "Prévoyance", "Épargne Retraite"],
    "foundingDate": "2008"
  },

  product: (productName: string, productDescription: string) => ({
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productName,
    "description": productDescription,
    "brand": {
      "@type": "Brand",
      "name": "AXA"
    },
    "provider": {
      "@type": "InsuranceAgency",
      "name": "MOUMEN TECHNIQUE ET PREVOYANCE"
    },
    "category": "Insurance"
  }),

  article: (title: string, description: string, publishDate: string, author: string = "MOUMEN TECHNIQUE ET PREVOYANCE") => ({
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description,
    "datePublished": publishDate,
    "author": {
      "@type": "Organization",
      "name": author
    },
    "publisher": {
      "@type": "Organization",
      "name": "MOUMEN TECHNIQUE ET PREVOYANCE",
      "logo": {
        "@type": "ImageObject",
        "url": "https://moumen-axa.ma/logo-mtp-official.png"
      }
    }
  }),

  contactPage: {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact - MOUMEN TECHNIQUE ET PREVOYANCE",
    "description": "Contactez votre agent général AXA au Maroc pour tous vos besoins d'assurance",
    "mainEntity": {
      "@type": "InsuranceAgency",
      "name": "MOUMEN TECHNIQUE ET PREVOYANCE",
      "telephone": "+212 5XX-XXXXXX",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Boulevard Mohammed V",
        "addressLocality": "Casablanca",
        "addressCountry": "MA"
      }
    }
  },

  faq: (questions: Array<{question: string, answer: string}>) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": questions.map(q => ({
      "@type": "Question",
      "name": q.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": q.answer
      }
    }))
  })
};

/**
 * Hook pour générer des meta tags SEO basés sur la route actuelle
 */
export const useSEO = (customSEO?: Partial<SEOHeadProps>) => {
  const defaultSEO: SEOHeadProps = {
    title: 'MOUMEN TECHNIQUE ET PREVOYANCE - Agent Général AXA Maroc',
    description: 'Votre agent général AXA au Maroc depuis 15 ans. Assurance auto, habitation, santé, prévoyance et épargne. Devis gratuit en ligne.',
    keywords: 'assurance maroc, axa maroc, agent général axa, assurance auto maroc, assurance habitation maroc',
    image: '/logo-mtp-official.png',
    url: 'https://moumen-axa.ma',
    type: 'website'
  };

  return {
    ...defaultSEO,
    ...customSEO
  };
};

/**
 * Composant pour les breadcrumbs SEO
 */
export const BreadcrumbSchema: React.FC<{
  items: Array<{ name: string; url: string }>
}> = ({ items }) => {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url.startsWith('http') ? item.url : `https://moumen-axa.ma${item.url}`
    }))
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </Helmet>
  );
};
