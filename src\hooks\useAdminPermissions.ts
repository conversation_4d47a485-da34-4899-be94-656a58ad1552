import { useState, useEffect, useCallback } from 'react';
import type { AdminModule, AdminPermission, AdminUser } from '../types/admin';

interface UseAdminPermissionsReturn {
  canAccess: (module: AdminModule) => boolean;
  hasPermission: (module: AdminModule, permission: AdminPermission) => boolean;
  isLoading: boolean;
  error: string | null;
  user: AdminUser | null;
  refreshPermissions: () => Promise<void>;
}

export const useAdminPermissions = (
  requiredModule?: AdminModule,
  requiredPermissions?: AdminPermission[]
): UseAdminPermissionsReturn => {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUser = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Non authentifié');
      }

      const response = await fetch('/api/admin/me', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Échec du chargement des permissions');
      }

      const userData: AdminUser = await response.json();
      setUser(userData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUser();
  }, [loadUser]);

  const canAccess = useCallback((module: AdminModule): boolean => {
    if (!user) return false;

    const moduleAccess = user.moduleAccess.find(access => access.module === module);
    if (!moduleAccess) return false;

    if (requiredPermissions?.length) {
      return requiredPermissions.every(permission =>
        moduleAccess.permissions.includes(permission)
      );
    }

    return true;
  }, [user, requiredPermissions]);

  const hasPermission = useCallback((module: AdminModule, permission: AdminPermission): boolean => {
    if (!user) return false;

    const moduleAccess = user.moduleAccess.find(access => access.module === module);
    if (!moduleAccess) return false;

    return moduleAccess.permissions.includes(permission);
  }, [user]);

  const refreshPermissions = useCallback(async () => {
    await loadUser();
  }, [loadUser]);

  // Vérifier automatiquement les permissions requises si spécifiées
  useEffect(() => {
    if (requiredModule && !isLoading && user) {
      const hasAccess = canAccess(requiredModule);
      if (!hasAccess) {
        setError(`Accès non autorisé au module ${requiredModule}`);
      }
    }
  }, [requiredModule, user, isLoading, canAccess]);

  return {
    canAccess,
    hasPermission,
    isLoading,
    error,
    user,
    refreshPermissions
  };
};

// Hook simplifié pour vérifier rapidement une permission
export const useAdminPermission = (
  module: AdminModule,
  permission: AdminPermission
): boolean => {
  const { hasPermission, isLoading } = useAdminPermissions();
  
  if (isLoading) return false;
  return hasPermission(module, permission);
};
