{
  "name": "mtp-backend",
  "version": "1.0.0",
  "description": "Backend API pour MOUMEN TECHNIQUE ET PREVOYANCE",
  "main": "dist/server.js",
  "scripts": {
    "dev": "tsx watch src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "test": "vitest",
    "lint": "eslint src/**/*.ts",
    // Removed Prisma scripts
    // "db:migrate": "prisma migrate dev",
    // "db:generate": "prisma generate",
    // "db:studio": "prisma studio",
    "db:seed": "tsx src/scripts/seed.ts"
  },
  "dependencies": {
    "@prisma/client": "^5.7.1",
    "bcryptjs": "^2.4.3",
    "compression": "^1.7.4",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "express": "^4.18.2",
    "helmet": "^7.1.0",
    "joi": "^17.11.0",
    "jsonwebtoken": "^9.0.2",
    "morgan": "^1.10.0",
    "multer": "^1.4.5-lts.1",
    "mysql2": "^3.6.5",
    "nodemailer": "^6.9.7",
    // Removed Prisma dependency
    // "@prisma/client": "^5.7.1",
    // "prisma": "^5.7.1",
    "rate-limiter-flexible": "^2.4.2",
    "winston": "^3.11.0"
  },
  "devDependencies": {
    "@types/bcryptjs": "^2.4.6",
    "@types/compression": "^1.8.1",
    "@types/cors": "^2.8.19",
    "@types/express": "^4.17.23",
    "@types/jsonwebtoken": "^9.0.10",
    "@types/morgan": "^1.9.10",
    "@types/multer": "^1.4.13",
    "@types/node": "^20.19.9",
    "@types/nodemailer": "^6.4.17",
    "@typescript-eslint/eslint-plugin": "^6.15.0",
    "@typescript-eslint/parser": "^6.15.0",
    "eslint": "^8.56.0",
    "tsx": "^4.6.2",
    "typescript": "^5.3.3",
    "vitest": "^1.1.0"
  },
  "keywords": [
    "insurance",
    "axa",
    "morocco",
    "api",
    "backend"
  ],
  "author": "MOUMEN TECHNIQUE ET PREVOYANCE",
  "license": "UNLICENSED"
}
