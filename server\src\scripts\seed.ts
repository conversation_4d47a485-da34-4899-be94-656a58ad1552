import bcrypt from 'bcryptjs';
// ...existing code...
import { logger } from '../utils/logger';

async function main() {
  logger.info('🌱 Starting database seed...');

  try {
    // Créer un utilisateur admin par défaut
    const adminPassword = await bcrypt.hash('Admin123!', 12);
    
// ...existing code...
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Administrateur',
        password: adminPassword,
        role: 'SUPER_ADMIN',
        isActive: true,
      },
    });

    logger.info(`✅ Admin user created: ${admin.email}`);

    // Créer quelques utilisateurs de test
    const userPassword = await bcrypt.hash('User123!', 12);
    
    const users = await Promise.all([
// ...existing code...
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: '<PERSON>',
          password: userPassword,
          role: 'USER',
          isActive: true,
        },
      }),
// ...existing code...
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: 'Fatima Conseillère',
          password: userPassword,
          role: 'USER',
          isActive: true,
        },
      }),
    ]);

    logger.info(`✅ ${users.length} user accounts created`);

    // Créer quelques leads de test
    const leads = await Promise.all([
// ...existing code...
        data: {
          name: 'Mohammed Alami',
          email: '<EMAIL>',
          phone: '**********',
          product: 'Assurance Auto',
          source: 'Site Web',
          city: 'Casablanca',
          message: 'Je souhaite assurer ma nouvelle voiture',
          status: 'NEW',
        },
      }),
// ...existing code...
        data: {
          name: 'Aicha Benali',
          email: '<EMAIL>',
          phone: '**********',
          product: 'Assurance Habitation',
          source: 'Facebook',
          city: 'Rabat',
          message: 'Devis pour assurance appartement',
          status: 'CONTACTED',
          assignedTo: users[0].id,
        },
      }),
// ...existing code...
        data: {
          name: 'Youssef Tazi',
          email: '<EMAIL>',
          phone: '**********',
          product: 'Assurance Santé',
          source: 'Recommandation',
          city: 'Marrakech',
          status: 'QUALIFIED',
          assignedTo: users[1].id,
        },
      }),
    ]);

    logger.info(`✅ ${leads.length} test leads created`);

    // Créer quelques clients de test
    const clients = await Promise.all([
// ...existing code...
        data: {
          name: 'Hassan Idrissi',
          email: '<EMAIL>',
          phone: '0612345679',
          type: 'INDIVIDUAL',
          city: 'Casablanca',
          address: '123 Rue Mohammed V, Casablanca',
          assignedTo: users[0].id,
        },
      }),
// ...existing code...
        data: {
          name: 'Entreprise SARL Tech',
          email: '<EMAIL>',
          phone: '0522123456',
          type: 'BUSINESS',
          city: 'Casablanca',
          address: '456 Boulevard Zerktouni, Casablanca',
          assignedTo: users[1].id,
        },
      }),
    ]);

    logger.info(`✅ ${clients.length} test clients created`);

    // Créer quelques devis de test
    const quotes = await Promise.all([
// ...existing code...
        data: {
          clientId: clients[0].id,
          product: 'Assurance Auto',
          amount: 2500,
          status: 'SENT',
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
          details: {
            vehicleType: 'Berline',
            power: '8CV',
            usage: 'Personnel',
          },
          createdBy: users[0].id,
        },
      }),
// ...existing code...
        data: {
          clientId: clients[1].id,
          product: 'Assurance Habitation',
          amount: 1800,
          status: 'ACCEPTED',
          validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 jours
          details: {
            propertyType: 'Appartement',
            surface: '120m²',
            location: 'Casablanca Centre',
          },
          createdBy: users[1].id,
        },
      }),
    ]);

    logger.info(`✅ ${quotes.length} test quotes created`);

    // Créer quelques bandeaux de test
    const banners = await Promise.all([
// ...existing code...
        data: {
          name: 'Promotion Assurance Auto',
          text: '🚗 Profitez de -20% sur votre assurance auto jusqu\'au 31 décembre !',
          type: 'PROMOTION',
          status: 'ACTIVE',
          backgroundColor: 'bg-gradient-to-r from-red-500 to-red-700',
          textColor: 'text-white',
          startDate: new Date(),
          endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 jours
          showContact: true,
        },
      }),
// ...existing code...
        data: {
          name: 'Information Santé',
          text: 'ℹ️ Nouvelle gamme d\'assurance santé disponible. Découvrez nos offres !',
          type: 'INFO',
          status: 'INACTIVE',
          backgroundColor: 'bg-gradient-to-r from-blue-500 to-blue-700',
          textColor: 'text-white',
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Dans 7 jours
          endDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 37 jours
          showContact: false,
        },
      }),
    ]);

    logger.info(`✅ ${banners.length} test banners created`);

    // Créer quelques articles de blog de test
    const blogPosts = await Promise.all([
// ...existing code...
        data: {
          title: 'Comment choisir son assurance auto au Maroc',
          slug: 'comment-choisir-assurance-auto-maroc',
          content: `
            <h2>Introduction</h2>
            <p>Choisir une assurance auto au Maroc peut sembler complexe. Voici nos conseils pour faire le bon choix.</p>
            
            <h2>Les critères importants</h2>
            <ul>
              <li>Le type de couverture</li>
              <li>Le montant de la franchise</li>
              <li>Les garanties incluses</li>
              <li>Le service client</li>
            </ul>
            
            <h2>Nos recommandations</h2>
            <p>Chez MOUMEN TECHNIQUE ET PREVOYANCE, nous vous accompagnons dans le choix de votre assurance auto...</p>
          `,
          excerpt: 'Guide complet pour choisir la meilleure assurance auto au Maroc selon vos besoins et votre budget.',
          status: 'PUBLISHED',
          publishedAt: new Date(),
        },
      }),
// ...existing code...
        data: {
          title: 'L\'importance de l\'assurance habitation',
          slug: 'importance-assurance-habitation',
          content: `
            <h2>Pourquoi assurer son logement ?</h2>
            <p>L'assurance habitation protège votre patrimoine immobilier et mobilier...</p>
          `,
          excerpt: 'Découvrez pourquoi l\'assurance habitation est essentielle pour protéger votre patrimoine.',
          status: 'DRAFT',
        },
      }),
    ]);

    logger.info(`✅ ${blogPosts.length} test blog posts created`);

    logger.info('🎉 Database seed completed successfully!');
    
    // Afficher les informations de connexion
    console.log('\n📋 Comptes créés:');
    console.log('👤 Admin: <EMAIL> / Admin123!');
    console.log('👤 Conseiller 1: <EMAIL> / User123!');
    console.log('👤 Conseiller 2: <EMAIL> / User123!');
    console.log('\n🚀 Vous pouvez maintenant vous connecter à l\'application!');

  } catch (error) {
    logger.error('❌ Error during database seed:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
// ...existing code...
  });
