import { Template } from '../types/admin';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
}

export function validateTemplate(template: Partial<Template>): ValidationResult {
  const errors: ValidationError[] = [];

  if (!template.name?.trim()) {
    errors.push({ field: 'name', message: 'Le nom du template est requis' });
  }

  if (!template.description?.trim()) {
    errors.push({ field: 'description', message: 'La description du template est requise' });
  }

  if (!template.category?.trim()) {
    errors.push({ field: 'category', message: 'La catégorie du template est requise' });
  }

  if (!template.type?.trim()) {
    errors.push({ field: 'type', message: 'Le type du template est requis' });
  }

  // Validation des champs spécifiques au type
  switch (template.type) {
    case 'auto':
      if (!template.fields?.includes('vehicule')) {
        errors.push({ field: 'fields', message: 'Le champ "vehicule" est requis pour un template auto' });
      }
      break;
    case 'habitation':
      if (!template.fields?.includes('logement')) {
        errors.push({ field: 'fields', message: 'Le champ "logement" est requis pour un template habitation' });
      }
      break;
    case 'sante':
      if (!template.fields?.includes('beneficiaires')) {
        errors.push({ field: 'fields', message: 'Le champ "beneficiaires" est requis pour un template santé' });
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validatePDFGeneration(data: any, template: Template): ValidationResult {
  const errors: ValidationError[] = [];

  // Validation des champs requis du template
  for (const field of template.fields) {
    if (!data[field]) {
      errors.push({ 
        field,
        message: `Le champ "${field}" est requis pour ce template`
      });
    }
  }

  // Validation spécifique selon le type
  switch (template.type) {
    case 'auto':
      if (data.vehicule) {
        if (!data.vehicule.marque) {
          errors.push({ field: 'vehicule.marque', message: 'La marque du véhicule est requise' });
        }
        if (!data.vehicule.modele) {
          errors.push({ field: 'vehicule.modele', message: 'Le modèle du véhicule est requis' });
        }
        if (!data.vehicule.immatriculation) {
          errors.push({ field: 'vehicule.immatriculation', message: 'L\'immatriculation est requise' });
        }
      }
      break;
    case 'habitation':
      if (data.logement) {
        if (!data.logement.type) {
          errors.push({ field: 'logement.type', message: 'Le type de logement est requis' });
        }
        if (!data.logement.surface) {
          errors.push({ field: 'logement.surface', message: 'La surface du logement est requise' });
        }
        if (!data.logement.adresse) {
          errors.push({ field: 'logement.adresse', message: 'L\'adresse du logement est requise' });
        }
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validatePDFFormat(format: any): ValidationResult {
  const errors: ValidationError[] = [];
  const validFormats = ['A4', 'A3', 'Letter', 'Legal'];
  const validOrientations = ['portrait', 'landscape'];

  if (!format.paperSize || !validFormats.includes(format.paperSize)) {
    errors.push({
      field: 'paperSize',
      message: `Le format du papier doit être l'un des suivants: ${validFormats.join(', ')}`
    });
  }

  if (!format.orientation || !validOrientations.includes(format.orientation)) {
    errors.push({
      field: 'orientation',
      message: 'L\'orientation doit être "portrait" ou "landscape"'
    });
  }

  if (format.margins) {
    if (typeof format.margins.top !== 'number' || format.margins.top < 0) {
      errors.push({ field: 'margins.top', message: 'La marge supérieure doit être un nombre positif' });
    }
    if (typeof format.margins.bottom !== 'number' || format.margins.bottom < 0) {
      errors.push({ field: 'margins.bottom', message: 'La marge inférieure doit être un nombre positif' });
    }
    if (typeof format.margins.left !== 'number' || format.margins.left < 0) {
      errors.push({ field: 'margins.left', message: 'La marge gauche doit être un nombre positif' });
    }
    if (typeof format.margins.right !== 'number' || format.margins.right < 0) {
      errors.push({ field: 'margins.right', message: 'La marge droite doit être un nombre positif' });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
