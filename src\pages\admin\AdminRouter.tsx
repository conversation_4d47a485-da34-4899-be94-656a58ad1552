import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AdminLogin } from './AdminLogin';
import { AdminDashboard_Optimized } from './AdminDashboard_Optimized';
import { ProtectedRoute } from '../../components/auth/ProtectedRoute';
import { ErrorBoundary } from '../../components/admin/ErrorBoundary';

// Composant de chargement global
const GlobalLoadingSpinner = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-axa-blue mx-auto mb-4"></div>
      <p className="text-gray-600">Chargement de l'interface d'administration...</p>
    </div>
  </div>
);

// Composant pour gérer l'authentification admin
const AdminAuthWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ProtectedRoute requiredRole="admin">
      {children}
    </ProtectedRoute>
  );
};

export const AdminRouter: React.FC = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<GlobalLoadingSpinner />}>
        <Routes>
          {/* Route de connexion admin */}
          <Route path="/login" element={<AdminLogin />} />
          
          {/* Routes protégées de l'admin */}
          <Route path="/*" element={
            <AdminAuthWrapper>
              <AdminDashboard_Optimized />
            </AdminAuthWrapper>
          } />
          
          {/* Redirection par défaut */}
          <Route path="/" element={<Navigate to="/admin/dashboard-insurance" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default AdminRouter;
