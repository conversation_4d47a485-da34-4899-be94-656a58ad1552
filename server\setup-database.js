const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
  console.log('🔧 Configuration de la base de données MySQL...');
  
  try {
    // Connexion à MySQL sans base de données spécifique
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '', // Mot de passe root MySQL (vide par défaut sur XAMPP/WAMP)
    });

    console.log('✅ Connexion à MySQL réussie');

    // Créer les bases de données
    await connection.execute(`CREATE DATABASE IF NOT EXISTS mtp_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS mtp_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ Bases de données créées');

    // <PERSON><PERSON>er l'utilisateur
    try {
      await connection.execute(`CREATE USER IF NOT EXISTS 'mtp_user'@'localhost' IDENTIFIED BY 'mtp_password_2024!'`);
      console.log('✅ Utilisateur mtp_user créé');
    } catch (error) {
      if (error.code === 'ER_CANNOT_USER') {
        console.log('ℹ️ Utilisateur mtp_user existe déjà');
      } else {
        throw error;
      }
    }

    // Accorder les privilèges
    await connection.execute(`GRANT ALL PRIVILEGES ON mtp_development.* TO 'mtp_user'@'localhost'`);
    await connection.execute(`GRANT ALL PRIVILEGES ON mtp_test.* TO 'mtp_user'@'localhost'`);
    await connection.execute(`FLUSH PRIVILEGES`);
    console.log('✅ Privilèges accordés');

    // Vérifier les bases créées
    const [databases] = await connection.execute(`SHOW DATABASES LIKE 'mtp_%'`);
    console.log('📋 Bases de données créées:', databases.map(db => Object.values(db)[0]));

    await connection.end();

    // Vérifier/créer le fichier .env
    const envPath = path.join(__dirname, '.env');
    if (!fs.existsSync(envPath)) {
      const envExamplePath = path.join(__dirname, '.env.example');
      if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath);
        console.log('✅ Fichier .env créé depuis .env.example');
      }
    }

    console.log('\n🎉 Configuration de la base de données terminée !');
    console.log('\n📋 Informations de connexion:');
    console.log('   Base de données: mtp_development');
    console.log('   Utilisateur: mtp_user');
    console.log('   Mot de passe: mtp_password_2024!');
    console.log('   URL: mysql://mtp_user:mtp_password_2024!@localhost:3306/mtp_development');
    
    console.log('\n🚀 Prochaines étapes:');
    console.log('   1. npm run db:generate');
    console.log('   2. npm run db:migrate');
    console.log('   3. npm run db:seed');
    console.log('   4. npm run dev');

  } catch (error) {
    console.error('❌ Erreur lors de la configuration:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solutions possibles:');
      console.log('   - Vérifiez que MySQL est démarré');
      console.log('   - Vérifiez que le port 3306 est ouvert');
      console.log('   - Si vous utilisez XAMPP/WAMP, démarrez MySQL depuis le panneau de contrôle');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Problème d\'authentification:');
      console.log('   - Vérifiez le mot de passe root MySQL');
      console.log('   - Modifiez le script si nécessaire');
    }
    
    process.exit(1);
  }
}

// Fonction alternative avec mot de passe
async function setupDatabaseWithPassword(rootPassword) {
  console.log('🔧 Configuration de la base de données MySQL avec mot de passe...');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: rootPassword,
    });

    console.log('✅ Connexion à MySQL réussie');

    // Même logique que setupDatabase() mais avec mot de passe
    await connection.execute(`CREATE DATABASE IF NOT EXISTS mtp_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS mtp_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ Bases de données créées');

    try {
      await connection.execute(`CREATE USER IF NOT EXISTS 'mtp_user'@'localhost' IDENTIFIED BY 'mtp_password_2024!'`);
      console.log('✅ Utilisateur mtp_user créé');
    } catch (error) {
      if (error.code === 'ER_CANNOT_USER') {
        console.log('ℹ️ Utilisateur mtp_user existe déjà');
      } else {
        throw error;
      }
    }

    await connection.execute(`GRANT ALL PRIVILEGES ON mtp_development.* TO 'mtp_user'@'localhost'`);
    await connection.execute(`GRANT ALL PRIVILEGES ON mtp_test.* TO 'mtp_user'@'localhost'`);
    await connection.execute(`FLUSH PRIVILEGES`);
    console.log('✅ Privilèges accordés');

    await connection.end();
    console.log('\n🎉 Configuration terminée !');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
    process.exit(1);
  }
}

// Vérifier les arguments de ligne de commande
const args = process.argv.slice(2);
if (args.length > 0 && args[0] === '--password') {
  const password = args[1] || '';
  setupDatabaseWithPassword(password);
} else {
  setupDatabase();
}
