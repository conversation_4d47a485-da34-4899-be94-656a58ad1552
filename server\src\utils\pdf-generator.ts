import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
// ...existing code...

interface PDFGenerationOptions {
  format?: 'A4' | 'A3' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export async function generatePDF(
  template: Template,
  data: any,
  options: PDFGenerationOptions = {}
): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    try {
      // Configuration par défaut
      const defaultOptions: PDFGenerationOptions = {
        format: 'A4',
        orientation: 'portrait',
        margins: {
          top: 50,
          bottom: 50,
          left: 50,
          right: 50
        }
      };

      const finalOptions = { ...defaultOptions, ...options };

      // Création du document PDF
      const doc = new PDFDocument({
        size: finalOptions.format,
        layout: finalOptions.orientation,
        margins: finalOptions.margins,
        info: {
          Title: template.name,
          Author: '<PERSON><PERSON><PERSON><PERSON> TECHNIQUE ET PREVOYANCE',
          Subject: template.description,
          Keywords: `${template.category}, ${template.type}`,
          CreationDate: new Date()
        }
      });

      // Buffer pour stocker le PDF
      const chunks: Buffer[] = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));

      // En-tête
      doc
        .fontSize(18)
        .font('Helvetica-Bold')
        .text('MOUMEN TECHNIQUE ET PREVOYANCE', { align: 'center' })
        .moveDown();

      // Titre du document
      doc
        .fontSize(16)
        .font('Helvetica')
        .text(template.name, { align: 'center' })
        .moveDown();

      // Contenu dynamique selon le type de template
      const fields = JSON.parse(template.fields);
      
      switch (template.type) {
        case 'auto':
          generateAutoContent(doc, data);
          break;
        case 'habitation':
          generateHabitationContent(doc, data);
          break;
        case 'sante':
          generateSanteContent(doc, data);
          break;
        default:
          generateDefaultContent(doc, data, fields);
      }

      // Pied de page
      doc
        .fontSize(10)
        .font('Helvetica')
        .text(
          'MOUMEN TECHNIQUE ET PREVOYANCE - Document généré le ' + new Date().toLocaleString('fr-FR'),
          { align: 'center' }
        );

      // Finalisation du document
      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

function generateAutoContent(doc: PDFKit.PDFDocument, data: any) {
  doc
    .fontSize(14)
    .font('Helvetica-Bold')
    .text('Informations du véhicule', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.vehicule) {
    doc.text(`Marque: ${data.vehicule.marque}`);
    doc.text(`Modèle: ${data.vehicule.modele}`);
    doc.text(`Année: ${data.vehicule.annee}`);
    doc.text(`Immatriculation: ${data.vehicule.immatriculation}`);
  }

  doc.moveDown().fontSize(14).font('Helvetica-Bold')
    .text('Garanties', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.garanties) {
    Object.entries(data.garanties).forEach(([garantie, selected]) => {
      doc.text(`${garantie}: ${selected ? '✓' : '✗'}`);
    });
  }
}

function generateHabitationContent(doc: PDFKit.PDFDocument, data: any) {
  doc
    .fontSize(14)
    .font('Helvetica-Bold')
    .text('Informations du logement', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.logement) {
    doc.text(`Type: ${data.logement.type}`);
    doc.text(`Surface: ${data.logement.surface} m²`);
    doc.text(`Adresse: ${data.logement.adresse}`);
  }

  doc.moveDown().fontSize(14).font('Helvetica-Bold')
    .text('Garanties', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.garanties) {
    Object.entries(data.garanties).forEach(([garantie, selected]) => {
      doc.text(`${garantie}: ${selected ? '✓' : '✗'}`);
    });
  }
}

function generateSanteContent(doc: PDFKit.PDFDocument, data: any) {
  doc
    .fontSize(14)
    .font('Helvetica-Bold')
    .text('Informations du bénéficiaire', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.beneficiaires) {
    data.beneficiaires.forEach((beneficiaire: any, index: number) => {
      doc.text(`Bénéficiaire ${index + 1}:`);
      doc.text(`Nom: ${beneficiaire.nom}`);
      doc.text(`Date de naissance: ${beneficiaire.dateNaissance}`);
      doc.text(`Numéro de sécurité sociale: ${beneficiaire.numeroSecu}`);
      doc.moveDown();
    });
  }

  doc.moveDown().fontSize(14).font('Helvetica-Bold')
    .text('Garanties', { underline: true })
    .moveDown();

  doc.fontSize(12).font('Helvetica');
  if (data.garanties) {
    Object.entries(data.garanties).forEach(([garantie, niveau]) => {
      doc.text(`${garantie}: Niveau ${niveau}`);
    });
  }
}

function generateDefaultContent(doc: PDFKit.PDFDocument, data: any, fields: string[]) {
  fields.forEach(field => {
    if (data[field]) {
      doc
        .fontSize(14)
        .font('Helvetica-Bold')
        .text(field.charAt(0).toUpperCase() + field.slice(1), { underline: true })
        .moveDown();

      doc
        .fontSize(12)
        .font('Helvetica')
        .text(JSON.stringify(data[field], null, 2))
        .moveDown();
    }
  });
}
