import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Star,
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Building,
  Target,
  Activity,
  FileText,
  CreditCard,
  Award
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  position?: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  status: 'ACTIVE' | 'INACTIVE' | 'PROSPECT' | 'SUSPENDED';
  type: 'INDIVIDUAL' | 'BUSINESS';
  segment: 'PREMIUM' | 'STANDARD' | 'BASIC';
  totalRevenue: number;
  lastOrderDate: string;
  registrationDate: string;
  notes: string;
  assignedTo: string;
  tags: string[];
  contracts: number;
  satisfaction: number;
}

export const ClientsManagement: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [segmentFilter, setSegmentFilter] = useState('ALL');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{show: boolean, message: string, type: 'success' | 'error'}>({
    show: false, message: '', type: 'success'
  });

  const [newClient, setNewClient] = useState<Partial<Client>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    position: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'Maroc',
    status: 'ACTIVE',
    type: 'INDIVIDUAL',
    segment: 'STANDARD',
    totalRevenue: 0,
    notes: '',
    assignedTo: 'current-user',
    tags: [],
    contracts: 0,
    satisfaction: 5
  });

  // Données de démonstration
  const demoClients: Client[] = [
    {
      id: '1',
      firstName: 'Ahmed',
      lastName: 'Benali',
      email: '<EMAIL>',
      phone: '+212 6 12 34 56 78',
      company: 'TechCorp Maroc',
      position: 'Directeur IT',
      address: '123 Rue Mohammed V',
      city: 'Casablanca',
      postalCode: '20000',
      country: 'Maroc',
      status: 'ACTIVE',
      type: 'BUSINESS',
      segment: 'PREMIUM',
      totalRevenue: 150000,
      lastOrderDate: '2024-01-10',
      registrationDate: '2023-06-15',
      notes: 'Client premium très satisfait. Renouvellement automatique.',
      assignedTo: 'Mohammed MOUMEN',
      tags: ['VIP', 'Tech', 'Renouvellement'],
      contracts: 3,
      satisfaction: 9
    },
    {
      id: '2',
      firstName: 'Fatima',
      lastName: 'El Mansouri',
      email: '<EMAIL>',
      phone: '+212 6 87 65 43 21',
      company: '',
      position: '',
      address: '456 Avenue Hassan II',
      city: 'Rabat',
      postalCode: '10000',
      country: 'Maroc',
      status: 'ACTIVE',
      type: 'INDIVIDUAL',
      segment: 'STANDARD',
      totalRevenue: 25000,
      lastOrderDate: '2024-01-08',
      registrationDate: '2023-09-20',
      notes: 'Cliente fidèle, paiements toujours à temps.',
      assignedTo: 'Sarah ALAMI',
      tags: ['Fidèle', 'Ponctuel'],
      contracts: 1,
      satisfaction: 8
    },
    {
      id: '3',
      firstName: 'Youssef',
      lastName: 'Tazi',
      email: '<EMAIL>',
      phone: '+212 5 22 33 44 55',
      company: 'Consulting Plus',
      position: 'Partner',
      address: '789 Boulevard Zerktouni',
      city: 'Casablanca',
      postalCode: '20100',
      country: 'Maroc',
      status: 'ACTIVE',
      type: 'BUSINESS',
      segment: 'STANDARD',
      totalRevenue: 75000,
      lastOrderDate: '2024-01-12',
      registrationDate: '2023-03-10',
      notes: 'Entreprise de conseil en croissance.',
      assignedTo: 'Karim BENJELLOUN',
      tags: ['Conseil', 'Croissance'],
      contracts: 2,
      satisfaction: 7
    },
    {
      id: '4',
      firstName: 'Aicha',
      lastName: 'Benkirane',
      email: '<EMAIL>',
      phone: '+212 6 55 44 33 22',
      company: '',
      position: '',
      address: '321 Rue Allal Ben Abdellah',
      city: 'Fès',
      postalCode: '30000',
      country: 'Maroc',
      status: 'INACTIVE',
      type: 'INDIVIDUAL',
      segment: 'BASIC',
      totalRevenue: 5000,
      lastOrderDate: '2023-11-15',
      registrationDate: '2023-08-05',
      notes: 'Client inactif depuis novembre.',
      assignedTo: 'Laila AMRANI',
      tags: ['Inactif'],
      contracts: 0,
      satisfaction: 6
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setClients(demoClients);
      setFilteredClients(demoClients);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Filtrer les clients
    let filtered = clients;

    if (searchTerm) {
      filtered = filtered.filter(client =>
        client.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.company?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(client => client.status === statusFilter);
    }

    if (segmentFilter !== 'ALL') {
      filtered = filtered.filter(client => client.segment === segmentFilter);
    }

    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(client => client.type === typeFilter);
    }

    setFilteredClients(filtered);
  }, [clients, searchTerm, statusFilter, segmentFilter, typeFilter]);

  const statusOptions = [
    { value: 'ACTIVE', label: 'Actif', color: 'bg-green-100 text-green-800' },
    { value: 'INACTIVE', label: 'Inactif', color: 'bg-gray-100 text-gray-800' },
    { value: 'PROSPECT', label: 'Prospect', color: 'bg-blue-100 text-blue-800' },
    { value: 'SUSPENDED', label: 'Suspendu', color: 'bg-red-100 text-red-800' }
  ];

  const segmentOptions = [
    { value: 'PREMIUM', label: 'Premium', color: 'bg-purple-100 text-purple-800' },
    { value: 'STANDARD', label: 'Standard', color: 'bg-blue-100 text-blue-800' },
    { value: 'BASIC', label: 'Basic', color: 'bg-gray-100 text-gray-800' }
  ];

  const typeOptions = [
    { value: 'INDIVIDUAL', label: 'Particulier', color: 'bg-green-100 text-green-800' },
    { value: 'BUSINESS', label: 'Entreprise', color: 'bg-orange-100 text-orange-800' }
  ];

  const getStatusInfo = (status: string) => {
    return statusOptions.find(s => s.value === status) || statusOptions[0];
  };

  const getSegmentInfo = (segment: string) => {
    return segmentOptions.find(s => s.value === segment) || segmentOptions[0];
  };

  const getTypeInfo = (type: string) => {
    return typeOptions.find(t => t.value === type) || typeOptions[0];
  };

  const handleAddClient = async () => {
    try {
      const clientToAdd: Client = {
        ...newClient as Client,
        id: Date.now().toString(),
        registrationDate: new Date().toISOString().split('T')[0],
        lastOrderDate: new Date().toISOString().split('T')[0]
      };

      setClients([...clients, clientToAdd]);
      setNewClient({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        company: '',
        position: '',
        address: '',
        city: '',
        postalCode: '',
        country: 'Maroc',
        status: 'ACTIVE',
        type: 'INDIVIDUAL',
        segment: 'STANDARD',
        totalRevenue: 0,
        notes: '',
        assignedTo: 'current-user',
        tags: [],
        contracts: 0,
        satisfaction: 5
      });
      setShowAddModal(false);
      setToast({show: true, message: 'Client ajouté avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de l\'ajout du client', type: 'error'});
    }
  };

  const handleEditClient = async () => {
    if (!selectedClient) return;

    try {
      const updatedClients = clients.map(client =>
        client.id === selectedClient.id ? selectedClient : client
      );
      setClients(updatedClients);
      setShowEditModal(false);
      setSelectedClient(null);
      setToast({show: true, message: 'Client modifié avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la modification', type: 'error'});
    }
  };

  const handleDeleteClient = async (clientId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce client ?')) {
      try {
        setClients(clients.filter(client => client.id !== clientId));
        setToast({show: true, message: 'Client supprimé avec succès', type: 'success'});
      } catch (error) {
        setToast({show: true, message: 'Erreur lors de la suppression', type: 'error'});
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h1>
            <p className="text-gray-600">Gérez votre portefeuille clients et suivez leur satisfaction</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Nouveau Client
          </button>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Clients</p>
                <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Clients Actifs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.filter(c => c.status === 'ACTIVE').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Chiffre d'Affaires</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.reduce((sum, client) => sum + client.totalRevenue, 0).toLocaleString()} DH
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Satisfaction Moyenne</p>
                <p className="text-2xl font-bold text-gray-900">
                  {clients.length > 0 ? (clients.reduce((sum, client) => sum + client.satisfaction, 0) / clients.length).toFixed(1) : 0}/10
                </p>
              </div>
              <Star className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Rechercher un client..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les statuts</option>
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
            <select
              value={segmentFilter}
              onChange={(e) => setSegmentFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les segments</option>
              {segmentOptions.map(segment => (
                <option key={segment.value} value={segment.value}>{segment.label}</option>
              ))}
            </select>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les types</option>
              {typeOptions.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des clients */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Segment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Chiffre d'Affaires
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Satisfaction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClients.map((client) => {
                const statusInfo = getStatusInfo(client.status);
                const segmentInfo = getSegmentInfo(client.segment);
                const typeInfo = getTypeInfo(client.type);

                return (
                  <motion.tr
                    key={client.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-axa-blue flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {client.firstName[0]}{client.lastName[0]}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {client.firstName} {client.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{client.email}</div>
                          {client.company && (
                            <div className="text-sm text-gray-500">{client.company}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeInfo.color}`}>
                        {typeInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusInfo.color}`}>
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${segmentInfo.color}`}>
                        {segmentInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {client.totalRevenue.toLocaleString()} DH
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < Math.floor(client.satisfaction / 2)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="ml-2 text-sm text-gray-900">{client.satisfaction}/10</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedClient(client);
                            setShowDetailModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedClient(client);
                            setShowEditModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClient(client.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Toast */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({...toast, show: false})}
        />
      )}
    </div>
  );
};

export default ClientsManagement;
