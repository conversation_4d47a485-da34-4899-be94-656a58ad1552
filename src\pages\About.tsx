import React from 'react';
import { motion } from 'framer-motion';
import { 
  Heart,
  Shield,
  Handshake,
  Clock,
  MapPin,
  Phone,
  Mail
} from 'lucide-react';

export const About: React.FC = () => {
  const values = [
    {
      icon: Shield,
      title: 'Expertise',
      description: 'Plus de 15 ans d\'expérience dans le domaine de l\'assurance au Maroc'
    },
    {
      icon: Heart,
      title: 'Proximité',
      description: 'Un service personnalisé et une relation de confiance avec chaque client'
    },
    {
      icon: Handshake,
      title: 'Engagement',
      description: 'Nous nous engageons à vous offrir les meilleures solutions d\'assurance'
    },
    {
      icon: Clock,
      title: 'Réactivité',
      description: 'Une équipe disponible 24h/24 pour répondre à tous vos besoins'
    }
  ];

  const team = [
    {
      name: '<PERSON> MOUMEN',
      role: 'Directeur Général',
      description: 'Expert en assurance avec plus de 20 ans d\'expérience dans le secteur.',
      image: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      name: 'Fatima ALAMI',
      role: 'Responsable Commerciale',
      description: 'Spécialisée dans l\'accompagnement des particuliers et des professionnels.',
      image: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    {
      name: 'Ahmed BENALI',
      role: 'Conseiller Sinistres',
      description: 'Expert dans la gestion et le suivi des sinistres pour tous types d\'assurance.',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ];

  const milestones = [
    { year: '2008', event: 'Création de MOUMEN TECHNIQUE ET PREVOYANCE' },
    { year: '2010', event: 'Obtention du statut d\'Agent Général AXA' },
    { year: '2015', event: '1000ème client satisfait' },
    { year: '2018', event: 'Expansion des services aux entreprises' },
    { year: '2020', event: 'Digitalisation des services' },
    { year: '2024', event: 'Plus de 5000 clients actifs' }
  ];

  return (
    <div className="py-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-axa-blue to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              À Propos de Nous
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              MOUMEN TECHNIQUE ET PREVOYANCE, votre partenaire de confiance pour tous vos besoins d'assurance au Maroc.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-axa-blue mb-6">
                Notre Histoire
              </h2>
              <div className="space-y-6 text-gray-600 leading-relaxed">
                <p>
                  Fondée en 2008, MOUMEN TECHNIQUE ET PREVOYANCE est née de la volonté de proposer des solutions d'assurance personnalisées et de qualité aux particuliers et aux entreprises du Maroc.
                </p>
                <p>
                  En tant qu'agent général AXA, nous bénéficions de la force et de l'expertise du leader mondial de l'assurance, tout en conservant notre proximité et notre connaissance du marché marocain.
                </p>
                <p>
                  Aujourd'hui, nous sommes fiers d'accompagner plus de 5000 clients dans la protection de leurs biens, de leur famille et de leur activité professionnelle.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <img
                src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Bureau MOUMEN TECHNIQUE ET PREVOYANCE"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-axa-blue/20 rounded-2xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-axa-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-axa-blue mb-6">
              Nos Valeurs
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Les principes qui guident notre action au quotidien pour vous offrir le meilleur service.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="bg-axa-blue w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                  <value.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-axa-blue mb-6">
              Notre Équipe
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Des professionnels expérimentés à votre service pour vous accompagner dans tous vos projets d'assurance.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-64 object-cover"
                />
                <div className="p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {member.name}
                  </h3>
                  <p className="text-axa-red font-medium mb-4">
                    {member.role}
                  </p>
                  <p className="text-gray-600 leading-relaxed">
                    {member.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-axa-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-axa-blue mb-6">
              Notre Parcours
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Les étapes clés de notre développement et de notre croissance.
            </p>
          </motion.div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-axa-blue"></div>
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`flex items-center ${
                    index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                  }`}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <div className="text-2xl font-bold text-axa-red mb-2">
                        {milestone.year}
                      </div>
                      <p className="text-gray-700 font-medium">
                        {milestone.event}
                      </p>
                    </div>
                  </div>
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-axa-red rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  <div className="w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-gradient-to-r from-axa-blue to-blue-700 rounded-2xl p-12 text-white text-center"
          >
            <h2 className="text-3xl lg:text-4xl font-bold mb-6">
              Rencontrons-nous
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Notre équipe est à votre disposition pour répondre à toutes vos questions et vous accompagner dans vos projets d'assurance.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <MapPin className="h-8 w-8 text-axa-red mb-4" />
                <h3 className="font-semibold mb-2">Adresse</h3>
                <p className="text-blue-100 text-center">
                  123 Boulevard Mohammed V<br />
                  Casablanca 20000, Maroc
                </p>
              </div>
              <div className="flex flex-col items-center">
                <Phone className="h-8 w-8 text-axa-red mb-4" />
                <h3 className="font-semibold mb-2">Téléphone</h3>
                <p className="text-blue-100">+212 5XX-XXXXXX</p>
              </div>
              <div className="flex flex-col items-center">
                <Mail className="h-8 w-8 text-axa-red mb-4" />
                <h3 className="font-semibold mb-2">Email</h3>
                <p className="text-blue-100"><EMAIL></p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};