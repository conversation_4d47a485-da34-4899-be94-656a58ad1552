import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  Legend
} from 'recharts';

// Couleurs AXA
const COLORS = {
  primary: '#00008F',
  secondary: '#FF1721',
  success: '#00A651',
  warning: '#FF8C00',
  info: '#00B9CE',
  light: '#F0F0F0',
  dark: '#333333'
};

const CHART_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.success,
  COLORS.warning,
  COLORS.info,
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7300',
  '#00ff00'
];

interface ChartContainerProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
  height?: number;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  subtitle,
  children,
  className = "",
  height = 300
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}
  >
    {(title || subtitle) && (
      <div className="mb-4">
        {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
        {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
      </div>
    )}
    <div style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        {children}
      </ResponsiveContainer>
    </div>
  </motion.div>
);

// Composant BarChart personnalisé
interface CustomBarChartProps {
  data: any[];
  xKey: string;
  yKey: string;
  title?: string;
  subtitle?: string;
  color?: string;
  className?: string;
  height?: number;
}

export const CustomBarChart: React.FC<CustomBarChartProps> = ({
  data,
  xKey,
  yKey,
  title,
  subtitle,
  color = COLORS.primary,
  className,
  height
}) => (
  <ChartContainer title={title} subtitle={subtitle} className={className} height={height}>
    <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
      <XAxis 
        dataKey={xKey} 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <YAxis 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <Tooltip 
        contentStyle={{
          backgroundColor: 'white',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}
      />
      <Bar dataKey={yKey} fill={color} radius={[4, 4, 0, 0]} />
    </BarChart>
  </ChartContainer>
);

// Composant LineChart personnalisé
interface CustomLineChartProps {
  data: any[];
  xKey: string;
  lines: { key: string; color?: string; name?: string }[];
  title?: string;
  subtitle?: string;
  className?: string;
  height?: number;
}

export const CustomLineChart: React.FC<CustomLineChartProps> = ({
  data,
  xKey,
  lines,
  title,
  subtitle,
  className,
  height
}) => (
  <ChartContainer title={title} subtitle={subtitle} className={className} height={height}>
    <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
      <XAxis 
        dataKey={xKey} 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <YAxis 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <Tooltip 
        contentStyle={{
          backgroundColor: 'white',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}
      />
      <Legend />
      {lines.map((line, index) => (
        <Line
          key={line.key}
          type="monotone"
          dataKey={line.key}
          stroke={line.color || CHART_COLORS[index]}
          strokeWidth={2}
          dot={{ r: 4 }}
          name={line.name || line.key}
        />
      ))}
    </LineChart>
  </ChartContainer>
);

// Composant PieChart personnalisé
interface CustomPieChartProps {
  data: any[];
  dataKey: string;
  nameKey: string;
  title?: string;
  subtitle?: string;
  className?: string;
  height?: number;
  showLabels?: boolean;
}

export const CustomPieChart: React.FC<CustomPieChartProps> = ({
  data,
  dataKey,
  nameKey,
  title,
  subtitle,
  className,
  height,
  showLabels = true
}) => (
  <ChartContainer title={title} subtitle={subtitle} className={className} height={height}>
    <PieChart>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        labelLine={false}
        label={showLabels ? ({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%` : false}
        outerRadius={80}
        fill="#8884d8"
        dataKey={dataKey}
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
        ))}
      </Pie>
      <Tooltip 
        contentStyle={{
          backgroundColor: 'white',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}
      />
      <Legend />
    </PieChart>
  </ChartContainer>
);

// Composant AreaChart personnalisé
interface CustomAreaChartProps {
  data: any[];
  xKey: string;
  areas: { key: string; color?: string; name?: string }[];
  title?: string;
  subtitle?: string;
  className?: string;
  height?: number;
  stacked?: boolean;
}

export const CustomAreaChart: React.FC<CustomAreaChartProps> = ({
  data,
  xKey,
  areas,
  title,
  subtitle,
  className,
  height,
  stacked = false
}) => (
  <ChartContainer title={title} subtitle={subtitle} className={className} height={height}>
    <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
      <XAxis 
        dataKey={xKey} 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <YAxis 
        tick={{ fontSize: 12 }}
        stroke="#666"
      />
      <Tooltip 
        contentStyle={{
          backgroundColor: 'white',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}
      />
      <Legend />
      {areas.map((area, index) => (
        <Area
          key={area.key}
          type="monotone"
          dataKey={area.key}
          stackId={stacked ? "1" : undefined}
          stroke={area.color || CHART_COLORS[index]}
          fill={area.color || CHART_COLORS[index]}
          fillOpacity={0.6}
          name={area.name || area.key}
        />
      ))}
    </AreaChart>
  </ChartContainer>
);

// Composant de métriques avec graphique simple
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon?: React.ReactNode;
  color?: string;
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  color = COLORS.primary,
  className = ""
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        {change && (
          <div className={`flex items-center mt-2 text-sm ${
            change.type === 'increase' ? 'text-green-600' : 'text-red-600'
          }`}>
            <span className={`inline-block w-0 h-0 mr-1 ${
              change.type === 'increase' 
                ? 'border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-green-600'
                : 'border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-red-600'
            }`}></span>
            {Math.abs(change.value)}% {change.period}
          </div>
        )}
      </div>
      {icon && (
        <div className="p-3 rounded-full" style={{ backgroundColor: `${color}20`, color }}>
          {icon}
        </div>
      )}
    </div>
  </motion.div>
);

// Export des couleurs pour utilisation externe
export { COLORS, CHART_COLORS };
