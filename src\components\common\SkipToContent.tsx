import React from 'react';

/**
 * Composant "Aller au contenu principal" pour l'accessibilité
 * Permet aux utilisateurs de lecteurs d'écran de passer directement au contenu
 */
export const SkipToContent: React.FC = () => {
  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-axa-blue text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-axa-blue"
      aria-label="Aller directement au contenu principal"
    >
      Aller au contenu principal
    </a>
  );
};

/**
 * Hook pour gérer la navigation au clavier
 */
export const useKeyboardNavigation = () => {
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Échapper pour fermer les modales
      if (event.key === 'Escape') {
        const modals = document.querySelectorAll('[role="dialog"]');
        modals.forEach(modal => {
          const closeButton = modal.querySelector('[aria-label*="fermer"], [aria-label*="Fermer"]');
          if (closeButton instanceof HTMLElement) {
            closeButton.click();
          }
        });
      }

      // Tab pour la navigation
      if (event.key === 'Tab') {
        // Ajouter une classe pour indiquer la navigation au clavier
        document.body.classList.add('keyboard-navigation');
      }
    };

    const handleMouseDown = () => {
      // Retirer la classe lors de l'utilisation de la souris
      document.body.classList.remove('keyboard-navigation');
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);
};

/**
 * Composant pour annoncer les changements aux lecteurs d'écran
 */
export const LiveRegion: React.FC<{
  message: string;
  politeness?: 'polite' | 'assertive';
  clearAfter?: number;
}> = ({ message, politeness = 'polite', clearAfter = 5000 }) => {
  const [currentMessage, setCurrentMessage] = React.useState(message);

  React.useEffect(() => {
    setCurrentMessage(message);
    
    if (clearAfter && message) {
      const timer = setTimeout(() => {
        setCurrentMessage('');
      }, clearAfter);
      
      return () => clearTimeout(timer);
    }
  }, [message, clearAfter]);

  return (
    <div
      aria-live={politeness}
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {currentMessage}
    </div>
  );
};

/**
 * Hook pour gérer les annonces aux lecteurs d'écran
 */
export const useScreenReaderAnnouncement = () => {
  const [announcement, setAnnouncement] = React.useState('');

  const announce = React.useCallback((message: string, politeness: 'polite' | 'assertive' = 'polite') => {
    setAnnouncement(''); // Clear first to ensure the message is announced
    setTimeout(() => {
      setAnnouncement(message);
    }, 100);
  }, []);

  return {
    announcement,
    announce,
    LiveRegion: () => <LiveRegion message={announcement} />
  };
};

/**
 * Composant pour améliorer l'accessibilité des boutons
 */
export const AccessibleButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'danger';
}> = ({ 
  children, 
  onClick, 
  disabled = false, 
  ariaLabel, 
  ariaDescribedBy,
  className = '',
  type = 'button',
  variant = 'primary'
}) => {
  const baseClasses = 'inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-axa-blue text-white hover:bg-blue-800 focus:ring-axa-blue disabled:bg-gray-300 disabled:text-gray-500',
    secondary: 'bg-white text-axa-blue border border-axa-blue hover:bg-blue-50 focus:ring-axa-blue disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-300',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 disabled:bg-gray-300 disabled:text-gray-500'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  );
};

/**
 * Composant pour les liens accessibles
 */
export const AccessibleLink: React.FC<{
  href: string;
  children: React.ReactNode;
  external?: boolean;
  ariaLabel?: string;
  className?: string;
}> = ({ href, children, external = false, ariaLabel, className = '' }) => {
  const baseClasses = 'transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-axa-blue focus:ring-offset-2 rounded';
  
  const linkProps = external ? {
    target: '_blank',
    rel: 'noopener noreferrer',
    'aria-label': ariaLabel || `${children} (s'ouvre dans un nouvel onglet)`
  } : {
    'aria-label': ariaLabel
  };

  return (
    <a
      href={href}
      className={`${baseClasses} ${className}`}
      {...linkProps}
    >
      {children}
      {external && (
        <span className="sr-only"> (s'ouvre dans un nouvel onglet)</span>
      )}
    </a>
  );
};

/**
 * Composant pour les formulaires accessibles
 */
export const AccessibleFormField: React.FC<{
  label: string;
  id: string;
  type?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  children?: React.ReactNode;
  className?: string;
}> = ({ label, id, type = 'text', required = false, error, helpText, children, className = '' }) => {
  const helpId = helpText ? `${id}-help` : undefined;
  const errorId = error ? `${id}-error` : undefined;
  const describedBy = [helpId, errorId].filter(Boolean).join(' ') || undefined;

  return (
    <div className={`space-y-2 ${className}`}>
      <label 
        htmlFor={id}
        className="block text-sm font-medium text-gray-700"
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="requis">*</span>
        )}
      </label>
      
      {children || (
        <input
          type={type}
          id={id}
          name={id}
          required={required}
          aria-describedby={describedBy}
          aria-invalid={error ? 'true' : 'false'}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent transition-colors ${
            error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      )}
      
      {helpText && (
        <p id={helpId} className="text-sm text-gray-600">
          {helpText}
        </p>
      )}
      
      {error && (
        <p id={errorId} className="text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};
