import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Calculator,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Download,
  Send,
  Signature,
  Car,
  Home,
  Heart,
  Shield,
  Building,
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  AlertTriangle,
  TrendingUp,
  Award,
  Target,
  Activity,
  Zap,
  Settings,
  PieChart,
  BarChart3,
  RefreshCw,
  Upload,
  Paperclip,
  MessageSquare
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface InsuranceQuote {
  id: string;
  quoteNumber: string;
  clientInfo: {
    id?: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    address: {
      street: string;
      city: string;
      postalCode: string;
      region: string;
    };
  };
  productType: 'AUTO' | 'HOME' | 'HEALTH' | 'LIFE' | 'BUSINESS' | 'TRAVEL';
  productDetails: {
    name: string;
    category: string;
    coverage: {
      amount: number;
      deductible: number;
      options: string[];
    };
    riskFactors: {
      [key: string]: any;
    };
  };
  pricing: {
    basePremium: number;
    adjustments: Array<{
      factor: string;
      description: string;
      amount: number;
      type: 'INCREASE' | 'DECREASE';
    }>;
    discounts: Array<{
      type: string;
      description: string;
      amount: number;
      percentage: number;
    }>;
    taxes: {
      amount: number;
      rate: number;
    };
    totalPremium: number;
    paymentOptions: Array<{
      frequency: 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL';
      amount: number;
      fees: number;
    }>;
  };
  status: 'DRAFT' | 'CALCULATED' | 'SENT' | 'VIEWED' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  validityPeriod: {
    startDate: string;
    endDate: string;
    daysRemaining: number;
  };
  assignedAgent: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  documents: Array<{
    id: string;
    name: string;
    type: 'QUOTE_PDF' | 'TERMS' | 'BROCHURE' | 'ADDITIONAL_INFO';
    url: string;
    generatedDate: string;
  }>;
  interactions: Array<{
    id: string;
    date: string;
    type: 'CREATED' | 'SENT' | 'VIEWED' | 'MODIFIED' | 'ACCEPTED' | 'REJECTED';
    description: string;
    user: string;
  }>;
  conversionProbability: number; // 0-100
  competitorAnalysis?: {
    competitors: Array<{
      name: string;
      estimatedPremium: number;
      advantages: string[];
      disadvantages: string[];
    }>;
    ourPosition: 'COMPETITIVE' | 'HIGHER' | 'LOWER';
    recommendations: string[];
  };
  approvalWorkflow?: {
    required: boolean;
    currentStep: string;
    approvers: Array<{
      role: string;
      name: string;
      status: 'PENDING' | 'APPROVED' | 'REJECTED';
      date?: string;
      comments?: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
  sentAt?: string;
  viewedAt?: string;
  respondedAt?: string;
}

interface PremiumCalculator {
  productType: string;
  baseRates: {
    [key: string]: number;
  };
  riskFactors: {
    [key: string]: {
      weight: number;
      values: {
        [key: string]: number;
      };
    };
  };
  discountRules: {
    [key: string]: {
      condition: string;
      percentage: number;
      maxAmount?: number;
    };
  };
}

export const QuotesContracts_Professional: React.FC = () => {
  const [quotes, setQuotes] = useState<InsuranceQuote[]>([]);
  const [selectedQuote, setSelectedQuote] = useState<InsuranceQuote | null>(null);
  const [showQuoteDetails, setShowQuoteDetails] = useState(false);
  const [showNewQuoteModal, setShowNewQuoteModal] = useState(false);
  const [showCalculator, setShowCalculator] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [productFilter, setProductFilter] = useState('ALL');
  const [agentFilter, setAgentFilter] = useState('ALL');
  const [toast, setToast] = useState<any>(null);

  // Calculateur de primes
  const [calculatorData, setCalculatorData] = useState({
    productType: 'AUTO',
    clientAge: 30,
    vehicleValue: 200000,
    vehicleAge: 2,
    drivingExperience: 10,
    location: 'CASABLANCA',
    coverage: 'COMPREHENSIVE',
    deductible: 2000,
    annualMileage: 15000,
    previousClaims: 0,
    multiPolicy: false,
    loyaltyYears: 0
  });

  const [calculatedPremium, setCalculatedPremium] = useState<any>(null);

  useEffect(() => {
    loadQuotes();
  }, []);

  const loadQuotes = async () => {
    try {
      setIsLoading(true);
      const response = await api.getQuotes();
      if (response.success) {
        setQuotes(response.data?.quotes || []);
      } else {
        // Données mock professionnelles
        setQuotes([
          {
            id: 'QUOTE-2024-001',
            quoteNumber: 'DEV-AUTO-2024-001234',
            clientInfo: {
              id: 'CLIENT-001',
              firstName: 'Youssef',
              lastName: 'ALAMI',
              email: '<EMAIL>',
              phone: '+212 6XX-XXXXXX',
              dateOfBirth: '1985-03-15',
              address: {
                street: '123 Avenue Hassan II',
                city: 'Casablanca',
                postalCode: '20000',
                region: 'Grand Casablanca'
              }
            },
            productType: 'AUTO',
            productDetails: {
              name: 'Assurance Auto Tous Risques Premium',
              category: 'Automobile',
              coverage: {
                amount: 500000,
                deductible: 2000,
                options: ['RC', 'Tous risques', 'Assistance 24h/24', 'Protection juridique', 'Véhicule de remplacement']
              },
              riskFactors: {
                vehicleValue: 250000,
                vehicleAge: 1,
                driverAge: 39,
                drivingExperience: 20,
                location: 'CASABLANCA',
                annualMileage: 12000,
                previousClaims: 0
              }
            },
            pricing: {
              basePremium: 2800,
              adjustments: [
                {
                  factor: 'YOUNG_VEHICLE',
                  description: 'Véhicule récent (bonus)',
                  amount: -200,
                  type: 'DECREASE'
                },
                {
                  factor: 'EXPERIENCED_DRIVER',
                  description: 'Conducteur expérimenté',
                  amount: -300,
                  type: 'DECREASE'
                },
                {
                  factor: 'HIGH_VALUE_VEHICLE',
                  description: 'Véhicule de valeur élevée',
                  amount: 400,
                  type: 'INCREASE'
                }
              ],
              discounts: [
                {
                  type: 'NO_CLAIMS',
                  description: 'Bonus sans sinistre',
                  amount: 420,
                  percentage: 15
                },
                {
                  type: 'MULTI_POLICY',
                  description: 'Réduction multi-produits',
                  amount: 140,
                  percentage: 5
                }
              ],
              taxes: {
                amount: 252,
                rate: 0.09
              },
              totalPremium: 2532,
              paymentOptions: [
                { frequency: 'ANNUAL', amount: 2532, fees: 0 },
                { frequency: 'SEMI_ANNUAL', amount: 1291, fees: 25 },
                { frequency: 'QUARTERLY', amount: 658, fees: 15 },
                { frequency: 'MONTHLY', amount: 221, fees: 10 }
              ]
            },
            status: 'SENT',
            validityPeriod: {
              startDate: '2024-01-15',
              endDate: '2024-02-14',
              daysRemaining: 18
            },
            assignedAgent: {
              id: 'AGENT-001',
              name: 'Fatima MOUMEN',
              email: '<EMAIL>',
              phone: '+212 5XX-XXXXXX'
            },
            documents: [
              {
                id: 'DOC-001',
                name: 'Devis Auto Premium.pdf',
                type: 'QUOTE_PDF',
                url: '/documents/quote-auto-001.pdf',
                generatedDate: '2024-01-15'
              },
              {
                id: 'DOC-002',
                name: 'Conditions Générales Auto.pdf',
                type: 'TERMS',
                url: '/documents/terms-auto.pdf',
                generatedDate: '2024-01-15'
              }
            ],
            interactions: [
              {
                id: 'INT-001',
                date: '2024-01-15T10:00:00Z',
                type: 'CREATED',
                description: 'Devis créé suite à demande client',
                user: 'Fatima MOUMEN'
              },
              {
                id: 'INT-002',
                date: '2024-01-15T14:30:00Z',
                type: 'SENT',
                description: 'Devis envoyé par email au client',
                user: 'Fatima MOUMEN'
              },
              {
                id: 'INT-003',
                date: '2024-01-16T09:15:00Z',
                type: 'VIEWED',
                description: 'Client a consulté le devis',
                user: 'Youssef ALAMI'
              }
            ],
            conversionProbability: 75,
            competitorAnalysis: {
              competitors: [
                {
                  name: 'Wafa Assurance',
                  estimatedPremium: 2800,
                  advantages: ['Prix similaire'],
                  disadvantages: ['Moins de services', 'Réseau plus limité']
                },
                {
                  name: 'MAMDA',
                  estimatedPremium: 2400,
                  advantages: ['Prix plus bas'],
                  disadvantages: ['Couverture limitée', 'Service client moyen']
                }
              ],
              ourPosition: 'COMPETITIVE',
              recommendations: [
                'Mettre en avant la qualité du service',
                'Proposer des options de paiement flexibles',
                'Inclure des services additionnels gratuits'
              ]
            },
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-16T09:15:00Z',
            sentAt: '2024-01-15T14:30:00Z',
            viewedAt: '2024-01-16T09:15:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des devis:', error);
      showToast('Erreur lors du chargement des devis', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const calculatePremium = () => {
    // Logique de calcul de prime sophistiquée
    let basePremium = 2000;
    
    // Facteurs de risque pour l'automobile
    if (calculatorData.productType === 'AUTO') {
      // Âge du conducteur
      if (calculatorData.clientAge < 25) basePremium *= 1.5;
      else if (calculatorData.clientAge < 30) basePremium *= 1.2;
      else if (calculatorData.clientAge > 65) basePremium *= 1.1;
      
      // Valeur du véhicule
      basePremium += (calculatorData.vehicleValue * 0.012);
      
      // Âge du véhicule
      if (calculatorData.vehicleAge > 10) basePremium *= 0.8;
      else if (calculatorData.vehicleAge < 2) basePremium *= 1.1;
      
      // Expérience de conduite
      if (calculatorData.drivingExperience < 3) basePremium *= 1.3;
      else if (calculatorData.drivingExperience > 15) basePremium *= 0.9;
      
      // Localisation
      const locationMultipliers = {
        'CASABLANCA': 1.2,
        'RABAT': 1.1,
        'MARRAKECH': 1.0,
        'FES': 0.95,
        'TANGIER': 1.05,
        'OTHER': 0.9
      };
      basePremium *= locationMultipliers[calculatorData.location] || 1.0;
      
      // Kilométrage annuel
      if (calculatorData.annualMileage > 20000) basePremium *= 1.15;
      else if (calculatorData.annualMileage < 10000) basePremium *= 0.95;
      
      // Sinistres précédents
      basePremium *= (1 + (calculatorData.previousClaims * 0.2));
    }
    
    // Réductions
    let discounts = [];
    let totalDiscount = 0;
    
    // Bonus sans sinistre
    if (calculatorData.previousClaims === 0) {
      const noClaimsDiscount = basePremium * 0.15;
      discounts.push({
        type: 'NO_CLAIMS',
        description: 'Bonus sans sinistre (15%)',
        amount: noClaimsDiscount,
        percentage: 15
      });
      totalDiscount += noClaimsDiscount;
    }
    
    // Multi-produits
    if (calculatorData.multiPolicy) {
      const multiPolicyDiscount = basePremium * 0.05;
      discounts.push({
        type: 'MULTI_POLICY',
        description: 'Réduction multi-produits (5%)',
        amount: multiPolicyDiscount,
        percentage: 5
      });
      totalDiscount += multiPolicyDiscount;
    }
    
    // Fidélité
    if (calculatorData.loyaltyYears > 0) {
      const loyaltyPercentage = Math.min(calculatorData.loyaltyYears * 2, 10);
      const loyaltyDiscount = basePremium * (loyaltyPercentage / 100);
      discounts.push({
        type: 'LOYALTY',
        description: `Fidélité ${calculatorData.loyaltyYears} ans (${loyaltyPercentage}%)`,
        amount: loyaltyDiscount,
        percentage: loyaltyPercentage
      });
      totalDiscount += loyaltyDiscount;
    }
    
    const netPremium = basePremium - totalDiscount;
    const taxes = netPremium * 0.09; // 9% de taxes
    const totalPremium = netPremium + taxes;
    
    const result = {
      basePremium: Math.round(basePremium),
      discounts,
      totalDiscount: Math.round(totalDiscount),
      netPremium: Math.round(netPremium),
      taxes: Math.round(taxes),
      totalPremium: Math.round(totalPremium),
      paymentOptions: [
        { frequency: 'ANNUAL', amount: Math.round(totalPremium), fees: 0 },
        { frequency: 'SEMI_ANNUAL', amount: Math.round(totalPremium / 2) + 25, fees: 25 },
        { frequency: 'QUARTERLY', amount: Math.round(totalPremium / 4) + 15, fees: 15 },
        { frequency: 'MONTHLY', amount: Math.round(totalPremium / 12) + 10, fees: 10 }
      ]
    };
    
    setCalculatedPremium(result);
    showToast('Prime calculée avec succès', 'success');
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800';
      case 'CALCULATED': return 'bg-blue-100 text-blue-800';
      case 'SENT': return 'bg-yellow-100 text-yellow-800';
      case 'VIEWED': return 'bg-purple-100 text-purple-800';
      case 'ACCEPTED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'EXPIRED': return 'bg-gray-100 text-gray-800';
      case 'CONVERTED': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProductIcon = (productType: string) => {
    switch (productType) {
      case 'AUTO': return <Car className="h-4 w-4" />;
      case 'HOME': return <Home className="h-4 w-4" />;
      case 'HEALTH': return <Heart className="h-4 w-4" />;
      case 'LIFE': return <Shield className="h-4 w-4" />;
      case 'BUSINESS': return <Building className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'DRAFT': 'Brouillon',
      'CALCULATED': 'Calculé',
      'SENT': 'Envoyé',
      'VIEWED': 'Consulté',
      'ACCEPTED': 'Accepté',
      'REJECTED': 'Rejeté',
      'EXPIRED': 'Expiré',
      'CONVERTED': 'Converti'
    };
    return statusMap[status] || status;
  };

  const filteredQuotes = Array.isArray(quotes) ? quotes.filter(quote => {
    const matchesSearch = quote.clientInfo.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.clientInfo.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.clientInfo.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || quote.status === statusFilter;
    const matchesProduct = productFilter === 'ALL' || quote.productType === productFilter;
    const matchesAgent = agentFilter === 'ALL' || quote.assignedAgent.id === agentFilter;
    
    return matchesSearch && matchesStatus && matchesProduct && matchesAgent;
  }) : [];

  const quotesStats = {
    total: quotes.length,
    sent: quotes.filter(q => q.status === 'SENT').length,
    accepted: quotes.filter(q => q.status === 'ACCEPTED').length,
    converted: quotes.filter(q => q.status === 'CONVERTED').length,
    totalValue: quotes.reduce((sum, q) => sum + q.pricing.totalPremium, 0),
    avgConversionRate: quotes.length > 0 ? Math.round(quotes.reduce((sum, q) => sum + q.conversionProbability, 0) / quotes.length) : 0,
    conversionRate: quotes.length > 0 ? Math.round((quotes.filter(q => q.status === 'CONVERTED').length / quotes.length) * 100) : 0
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Devis & Contrats</h1>
          <p className="text-gray-600">Génération de devis et gestion des contrats</p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCalculator(true)}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Calculator className="h-4 w-4 mr-2" />
            Calculateur
          </button>
          <button
            onClick={() => setShowNewQuoteModal(true)}
            className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouveau Devis
          </button>
        </div>
      </div>

      {/* Statistiques des devis */}
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Send className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Envoyés</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.sent}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Acceptés</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.accepted}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Convertis</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.converted}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Valeur</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(quotesStats.totalValue)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Target className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Prob. Moy.</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.avgConversionRate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Conversion</p>
              <p className="text-lg font-bold text-gray-900">{quotesStats.conversionRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Calculateur de primes modal */}
      <AnimatePresence>
        {showCalculator && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-900">Calculateur de Primes</h2>
                  <button
                    onClick={() => setShowCalculator(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="h-6 w-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Formulaire de calcul */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Paramètres de Calcul</h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Type de produit
                      </label>
                      <select
                        value={calculatorData.productType}
                        onChange={(e) => setCalculatorData({...calculatorData, productType: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                      >
                        <option value="AUTO">Automobile</option>
                        <option value="HOME">Habitation</option>
                        <option value="HEALTH">Santé</option>
                        <option value="LIFE">Vie</option>
                        <option value="BUSINESS">Entreprise</option>
                      </select>
                    </div>

                    {calculatorData.productType === 'AUTO' && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Âge du conducteur
                            </label>
                            <input
                              type="number"
                              value={calculatorData.clientAge}
                              onChange={(e) => setCalculatorData({...calculatorData, clientAge: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Expérience (années)
                            </label>
                            <input
                              type="number"
                              value={calculatorData.drivingExperience}
                              onChange={(e) => setCalculatorData({...calculatorData, drivingExperience: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Valeur du véhicule (DH)
                            </label>
                            <input
                              type="number"
                              value={calculatorData.vehicleValue}
                              onChange={(e) => setCalculatorData({...calculatorData, vehicleValue: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Âge du véhicule (années)
                            </label>
                            <input
                              type="number"
                              value={calculatorData.vehicleAge}
                              onChange={(e) => setCalculatorData({...calculatorData, vehicleAge: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Localisation
                          </label>
                          <select
                            value={calculatorData.location}
                            onChange={(e) => setCalculatorData({...calculatorData, location: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                          >
                            <option value="CASABLANCA">Casablanca</option>
                            <option value="RABAT">Rabat</option>
                            <option value="MARRAKECH">Marrakech</option>
                            <option value="FES">Fès</option>
                            <option value="TANGIER">Tanger</option>
                            <option value="OTHER">Autre</option>
                          </select>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Kilométrage annuel
                            </label>
                            <input
                              type="number"
                              value={calculatorData.annualMileage}
                              onChange={(e) => setCalculatorData({...calculatorData, annualMileage: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Sinistres précédents
                            </label>
                            <input
                              type="number"
                              value={calculatorData.previousClaims}
                              onChange={(e) => setCalculatorData({...calculatorData, previousClaims: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="multiPolicy"
                              checked={calculatorData.multiPolicy}
                              onChange={(e) => setCalculatorData({...calculatorData, multiPolicy: e.target.checked})}
                              className="h-4 w-4 text-axa-blue focus:ring-axa-blue border-gray-300 rounded"
                            />
                            <label htmlFor="multiPolicy" className="ml-2 text-sm text-gray-700">
                              Client multi-produits
                            </label>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Années de fidélité
                            </label>
                            <input
                              type="number"
                              value={calculatorData.loyaltyYears}
                              onChange={(e) => setCalculatorData({...calculatorData, loyaltyYears: parseInt(e.target.value)})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
                            />
                          </div>
                        </div>
                      </>
                    )}

                    <button
                      onClick={calculatePremium}
                      className="w-full flex items-center justify-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Calculator className="h-4 w-4 mr-2" />
                      Calculer la Prime
                    </button>
                  </div>

                  {/* Résultats du calcul */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Résultats du Calcul</h3>

                    {calculatedPremium ? (
                      <div className="space-y-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-3">Détail du Calcul</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Prime de base:</span>
                              <span className="font-medium">{formatCurrency(calculatedPremium.basePremium)}</span>
                            </div>

                            {calculatedPremium.discounts.map((discount, index) => (
                              <div key={index} className="flex justify-between text-green-600">
                                <span>- {discount.description}:</span>
                                <span className="font-medium">-{formatCurrency(discount.amount)}</span>
                              </div>
                            ))}

                            <hr className="my-2" />
                            <div className="flex justify-between">
                              <span>Prime nette:</span>
                              <span className="font-medium">{formatCurrency(calculatedPremium.netPremium)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Taxes (9%):</span>
                              <span className="font-medium">{formatCurrency(calculatedPremium.taxes)}</span>
                            </div>
                            <hr className="my-2" />
                            <div className="flex justify-between text-lg font-bold text-axa-blue">
                              <span>Prime totale:</span>
                              <span>{formatCurrency(calculatedPremium.totalPremium)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-3">Options de Paiement</h4>
                          <div className="space-y-2 text-sm">
                            {calculatedPremium.paymentOptions.map((option, index) => (
                              <div key={index} className="flex justify-between">
                                <span>
                                  {option.frequency === 'ANNUAL' ? 'Annuel' :
                                   option.frequency === 'SEMI_ANNUAL' ? 'Semestriel' :
                                   option.frequency === 'QUARTERLY' ? 'Trimestriel' : 'Mensuel'}:
                                </span>
                                <span className="font-medium">
                                  {formatCurrency(option.amount)}
                                  {option.fees > 0 && (
                                    <span className="text-gray-500 ml-1">(+{formatCurrency(option.fees)} frais)</span>
                                  )}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="flex space-x-3">
                          <button
                            onClick={() => {
                              // Logique pour créer un devis avec ces paramètres
                              showToast('Devis créé avec succès', 'success');
                              setShowCalculator(false);
                            }}
                            className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                          >
                            Créer le Devis
                          </button>
                          <button
                            onClick={() => {
                              // Logique pour sauvegarder le calcul
                              showToast('Calcul sauvegardé', 'success');
                            }}
                            className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                          >
                            Sauvegarder
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Calculator className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <p>Remplissez les paramètres et cliquez sur "Calculer" pour voir les résultats</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export { QuotesContracts_Professional as default };
