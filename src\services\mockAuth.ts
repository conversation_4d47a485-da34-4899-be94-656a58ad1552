/**
 * Service d'authentification mock pour les tests
 * À utiliser quand le backend n'est pas disponible
 */

// Utilisateurs de test
const MOCK_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'Admin123!',
    name: 'Administrateur',
    role: 'SUPER_ADMIN'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'User123!',
    name: 'Conseiller 1',
    role: 'ADMIN'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'User123!',
    name: 'Conseiller 2',
    role: 'ADMIN'
  }
];

// Token mock
const MOCK_TOKEN = 'mock-jwt-token-for-testing';

export const mockAuthService = {
  /**
   * Connexion mock
   */
  async login(email: string, password: string) {
    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 500));

    const user = MOCK_USERS.find(u => u.email === email && u.password === password);
    
    if (user) {
      // Stocker le token et les infos utilisateur
      localStorage.setItem('auth_token', MOCK_TOKEN);
      localStorage.setItem('user_info', JSON.stringify({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }));

      return {
        success: true,
        message: 'Connexion réussie',
        data: {
          token: MOCK_TOKEN,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          }
        }
      };
    } else {
      return {
        success: false,
        message: 'Email ou mot de passe incorrect'
      };
    }
  },

  /**
   * Déconnexion mock
   */
  async logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    return { success: true };
  },

  /**
   * Vérifier si l'utilisateur est connecté
   */
  isAuthenticated() {
    return !!localStorage.getItem('auth_token');
  },

  /**
   * Obtenir les informations de l'utilisateur connecté
   */
  getCurrentUser() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
      return {
        success: true,
        data: JSON.parse(userInfo)
      };
    }
    return {
      success: false,
      message: 'Non connecté'
    };
  }
};

/**
 * Hook d'authentification mock
 */
export const useMockAuth = () => {
  const login = async (email: string, password: string) => {
    try {
      const response = await mockAuthService.login(email, password);
      return response;
    } catch (error) {
      return {
        success: false,
        message: 'Erreur de connexion'
      };
    }
  };

  const logout = async () => {
    try {
      await mockAuthService.logout();
      window.location.href = '/admin/login';
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout même en cas d'erreur
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');
      window.location.href = '/admin/login';
    }
  };

  const isAuthenticated = () => {
    return mockAuthService.isAuthenticated();
  };

  const getCurrentUser = () => {
    return mockAuthService.getCurrentUser();
  };

  return {
    login,
    logout,
    isAuthenticated,
    getCurrentUser
  };
};
