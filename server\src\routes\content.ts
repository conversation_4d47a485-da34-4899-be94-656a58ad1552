import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createPageSchema = Joi.object({
  title: Joi.string().min(1).max(200).required(),
  slug: Joi.string().min(1).max(200).required(),
  content: Joi.string().required(),
  excerpt: Joi.string().max(500),
  status: Joi.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED').default('DRAFT'),
  seo: Joi.object({
    title: Joi.string().max(60),
    description: Joi.string().max(160),
    keywords: Joi.string().max(255),
  }),
});

const updatePageSchema = Joi.object({
  title: Joi.string().min(1).max(200),
  slug: Joi.string().min(1).max(200),
  content: Joi.string(),
  excerpt: Joi.string().max(500),
  status: Joi.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED'),
  seo: Joi.object({
    title: Joi.string().max(60),
    description: Joi.string().max(160),
    keywords: Joi.string().max(255),
  }),
});

/**
 * GET /api/content/pages
 * Récupérer toutes les pages
 */
router.get('/pages', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as string;
  const search = req.query.search as string;

  const skip = (page - 1) * limit;

  // Construire les filtres
  const where: any = {};
  
  if (status && status !== 'all') {
    where.status = status.toUpperCase();
  }
  
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
      { slug: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [pages, total] = await Promise.all([
    prisma.contentPage.findMany({
      where,
      skip,
      take: limit,
      orderBy: { updatedAt: 'desc' },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        status: true,
        publishedAt: true,
        createdAt: true,
        updatedAt: true,
        seo: true,
      },
    }),
    prisma.contentPage.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      pages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * GET /api/content/pages/:id
 * Récupérer une page spécifique
 */
router.get('/pages/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = await prisma.contentPage.findUnique({
    where: { id: req.params.id },
  });

  if (!page) {
    throw createError('Page non trouvée', 404);
  }

  res.json({
    success: true,
    data: { page },
  });
}));

/**
 * POST /api/content/pages
 * Créer une nouvelle page
 */
router.post('/pages', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createPageSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le slug est unique
  const existingPage = await prisma.contentPage.findUnique({
    where: { slug: value.slug },
  });

  if (existingPage) {
    throw createError('Une page avec ce slug existe déjà', 409);
  }

  // Créer la page
  const page = await prisma.contentPage.create({
    data: {
      ...value,
      publishedAt: value.status === 'PUBLISHED' ? new Date() : null,
    },
  });

  logger.info(`Content page created: ${page.title} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Page créée avec succès',
    data: { page },
  });
}));

/**
 * PUT /api/content/pages/:id
 * Mettre à jour une page
 */
router.put('/pages/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updatePageSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que la page existe
  const existingPage = await prisma.contentPage.findUnique({
    where: { id: req.params.id },
  });

  if (!existingPage) {
    throw createError('Page non trouvée', 404);
  }

  // Vérifier l'unicité du slug si modifié
  if (value.slug && value.slug !== existingPage.slug) {
    const slugExists = await prisma.contentPage.findUnique({
      where: { slug: value.slug },
    });

    if (slugExists) {
      throw createError('Une page avec ce slug existe déjà', 409);
    }
  }

  // Mettre à jour la page
  const updateData: any = { ...value };
  
  // Gérer la date de publication
  if (value.status === 'PUBLISHED' && existingPage.status !== 'PUBLISHED') {
    updateData.publishedAt = new Date();
  } else if (value.status !== 'PUBLISHED') {
    updateData.publishedAt = null;
  }

  const page = await prisma.contentPage.update({
    where: { id: req.params.id },
    data: updateData,
  });

  logger.info(`Content page updated: ${page.title} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Page mise à jour avec succès',
    data: { page },
  });
}));

/**
 * DELETE /api/content/pages/:id
 * Supprimer une page
 */
router.delete('/pages/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = await prisma.contentPage.findUnique({
    where: { id: req.params.id },
  });

  if (!page) {
    throw createError('Page non trouvée', 404);
  }

  await prisma.contentPage.delete({
    where: { id: req.params.id },
  });

  logger.info(`Content page deleted: ${page.title} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Page supprimée avec succès',
  });
}));

/**
 * GET /api/content/pages/slug/:slug
 * Récupérer une page par son slug (public)
 */
router.get('/pages/slug/:slug', asyncHandler(async (req, res) => {
  const page = await prisma.contentPage.findUnique({
    where: { 
      slug: req.params.slug,
      status: 'PUBLISHED',
    },
  });

  if (!page) {
    throw createError('Page non trouvée', 404);
  }

  res.json({
    success: true,
    data: { page },
  });
}));

/**
 * POST /api/content/pages/:id/duplicate
 * Dupliquer une page
 */
router.post('/pages/:id/duplicate', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const originalPage = await prisma.contentPage.findUnique({
    where: { id: req.params.id },
  });

  if (!originalPage) {
    throw createError('Page non trouvée', 404);
  }

  // Créer un nouveau slug unique
  const baseSlug = `${originalPage.slug}-copie`;
  let newSlug = baseSlug;
  let counter = 1;

  while (await prisma.contentPage.findUnique({ where: { slug: newSlug } })) {
    newSlug = `${baseSlug}-${counter}`;
    counter++;
  }

  // Créer la copie
  const duplicatedPage = await prisma.contentPage.create({
    data: {
      title: `${originalPage.title} (Copie)`,
      slug: newSlug,
      content: originalPage.content,
      excerpt: originalPage.excerpt,
      status: 'DRAFT',
      seo: originalPage.seo,
    },
  });

  logger.info(`Content page duplicated: ${originalPage.title} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Page dupliquée avec succès',
    data: { page: duplicatedPage },
  });
}));

export default router;
