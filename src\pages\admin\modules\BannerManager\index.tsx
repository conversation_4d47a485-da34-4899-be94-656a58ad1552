import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Search,
  Grid,
  List as ListIcon,
  Edit,
  Trash2,
  Eye,
  Save,
  X,
  MessageSquare,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { api } from '../../../../services/api';
import { Toast } from '../../../../components/common/Toast';
import { ContentForm } from './ContentForm';
import { TargetingForm } from './TargetingForm';
import { StyleForm } from './StyleForm';
import { SchedulingForm } from './SchedulingForm';
import { BannerPreview } from './BannerPreview';
import { BannerValidation } from './validation';
import type { Banner, ViewMode } from './types';

export const BannerManager: React.FC = () => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [filteredBanners, setFilteredBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' } | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');

  const { handleSubmit, control, reset, formState: { errors } } = useForm({
    resolver: zodResolver(BannerValidation),
    defaultValues: {
      title: '',
      content: '',
      type: 'info',
      targetPages: [],
      excludedPages: [],
      styles: {
        backgroundColor: '#ffffff',
        textColor: '#000000',
        position: 'top',
      },
      scheduling: {
        startDate: null,
        endDate: null,
        frequency: 'always',
      },
      status: 'draft',
    },
  });

  useEffect(() => {
    loadBanners();
  }, []);

  useEffect(() => {
    filterBanners();
  }, [banners, searchQuery]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
  };

  const loadBanners = async () => {
    setIsLoading(true);
    try {
      const response = await api.getBanners();
      if (response.success && response.data) {
        setBanners(response.data);
        showToast('Bannières chargées avec succès', 'success');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des bannières:', error);
      showToast('Erreur lors du chargement des bannières', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const filterBanners = () => {
    if (!searchQuery) {
      setFilteredBanners(banners);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = banners.filter(banner => 
      banner.title.toLowerCase().includes(query) ||
      banner.content.toLowerCase().includes(query)
    );
    setFilteredBanners(filtered);
  };

  const onSubmit = async (data: any) => {
    try {
      let response;
      
      if (editingBanner) {
        response = await api.updateBanner(editingBanner.id, data);
      } else {
        response = await api.createBanner(data);
      }

      if (response.success) {
        showToast(
          editingBanner ? 'Bannière mise à jour avec succès' : 'Bannière créée avec succès',
          'success'
        );
        setShowEditor(false);
        loadBanners();
      } else {
        showToast(response.message || 'Une erreur est survenue', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      showToast('Erreur lors de la sauvegarde', 'error');
    }
  };

  const handleDeleteBanner = async (banner: Banner) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la bannière "${banner.title}" ?`)) {
      return;
    }

    try {
      const response = await api.deleteBanner(banner.id);
      
      if (response.success) {
        showToast('Bannière supprimée avec succès', 'success');
        loadBanners();
      } else {
        showToast(response.message || 'Erreur lors de la suppression', 'error');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      showToast('Erreur lors de la suppression', 'error');
    }
  };

  const openEditor = (banner?: Banner) => {
    if (banner) {
      reset({
        title: banner.title,
        content: banner.content,
        type: banner.type,
        targetPages: banner.targetPages,
        excludedPages: banner.excludedPages,
        styles: banner.styles,
        scheduling: banner.scheduling,
        status: banner.status,
      });
      setEditingBanner(banner);
    } else {
      reset();
      setEditingBanner(null);
    }
    setShowEditor(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Bannières</h1>
          <p className="text-gray-600 mt-2">
            Créez et gérez les bannières d'information sur votre site
          </p>
        </div>
        <button
          onClick={() => openEditor()}
          className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle Bannière
        </button>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center space-x-4 bg-white p-4 rounded-lg shadow">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Rechercher..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          />
          <Search className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${
              viewMode === 'grid'
                ? 'bg-axa-blue text-white'
                : 'text-gray-400 hover:bg-gray-100'
            }`}
          >
            <Grid className="h-5 w-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${
              viewMode === 'list'
                ? 'bg-axa-blue text-white'
                : 'text-gray-400 hover:bg-gray-100'
            }`}
          >
            <ListIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Banner List */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-3 gap-4' : 'space-y-4'}>
        {filteredBanners.map((banner) => (
          <div
            key={banner.id}
            className={`bg-white rounded-lg shadow ${
              viewMode === 'grid' ? '' : 'flex items-center justify-between'
            } p-4`}
          >
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">{banner.title}</h3>
              <p className="text-sm text-gray-500 line-clamp-2">{banner.content}</p>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  banner.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {banner.status === 'active' ? 'Active' : 'Inactive'}
                </span>
                <span className="text-xs text-gray-500">
                  {banner.targetPages.length} page(s) ciblée(s)
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2 mt-4">
              <button
                onClick={() => {
                  setEditingBanner(banner);
                  setShowPreview(true);
                }}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <Eye className="h-4 w-4" />
              </button>
              <button
                onClick={() => openEditor(banner)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleDeleteBanner(banner)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={() => setShowEditor(false)}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="inline-block w-full max-w-4xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">
                      {editingBanner ? 'Modifier la bannière' : 'Nouvelle bannière'}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowPreview(true)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Prévisualiser
                      </button>
                      <button
                        type="submit"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-axa-blue hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Sauvegarder
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowEditor(false)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-axa-blue"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="px-6 py-4 grid grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <ContentForm control={control} errors={errors} />
                    <TargetingForm control={control} errors={errors} />
                  </div>
                  <div className="space-y-6">
                    <StyleForm control={control} errors={errors} />
                    <SchedulingForm control={control} errors={errors} />
                  </div>
                </div>
              </form>
            </motion.div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && editingBanner && (
        <BannerPreview
          banner={editingBanner}
          onClose={() => setShowPreview(false)}
        />
      )}

      {/* Toast */}
      <AnimatePresence>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default BannerManager;
