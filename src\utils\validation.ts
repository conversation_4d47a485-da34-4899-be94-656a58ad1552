// Utilitaires de validation pour les formulaires

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Validation d'email
const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

export const validateEmail = (email: string): boolean => {
  if (!email) return false;
  return emailRegex.test(email.toLowerCase());
};

// Validation de téléphone marocain
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+212|0)[5-7][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// Validation de nom
export const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 50;
};

// Validation de mot de passe
export const validatePassword = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*]/.test(password);
  
  return password.length >= minLength && 
         hasUpperCase && 
         hasLowerCase && 
         hasNumbers && 
         hasSpecialChar;
};

// Validation de lead
export const validateLead = (lead: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!validateName(lead.name || '')) {
    errors.push({ field: 'name', message: 'Le nom du lead est requis (2-50 caractères)' });
  }
  
  if (!validateEmail(lead.email || '')) {
    errors.push({ field: 'email', message: 'L\'email du lead n\'est pas valide' });
  }
  
  if (!validatePhone(lead.phone || '')) {
    errors.push({ field: 'phone', message: 'Le téléphone du lead n\'est pas valide' });
  }
  
  if (!lead.product || lead.product.trim().length === 0) {
    errors.push({ field: 'product', message: 'Le produit d\'intérêt est requis' });
  }
  
  if (!lead.source || lead.source.trim().length === 0) {
    errors.push({ field: 'source', message: 'La source du lead est requise' });
  }
  
  if (!lead.city || lead.city.trim().length === 0) {
    errors.push({ field: 'city', message: 'La ville du lead est requise' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation de client
export const validateClient = (client: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!validateName(client.name || '')) {
    errors.push({ field: 'name', message: 'Le nom du client est requis (2-50 caractères)' });
  }
  
  if (!validateEmail(client.email || '')) {
    errors.push({ field: 'email', message: 'L\'email du client n\'est pas valide' });
  }
  
  if (!validatePhone(client.phone || '')) {
    errors.push({ field: 'phone', message: 'Le téléphone du client n\'est pas valide' });
  }
  
  if (!client.type || !['Particulier', 'Professionnel'].includes(client.type)) {
    errors.push({ field: 'type', message: 'Le type de client est requis' });
  }
  
  if (!client.city || client.city.trim().length === 0) {
    errors.push({ field: 'city', message: 'La ville du client est requise' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation de devis
export const validateQuote = (quote: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!validateName(quote.clientName || '')) {
    errors.push({ field: 'clientName', message: 'Le nom du client est requis' });
  }
  
  if (!quote.product || quote.product.trim().length === 0) {
    errors.push({ field: 'product', message: 'Le produit est requis' });
  }
  
  if (!quote.amount || isNaN(parseFloat(quote.amount))) {
    errors.push({ field: 'amount', message: 'Le montant est requis et doit être un nombre' });
  }
  
  if (!quote.status || !['Brouillon', 'Envoyé', 'Accepté', 'Refusé', 'Expiré'].includes(quote.status)) {
    errors.push({ field: 'status', message: 'Le statut est requis' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation de sinistre
export const validateClaim = (claim: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!validateName(claim.clientName || '')) {
    errors.push({ field: 'clientName', message: 'Le nom du client est requis' });
  }
  
  if (!claim.contractType || claim.contractType.trim().length === 0) {
    errors.push({ field: 'contractType', message: 'Le type de contrat est requis' });
  }
  
  if (!claim.claimType || claim.claimType.trim().length === 0) {
    errors.push({ field: 'claimType', message: 'Le type de sinistre est requis' });
  }
  
  if (!claim.amount || isNaN(parseFloat(claim.amount))) {
    errors.push({ field: 'amount', message: 'Le montant est requis et doit être un nombre' });
  }
  
  if (!claim.status || !['Nouveau', 'En cours', 'Enquête', 'Approuvé', 'Refusé', 'Clôturé'].includes(claim.status)) {
    errors.push({ field: 'status', message: 'Le statut est requis' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation d'utilisateur
export const validateUser = (user: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!validateName(user.name || '')) {
    errors.push({ field: 'name', message: 'Le nom de l\'utilisateur est requis' });
  }
  
  if (!validateEmail(user.email || '')) {
    errors.push({ field: 'email', message: 'L\'email de l\'utilisateur n\'est pas valide' });
  }
  
  if (!user.role || !['Super Admin', 'Manager', 'Conseiller', 'Agent'].includes(user.role)) {
    errors.push({ field: 'role', message: 'Le rôle est requis' });
  }
  
  if (!user.permissions || !Array.isArray(user.permissions) || user.permissions.length === 0) {
    errors.push({ field: 'permissions', message: 'Au moins une permission est requise' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation de bannière
export const validateBanner = (banner: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!banner.name || banner.name.trim().length === 0) {
    errors.push({ field: 'name', message: 'Le nom de la bannière est requis' });
  }
  
  if (!banner.text || banner.text.trim().length === 0) {
    errors.push({ field: 'text', message: 'Le texte de la bannière est requis' });
  }
  
  if (!banner.type || !['promotion', 'info', 'alert', 'success'].includes(banner.type)) {
    errors.push({ field: 'type', message: 'Le type de bannière est requis' });
  }
  
  if (!banner.status || !['active', 'inactive', 'scheduled', 'expired'].includes(banner.status)) {
    errors.push({ field: 'status', message: 'Le statut est requis' });
  }
  
  if (!banner.backgroundColor || banner.backgroundColor.trim().length === 0) {
    errors.push({ field: 'backgroundColor', message: 'La couleur de fond est requise' });
  }
  
  if (!banner.textColor || banner.textColor.trim().length === 0) {
    errors.push({ field: 'textColor', message: 'La couleur du texte est requise' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation de page de contenu
export const validatePage = (page: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!page.title || page.title.trim().length === 0) {
    errors.push({ field: 'title', message: 'Le titre de la page est requis' });
  }
  
  if (!page.slug || page.slug.trim().length === 0) {
    errors.push({ field: 'slug', message: 'Le slug de la page est requis' });
  }
  
  if (!page.status || !['published', 'draft'].includes(page.status)) {
    errors.push({ field: 'status', message: 'Le statut est requis' });
  }
  
  if (!page.template || page.template.trim().length === 0) {
    errors.push({ field: 'template', message: 'Le template est requis' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation d'article de blog
export const validateBlogPost = (post: any): ValidationResult => {
  const errors: ValidationError[] = [];
  
  if (!post.title || post.title.trim().length === 0) {
    errors.push({ field: 'title', message: 'Le titre de l\'article est requis' });
  }
  
  if (!post.slug || post.slug.trim().length === 0) {
    errors.push({ field: 'slug', message: 'Le slug de l\'article est requis' });
  }
  
  if (!post.status || !['published', 'draft'].includes(post.status)) {
    errors.push({ field: 'status', message: 'Le statut est requis' });
  }
  
  if (!post.category || post.category.trim().length === 0) {
    errors.push({ field: 'category', message: 'La catégorie est requise' });
  }
  
  if (!post.excerpt || post.excerpt.trim().length === 0) {
    errors.push({ field: 'excerpt', message: 'L\'extrait est requis' });
  }
  
  if (!post.content || post.content.trim().length === 0) {
    errors.push({ field: 'content', message: 'Le contenu est requis' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Fonction utilitaire pour formater les erreurs
export const formatValidationErrors = (errors: ValidationError[]): string => {
  return errors.map(error => `${error.field}: ${error.message}`).join(', ');
};