import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  Server, 
  Database, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Activity,
  Clock
} from 'lucide-react';

interface APIEndpoint {
  name: string;
  url: string;
  status: 'online' | 'offline' | 'checking';
  responseTime?: number;
  lastCheck?: Date;
}

export const APIStatus: React.FC = () => {
  const [endpoints, setEndpoints] = useState<APIEndpoint[]>([
    { name: 'Health Check', url: '/health', status: 'checking' },
    { name: 'Authentication', url: '/auth/login', status: 'checking' },
    { name: 'Leads API', url: '/leads', status: 'checking' },
    { name: 'Clients API', url: '/clients', status: 'checking' },
    { name: 'Quotes API', url: '/quotes', status: 'checking' },
    { name: 'Claims API', url: '/claims', status: 'checking' },
    { name: 'Analytics API', url: '/analytics/dashboard', status: 'checking' }
  ]);

  const [isChecking, setIsChecking] = useState(false);
  const [lastFullCheck, setLastFullCheck] = useState<Date | null>(null);

  const checkEndpoint = async (endpoint: APIEndpoint): Promise<APIEndpoint> => {
    const startTime = Date.now();
    const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
    
    try {
      const response = await fetch(`${baseURL}${endpoint.url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
        }
      });

      const responseTime = Date.now() - startTime;
      
      return {
        ...endpoint,
        status: response.ok ? 'online' : 'offline',
        responseTime,
        lastCheck: new Date()
      };
    } catch (error) {
      return {
        ...endpoint,
        status: 'offline',
        responseTime: Date.now() - startTime,
        lastCheck: new Date()
      };
    }
  };

  const checkAllEndpoints = async () => {
    setIsChecking(true);
    
    const promises = endpoints.map(endpoint => checkEndpoint(endpoint));
    const results = await Promise.all(promises);
    
    setEndpoints(results);
    setLastFullCheck(new Date());
    setIsChecking(false);
  };

  useEffect(() => {
    checkAllEndpoints();
    
    // Vérification automatique toutes les 30 secondes
    const interval = setInterval(checkAllEndpoints, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'checking':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-50 border-green-200';
      case 'offline':
        return 'bg-red-50 border-red-200';
      case 'checking':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-yellow-50 border-yellow-200';
    }
  };

  const onlineCount = endpoints.filter(e => e.status === 'online').length;
  const totalCount = endpoints.length;
  const healthPercentage = (onlineCount / totalCount) * 100;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {healthPercentage === 100 ? (
              <Wifi className="h-6 w-6 text-green-600" />
            ) : healthPercentage > 50 ? (
              <Activity className="h-6 w-6 text-yellow-600" />
            ) : (
              <WifiOff className="h-6 w-6 text-red-600" />
            )}
            <h2 className="text-xl font-semibold text-gray-900">Statut API</h2>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            healthPercentage === 100 ? 'bg-green-100 text-green-800' :
            healthPercentage > 50 ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {onlineCount}/{totalCount} Services
          </div>
        </div>
        
        <button
          onClick={checkAllEndpoints}
          disabled={isChecking}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
          {isChecking ? 'Vérification...' : 'Actualiser'}
        </button>
      </div>

      {/* Indicateur de santé global */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Santé globale de l'API</span>
          <span className="text-sm font-bold text-gray-900">{healthPercentage.toFixed(0)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className={`h-2 rounded-full transition-all duration-500 ${
              healthPercentage === 100 ? 'bg-green-500' :
              healthPercentage > 50 ? 'bg-yellow-500' :
              'bg-red-500'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${healthPercentage}%` }}
          />
        </div>
      </div>

      {/* Liste des endpoints */}
      <div className="space-y-3">
        {endpoints.map((endpoint, index) => (
          <motion.div
            key={endpoint.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border ${getStatusColor(endpoint.status)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(endpoint.status)}
                <div>
                  <h3 className="font-medium text-gray-900">{endpoint.name}</h3>
                  <p className="text-sm text-gray-500">{endpoint.url}</p>
                </div>
              </div>
              
              <div className="text-right">
                {endpoint.responseTime && (
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Clock className="h-3 w-3" />
                    <span>{endpoint.responseTime}ms</span>
                  </div>
                )}
                {endpoint.lastCheck && (
                  <p className="text-xs text-gray-400">
                    {endpoint.lastCheck.toLocaleTimeString('fr-FR')}
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Informations de connexion */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Server className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">Serveur API</span>
        </div>
        <p className="text-sm text-gray-600">
          {import.meta.env.VITE_API_URL || 'http://localhost:3001/api'}
        </p>
        {lastFullCheck && (
          <p className="text-xs text-gray-500 mt-1">
            Dernière vérification: {lastFullCheck.toLocaleString('fr-FR')}
          </p>
        )}
      </div>

      {/* Instructions si API offline */}
      {healthPercentage < 100 && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Action requise</span>
          </div>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>• Vérifiez que le serveur API est démarré</p>
            <p>• Exécutez: <code className="bg-yellow-100 px-1 rounded">node start-api-server.js</code></p>
            <p>• Le système utilise les données mock en attendant</p>
          </div>
        </div>
      )}
    </div>
  );
};
