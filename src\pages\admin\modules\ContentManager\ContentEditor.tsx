import React, { useState } from 'react';
import {
  Bold,
  Italic,
  List,
  Link,
  Image,
  Code,
  Quote,
  Heading1,
  Heading2,
  Heading3,
} from 'lucide-react';

interface ContentEditorProps {
  value: string;
  onChange: (value: string) => void;
  onMediaSelect: (url: string) => void;
}

export const ContentEditor: React.FC<ContentEditorProps> = ({
  value,
  onChange,
  onMediaSelect,
}) => {
  const [preview, setPreview] = useState(false);

  const insertMarkdown = (prefix: string, suffix: string = '') => {
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const selected = text.substring(start, end);
      const after = text.substring(end);
      
      const newText = `${before}${prefix}${selected}${suffix}${after}`;
      onChange(newText);
      
      // Restore selection
      requestAnimationFrame(() => {
        textarea.focus();
        textarea.setSelectionRange(
          start + prefix.length,
          end + prefix.length
        );
      });
    }
  };

  const ToolbarButton: React.FC<{
    icon: React.ReactNode;
    title: string;
    onClick: () => void;
  }> = ({ icon, title, onClick }) => (
    <button
      type="button"
      onClick={onClick}
      className="p-2 text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded"
      title={title}
    >
      {icon}
    </button>
  );

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      <div className="border-b border-gray-300 bg-white p-2 flex items-center space-x-1">
        <ToolbarButton
          icon={<Bold className="h-4 w-4" />}
          title="Gras"
          onClick={() => insertMarkdown('**', '**')}
        />
        <ToolbarButton
          icon={<Italic className="h-4 w-4" />}
          title="Italique"
          onClick={() => insertMarkdown('_', '_')}
        />
        <div className="w-px h-6 bg-gray-300 mx-2" />
        <ToolbarButton
          icon={<Heading1 className="h-4 w-4" />}
          title="Titre 1"
          onClick={() => insertMarkdown('# ')}
        />
        <ToolbarButton
          icon={<Heading2 className="h-4 w-4" />}
          title="Titre 2"
          onClick={() => insertMarkdown('## ')}
        />
        <ToolbarButton
          icon={<Heading3 className="h-4 w-4" />}
          title="Titre 3"
          onClick={() => insertMarkdown('### ')}
        />
        <div className="w-px h-6 bg-gray-300 mx-2" />
        <ToolbarButton
          icon={<List className="h-4 w-4" />}
          title="Liste"
          onClick={() => insertMarkdown('- ')}
        />
        <ToolbarButton
          icon={<Quote className="h-4 w-4" />}
          title="Citation"
          onClick={() => insertMarkdown('> ')}
        />
        <ToolbarButton
          icon={<Code className="h-4 w-4" />}
          title="Code"
          onClick={() => insertMarkdown('`', '`')}
        />
        <div className="w-px h-6 bg-gray-300 mx-2" />
        <ToolbarButton
          icon={<Link className="h-4 w-4" />}
          title="Lien"
          onClick={() => insertMarkdown('[', '](url)')}
        />
        <ToolbarButton
          icon={<Image className="h-4 w-4" />}
          title="Image"
          onClick={() => onMediaSelect('')}
        />
      </div>

      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full h-96 p-4 font-mono text-sm focus:outline-none resize-none"
        placeholder="Écrivez votre contenu ici..."
      />
    </div>
  );
};

export default ContentEditor;
