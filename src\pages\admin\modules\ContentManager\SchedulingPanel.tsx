import React from 'react';
import { Controller } from 'react-hook-form';
import { Calendar } from 'lucide-react';

interface SchedulingPanelProps {
  control: any;
  errors: any;
}

export const SchedulingPanel: React.FC<SchedulingPanelProps> = ({ control, errors }) => {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Programmation</h4>
      
      <Controller
        name="publishDate"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de publication
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                {...field}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent pl-10"
              />
              <Calendar className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            {errors.publishDate && (
              <p className="mt-1 text-sm text-red-600">
                {errors.publishDate.message as string}
              </p>
            )}
          </div>
        )}
      />

      <Controller
        name="unpublishDate"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de dépublication
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                {...field}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent pl-10"
              />
              <Calendar className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            {errors.unpublishDate && (
              <p className="mt-1 text-sm text-red-600">
                {errors.unpublishDate.message as string}
              </p>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default SchedulingPanel;
