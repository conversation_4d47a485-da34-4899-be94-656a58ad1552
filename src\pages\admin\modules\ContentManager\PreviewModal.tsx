import React from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import { MarkdownRenderer } from '../../../../components/common/MarkdownRenderer';
import type { ContentItem } from './types';

interface PreviewModalProps {
  content: ContentItem | null;
  onClose: () => void;
}

export const PreviewModal: React.FC<PreviewModalProps> = ({ content, onClose }) => {
  if (!content) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="inline-block w-full max-w-6xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"
        >
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Prévisualisation
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 max-h-[70vh] overflow-y-auto">
            {/* En-tête */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {content.title}
              </h1>
              {content.excerpt && (
                <p className="text-xl text-gray-600 mb-4">{content.excerpt}</p>
              )}
              {content.featuredImage && (
                <img
                  src={content.featuredImage}
                  alt={content.title}
                  className="w-full h-64 object-cover rounded-lg mb-4"
                />
              )}
              <div className="flex items-center text-sm text-gray-500">
                <span>
                  Publié le{' '}
                  {new Date(content.createdAt).toLocaleDateString('fr-FR', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                  })}
                </span>
                {content.category && (
                  <>
                    <span className="mx-2">•</span>
                    <span className="text-axa-blue">{content.category}</span>
                  </>
                )}
              </div>
            </div>

            {/* Contenu */}
            <div className="prose max-w-none">
              <MarkdownRenderer content={content.content || ''} />
            </div>

            {/* Tags */}
            {content.tags && content.tags.length > 0 && (
              <div className="mt-8">
                <div className="flex flex-wrap gap-2">
                  {content.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PreviewModal;
