import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Star,
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Building,
  Target,
  Activity
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast } from '../../../components/common/Toast';
import { LeadDetailsModal } from '../../../components/admin/LeadDetailsModal';

interface Lead {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  position?: string;
  source: string;
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CLOSED_WON' | 'CLOSED_LOST';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  estimatedValue: number;
  probability: number;
  expectedCloseDate: string;
  lastContact: string;
  notes: string;
  assignedTo: string;
  createdAt: string;
  updatedAt: string;
}

export const LeadsManagement: React.FC = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<Lead[]>([]);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [priorityFilter, setPriorityFilter] = useState('ALL');
  const [loading, setLoading] = useState(true);
  const [toast, setToast] = useState<{show: boolean, message: string, type: 'success' | 'error'}>({
    show: false, message: '', type: 'success'
  });

  const [newLead, setNewLead] = useState<Partial<Lead>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    position: '',
    source: 'WEBSITE',
    status: 'NEW',
    priority: 'MEDIUM',
    estimatedValue: 0,
    probability: 10,
    expectedCloseDate: '',
    notes: '',
    assignedTo: 'current-user'
  });

  // Données de démonstration
  const demoLeads: Lead[] = [
    {
      id: '1',
      firstName: 'Ahmed',
      lastName: 'Benali',
      email: '<EMAIL>',
      phone: '+212 6 12 34 56 78',
      company: 'TechCorp Maroc',
      position: 'Directeur IT',
      source: 'WEBSITE',
      status: 'QUALIFIED',
      priority: 'HIGH',
      estimatedValue: 50000,
      probability: 75,
      expectedCloseDate: '2024-02-15',
      lastContact: '2024-01-10',
      notes: 'Intéressé par nos solutions CRM. Rendez-vous prévu la semaine prochaine.',
      assignedTo: 'Mohammed MOUMEN',
      createdAt: '2024-01-05',
      updatedAt: '2024-01-10'
    },
    {
      id: '2',
      firstName: 'Fatima',
      lastName: 'El Mansouri',
      email: '<EMAIL>',
      phone: '+212 6 87 65 43 21',
      company: 'StartupMA',
      position: 'CEO',
      source: 'REFERRAL',
      status: 'PROPOSAL',
      priority: 'URGENT',
      estimatedValue: 75000,
      probability: 60,
      expectedCloseDate: '2024-01-30',
      lastContact: '2024-01-08',
      notes: 'Startup en croissance rapide. Budget confirmé.',
      assignedTo: 'Sarah ALAMI',
      createdAt: '2024-01-03',
      updatedAt: '2024-01-08'
    },
    {
      id: '3',
      firstName: 'Youssef',
      lastName: 'Tazi',
      email: '<EMAIL>',
      phone: '+212 5 22 33 44 55',
      company: 'Consulting Plus',
      position: 'Partner',
      source: 'LINKEDIN',
      status: 'CONTACTED',
      priority: 'MEDIUM',
      estimatedValue: 30000,
      probability: 40,
      expectedCloseDate: '2024-03-01',
      lastContact: '2024-01-12',
      notes: 'Premier contact établi. Envoi de documentation.',
      assignedTo: 'Karim BENJELLOUN',
      createdAt: '2024-01-12',
      updatedAt: '2024-01-12'
    }
  ];

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setLeads(demoLeads);
      setFilteredLeads(demoLeads);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Filtrer les prospects
    let filtered = leads;

    if (searchTerm) {
      filtered = filtered.filter(lead =>
        lead.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.company?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(lead => lead.status === statusFilter);
    }

    if (priorityFilter !== 'ALL') {
      filtered = filtered.filter(lead => lead.priority === priorityFilter);
    }

    setFilteredLeads(filtered);
  }, [leads, searchTerm, statusFilter, priorityFilter]);

  const statusOptions = [
    { value: 'NEW', label: 'Nouveau', color: 'bg-blue-100 text-blue-800' },
    { value: 'CONTACTED', label: 'Contacté', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'QUALIFIED', label: 'Qualifié', color: 'bg-purple-100 text-purple-800' },
    { value: 'PROPOSAL', label: 'Proposition', color: 'bg-orange-100 text-orange-800' },
    { value: 'NEGOTIATION', label: 'Négociation', color: 'bg-indigo-100 text-indigo-800' },
    { value: 'CLOSED_WON', label: 'Gagné', color: 'bg-green-100 text-green-800' },
    { value: 'CLOSED_LOST', label: 'Perdu', color: 'bg-red-100 text-red-800' }
  ];

  const priorityOptions = [
    { value: 'LOW', label: 'Faible', color: 'bg-gray-100 text-gray-800' },
    { value: 'MEDIUM', label: 'Moyenne', color: 'bg-blue-100 text-blue-800' },
    { value: 'HIGH', label: 'Élevée', color: 'bg-orange-100 text-orange-800' },
    { value: 'URGENT', label: 'Urgente', color: 'bg-red-100 text-red-800' }
  ];

  const getStatusInfo = (status: string) => {
    return statusOptions.find(s => s.value === status) || statusOptions[0];
  };

  const getPriorityInfo = (priority: string) => {
    return priorityOptions.find(p => p.value === priority) || priorityOptions[0];
  };

  const handleAddLead = async () => {
    try {
      const leadToAdd: Lead = {
        ...newLead as Lead,
        id: Date.now().toString(),
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0],
        lastContact: new Date().toISOString().split('T')[0]
      };

      setLeads([...leads, leadToAdd]);
      setNewLead({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        company: '',
        position: '',
        source: 'WEBSITE',
        status: 'NEW',
        priority: 'MEDIUM',
        estimatedValue: 0,
        probability: 10,
        expectedCloseDate: '',
        notes: '',
        assignedTo: 'current-user'
      });
      setShowAddModal(false);
      setToast({show: true, message: 'Prospect ajouté avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de l\'ajout du prospect', type: 'error'});
    }
  };

  const handleEditLead = async () => {
    if (!selectedLead) return;

    try {
      const updatedLeads = leads.map(lead =>
        lead.id === selectedLead.id
          ? { ...selectedLead, updatedAt: new Date().toISOString().split('T')[0] }
          : lead
      );
      setLeads(updatedLeads);
      setShowEditModal(false);
      setSelectedLead(null);
      setToast({show: true, message: 'Prospect modifié avec succès', type: 'success'});
    } catch (error) {
      setToast({show: true, message: 'Erreur lors de la modification', type: 'error'});
    }
  };

  const handleDeleteLead = async (leadId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce prospect ?')) {
      try {
        setLeads(leads.filter(lead => lead.id !== leadId));
        setToast({show: true, message: 'Prospect supprimé avec succès', type: 'success'});
      } catch (error) {
        setToast({show: true, message: 'Erreur lors de la suppression', type: 'error'});
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Prospects</h1>
            <p className="text-gray-600">Gérez votre pipeline commercial et suivez vos opportunités</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-axa-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Nouveau Prospect
          </button>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Prospects</p>
                <p className="text-2xl font-bold text-gray-900">{leads.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Qualifiés</p>
                <p className="text-2xl font-bold text-gray-900">
                  {leads.filter(l => l.status === 'QUALIFIED').length}
                </p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Valeur Pipeline</p>
                <p className="text-2xl font-bold text-gray-900">
                  {leads.reduce((sum, lead) => sum + lead.estimatedValue, 0).toLocaleString()} DH
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-600" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taux Conversion</p>
                <p className="text-2xl font-bold text-gray-900">
                  {leads.length > 0 ? Math.round((leads.filter(l => l.status === 'CLOSED_WON').length / leads.length) * 100) : 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Rechercher un prospect..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Tous les statuts</option>
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
            >
              <option value="ALL">Toutes les priorités</option>
              {priorityOptions.map(priority => (
                <option key={priority.value} value={priority.value}>{priority.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Liste des prospects */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Prospect
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Entreprise
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priorité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur Estimée
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Probabilité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLeads.map((lead) => {
                const statusInfo = getStatusInfo(lead.status);
                const priorityInfo = getPriorityInfo(lead.priority);

                return (
                  <motion.tr
                    key={lead.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-axa-blue flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {lead.firstName[0]}{lead.lastName[0]}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {lead.firstName} {lead.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{lead.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{lead.company}</div>
                      <div className="text-sm text-gray-500">{lead.position}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusInfo.color}`}>
                        {statusInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityInfo.color}`}>
                        {priorityInfo.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {lead.estimatedValue.toLocaleString()} DH
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-axa-blue h-2 rounded-full"
                            style={{ width: `${lead.probability}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-900">{lead.probability}%</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedLead(lead);
                            setShowDetailsModal(true);
                          }}
                          className="text-axa-blue hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteLead(lead.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal d'ajout */}
      <AnimatePresence>
        {showAddModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.95 }}
              className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Nouveau Prospect</h2>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
                  <input
                    type="text"
                    value={newLead.firstName}
                    onChange={(e) => setNewLead({...newLead, firstName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
                  <input
                    type="text"
                    value={newLead.lastName}
                    onChange={(e) => setNewLead({...newLead, lastName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                  <input
                    type="email"
                    value={newLead.email}
                    onChange={(e) => setNewLead({...newLead, email: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                  <input
                    type="tel"
                    value={newLead.phone}
                    onChange={(e) => setNewLead({...newLead, phone: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Entreprise</label>
                  <input
                    type="text"
                    value={newLead.company}
                    onChange={(e) => setNewLead({...newLead, company: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Poste</label>
                  <input
                    type="text"
                    value={newLead.position}
                    onChange={(e) => setNewLead({...newLead, position: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
                  <select
                    value={newLead.source}
                    onChange={(e) => setNewLead({...newLead, source: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  >
                    <option value="WEBSITE">Site Web</option>
                    <option value="REFERRAL">Référence</option>
                    <option value="LINKEDIN">LinkedIn</option>
                    <option value="EMAIL">Email</option>
                    <option value="PHONE">Téléphone</option>
                    <option value="EVENT">Événement</option>
                    <option value="OTHER">Autre</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priorité</label>
                  <select
                    value={newLead.priority}
                    onChange={(e) => setNewLead({...newLead, priority: e.target.value as any})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  >
                    {priorityOptions.map(priority => (
                      <option key={priority.value} value={priority.value}>{priority.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Valeur Estimée (DH)</label>
                  <input
                    type="number"
                    value={newLead.estimatedValue}
                    onChange={(e) => setNewLead({...newLead, estimatedValue: Number(e.target.value)})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Probabilité (%)</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={newLead.probability}
                    onChange={(e) => setNewLead({...newLead, probability: Number(e.target.value)})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de Clôture Prévue</label>
                  <input
                    type="date"
                    value={newLead.expectedCloseDate}
                    onChange={(e) => setNewLead({...newLead, expectedCloseDate: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  <textarea
                    value={newLead.notes}
                    onChange={(e) => setNewLead({...newLead, notes: e.target.value})}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={handleAddLead}
                  className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700"
                >
                  Ajouter
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modal d'édition */}
      <AnimatePresence>
        {showEditModal && selectedLead && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.95 }}
              className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Modifier le Prospect</h2>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
                  <input
                    type="text"
                    value={selectedLead.firstName}
                    onChange={(e) => setSelectedLead({...selectedLead, firstName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
                  <input
                    type="text"
                    value={selectedLead.lastName}
                    onChange={(e) => setSelectedLead({...selectedLead, lastName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                  <input
                    type="email"
                    value={selectedLead.email}
                    onChange={(e) => setSelectedLead({...selectedLead, email: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                  <input
                    type="tel"
                    value={selectedLead.phone}
                    onChange={(e) => setSelectedLead({...selectedLead, phone: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Entreprise</label>
                  <input
                    type="text"
                    value={selectedLead.company}
                    onChange={(e) => setSelectedLead({...selectedLead, company: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Poste</label>
                  <input
                    type="text"
                    value={selectedLead.position}
                    onChange={(e) => setSelectedLead({...selectedLead, position: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                  <select
                    value={selectedLead.status}
                    onChange={(e) => setSelectedLead({...selectedLead, status: e.target.value as any})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  >
                    {statusOptions.map(status => (
                      <option key={status.value} value={status.value}>{status.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priorité</label>
                  <select
                    value={selectedLead.priority}
                    onChange={(e) => setSelectedLead({...selectedLead, priority: e.target.value as any})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  >
                    {priorityOptions.map(priority => (
                      <option key={priority.value} value={priority.value}>{priority.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Valeur Estimée (DH)</label>
                  <input
                    type="number"
                    value={selectedLead.estimatedValue}
                    onChange={(e) => setSelectedLead({...selectedLead, estimatedValue: Number(e.target.value)})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Probabilité (%)</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={selectedLead.probability}
                    onChange={(e) => setSelectedLead({...selectedLead, probability: Number(e.target.value)})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date de Clôture Prévue</label>
                  <input
                    type="date"
                    value={selectedLead.expectedCloseDate}
                    onChange={(e) => setSelectedLead({...selectedLead, expectedCloseDate: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  <textarea
                    value={selectedLead.notes}
                    onChange={(e) => setSelectedLead({...selectedLead, notes: e.target.value})}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-axa-blue focus:border-axa-blue"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={handleEditLead}
                  className="px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700"
                >
                  Modifier
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toast */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({...toast, show: false})}
        />
      )}

      {/* Modal de détails */}
      {selectedLead && (
        <LeadDetailsModal
          lead={selectedLead}
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedLead(null);
          }}
        />
      )}
    </div>
  );
};

export default LeadsManagement;
