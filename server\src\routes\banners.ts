import { Router } from 'express';
import Joi from 'joi';
import { prisma } from '@/config/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authenticate, authorize, AuthenticatedRequest, optionalAuth } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// Schémas de validation
const createBannerSchema = Joi.object({
  name: Joi.string().min(1).max(100).required(),
  text: Joi.string().min(1).max(500).required(),
  type: Joi.string().valid('INFO', 'PROMOTION', 'ALERT').required(),
  backgroundColor: Joi.string().required(),
  textColor: Joi.string().required(),
  startDate: Joi.date().required(),
  endDate: Joi.date().greater(Joi.ref('startDate')).required(),
  showContact: Joi.boolean().default(false),
});

const updateBannerSchema = Joi.object({
  name: Joi.string().min(1).max(100),
  text: Joi.string().min(1).max(500),
  type: Joi.string().valid('INFO', 'PROMOTION', 'ALERT'),
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'SCHEDULED', 'EXPIRED'),
  backgroundColor: Joi.string(),
  textColor: Joi.string(),
  startDate: Joi.date(),
  endDate: Joi.date(),
  showContact: Joi.boolean(),
});

/**
 * GET /api/banners
 * Récupérer tous les bandeaux (admin)
 */
router.get('/', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const banners = await prisma.banner.findMany({
    orderBy: { createdAt: 'desc' },
  });

  res.json({
    success: true,
    data: banners,
  });
}));

/**
 * GET /api/banners/active
 * Récupérer les bandeaux actifs (public)
 */
router.get('/active', optionalAuth, asyncHandler(async (req, res) => {
  const now = new Date();
  
  const banners = await prisma.banner.findMany({
    where: {
      status: 'ACTIVE',
      startDate: { lte: now },
      endDate: { gte: now },
    },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      name: true,
      text: true,
      type: true,
      backgroundColor: true,
      textColor: true,
      showContact: true,
    },
  });

  // Incrémenter les vues
  if (banners.length > 0) {
    await prisma.banner.updateMany({
      where: {
        id: { in: banners.map(b => b.id) },
      },
      data: {
        views: { increment: 1 },
      },
    });
  }

  res.json({
    success: true,
    data: banners,
  });
}));

/**
 * GET /api/banners/:id
 * Récupérer un bandeau spécifique
 */
router.get('/:id', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const banner = await prisma.banner.findUnique({
    where: { id: req.params.id },
  });

  if (!banner) {
    throw createError('Bandeau non trouvé', 404);
  }

  res.json({
    success: true,
    data: banner,
  });
}));

/**
 * POST /api/banners
 * Créer un nouveau bandeau
 */
router.post('/', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = createBannerSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Déterminer le statut initial
  const now = new Date();
  let status = 'INACTIVE';
  
  if (value.startDate <= now && value.endDate >= now) {
    status = 'ACTIVE';
  } else if (value.startDate > now) {
    status = 'SCHEDULED';
  } else if (value.endDate < now) {
    status = 'EXPIRED';
  }

  // Créer le bandeau
  const banner = await prisma.banner.create({
    data: {
      ...value,
      status,
    },
  });

  logger.info(`Banner created: ${banner.name} by ${req.user?.email}`);

  res.status(201).json({
    success: true,
    message: 'Bandeau créé avec succès',
    data: banner,
  });
}));

/**
 * PUT /api/banners/:id
 * Mettre à jour un bandeau
 */
router.put('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Validation des données
  const { error, value } = updateBannerSchema.validate(req.body);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  // Vérifier que le bandeau existe
  const existingBanner = await prisma.banner.findUnique({
    where: { id: req.params.id },
  });

  if (!existingBanner) {
    throw createError('Bandeau non trouvé', 404);
  }

  // Mettre à jour le statut automatiquement si les dates changent
  const updateData = { ...value };
  
  if (value.startDate || value.endDate) {
    const now = new Date();
    const startDate = value.startDate || existingBanner.startDate;
    const endDate = value.endDate || existingBanner.endDate;
    
    if (startDate <= now && endDate >= now) {
      updateData.status = 'ACTIVE';
    } else if (startDate > now) {
      updateData.status = 'SCHEDULED';
    } else if (endDate < now) {
      updateData.status = 'EXPIRED';
    }
  }

  // Mettre à jour le bandeau
  const banner = await prisma.banner.update({
    where: { id: req.params.id },
    data: updateData,
  });

  logger.info(`Banner updated: ${banner.name} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Bandeau mis à jour avec succès',
    data: banner,
  });
}));

/**
 * DELETE /api/banners/:id
 * Supprimer un bandeau
 */
router.delete('/:id', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const banner = await prisma.banner.findUnique({
    where: { id: req.params.id },
  });

  if (!banner) {
    throw createError('Bandeau non trouvé', 404);
  }

  await prisma.banner.delete({
    where: { id: req.params.id },
  });

  logger.info(`Banner deleted: ${banner.name} by ${req.user?.email}`);

  res.json({
    success: true,
    message: 'Bandeau supprimé avec succès',
  });
}));

/**
 * POST /api/banners/:id/toggle
 * Activer/désactiver un bandeau
 */
router.post('/:id/toggle', authenticate, authorize(['ADMIN', 'SUPER_ADMIN']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const banner = await prisma.banner.findUnique({
    where: { id: req.params.id },
  });

  if (!banner) {
    throw createError('Bandeau non trouvé', 404);
  }

  const newStatus = banner.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  
  const updatedBanner = await prisma.banner.update({
    where: { id: req.params.id },
    data: { status: newStatus },
  });

  logger.info(`Banner toggled: ${banner.name} to ${newStatus} by ${req.user?.email}`);

  res.json({
    success: true,
    message: `Bandeau ${newStatus === 'ACTIVE' ? 'activé' : 'désactivé'} avec succès`,
    data: updatedBanner,
  });
}));

/**
 * POST /api/banners/:id/click
 * Enregistrer un clic sur un bandeau (public)
 */
router.post('/:id/click', asyncHandler(async (req, res) => {
  const banner = await prisma.banner.findUnique({
    where: { id: req.params.id },
  });

  if (!banner) {
    throw createError('Bandeau non trouvé', 404);
  }

  // Incrémenter le compteur de clics
  await prisma.banner.update({
    where: { id: req.params.id },
    data: {
      clicks: { increment: 1 },
    },
  });

  res.json({
    success: true,
    message: 'Clic enregistré',
  });
}));

/**
 * GET /api/banners/stats/summary
 * Statistiques des bandeaux
 */
router.get('/stats/summary', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const [totalBanners, activeBanners, totalViews, totalClicks] = await Promise.all([
    prisma.banner.count(),
    prisma.banner.count({ where: { status: 'ACTIVE' } }),
    prisma.banner.aggregate({ _sum: { views: true } }),
    prisma.banner.aggregate({ _sum: { clicks: true } }),
  ]);

  const clickThroughRate = totalViews._sum.views && totalClicks._sum.clicks 
    ? (totalClicks._sum.clicks / totalViews._sum.views) * 100 
    : 0;

  res.json({
    success: true,
    data: {
      totalBanners,
      activeBanners,
      totalViews: totalViews._sum.views || 0,
      totalClicks: totalClicks._sum.clicks || 0,
      clickThroughRate: Math.round(clickThroughRate * 100) / 100,
    },
  });
}));

export default router;
