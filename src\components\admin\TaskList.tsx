import React from 'react';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';
import useTasks from '../../hooks/useTasks';

interface TaskListProps {
  leadId?: string;
}

export const TaskList: React.FC<TaskListProps> = ({ leadId }) => {
  const { tasks, getTasksForLead } = useTasks();
  const displayedTasks = leadId ? getTasksForLead(leadId) : tasks;

  return (
    <div className="space-y-4">
      {displayedTasks.length === 0 ? (
        <div className="text-center text-gray-500 py-4">
          Aucune tâche en cours
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {displayedTasks.map((task) => (
            <div
              key={task.id}
              className="p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  {task.type === 'FOLLOW_UP' && (
                    <Clock className="h-5 w-5 text-blue-500" />
                  )}
                  {task.type === 'QUOTE_REMINDER' && (
                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                  )}
                  {task.type === 'STATUS_UPDATE' && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {task.description}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Échéance : {new Date(task.dueDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TaskList;
