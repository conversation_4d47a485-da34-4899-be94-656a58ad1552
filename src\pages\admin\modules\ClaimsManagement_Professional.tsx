import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  FileText,
  Upload,
  Download,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Car,
  Home,
  Heart,
  Shield,
  User,
  Building,
  Camera,
  Paperclip,
  MessageSquare,
  DollarSign,
  Calculator,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  UserCheck,
  Gavel,
  TrendingUp,
  AlertCircle,
  Info
} from 'lucide-react';
import { api } from '../../../services/api';
import { Toast  } from '../../../components/common/Toast';

interface Claim {
  id: string;
  policyNumber: string;
  clientName: string;
  clientPhone: string;
  clientEmail: string;
  productType: 'AUTO' | 'HOME' | 'HEALTH' | 'LIFE' | 'BUSINESS';
  incidentDate: string;
  reportDate: string;
  status: 'DECLARED' | 'UNDER_REVIEW' | 'INVESTIGATING' | 'EXPERT_ASSIGNED' | 'SETTLEMENT_PROPOSED' | 'APPROVED' | 'PAID' | 'REJECTED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  estimatedAmount: number;
  approvedAmount?: number;
  paidAmount?: number;
  description: string;
  location: string;
  circumstances: string;
  assignedExpert?: {
    id: string;
    name: string;
    phone: string;
    email: string;
    specialization: string;
  };
  assignedAdjuster?: {
    id: string;
    name: string;
    email: string;
  };
  documents: Array<{
    id: string;
    name: string;
    type: 'PHOTO' | 'REPORT' | 'INVOICE' | 'MEDICAL' | 'POLICE_REPORT' | 'OTHER';
    url: string;
    uploadDate: string;
  }>;
  timeline: Array<{
    id: string;
    date: string;
    action: string;
    description: string;
    user: string;
    status?: string;
  }>;
  reserveAmount: number;
  deductible: number;
  coverage: string[];
  thirdParties?: Array<{
    name: string;
    phone: string;
    insuranceCompany?: string;
    policyNumber?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export const ClaimsManagement_Professional: React.FC = () => {
  const [claims, setClaims] = useState<Claim[]>([]);
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [showClaimDetails, setShowClaimDetails] = useState(false);
  const [showNewClaimModal, setShowNewClaimModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [priorityFilter, setPriorityFilter] = useState('ALL');
  const [productFilter, setProductFilter] = useState('ALL');
  const [toast, setToast] = useState<any>(null);

  useEffect(() => {
    loadClaims();
  }, []);

  const loadClaims = async () => {
    try {
      setIsLoading(true);
      const response = await api.getClaims();
      if (response.success) {
        setClaims(response.data?.claims || []);
      } else {
        // Données mock professionnelles
        setClaims([
          {
            id: 'SIN-2024-001',
            policyNumber: 'POL-AUTO-2023-1234',
            clientName: 'Ahmed Benali',
            clientPhone: '+212 6XX-XXXXXX',
            clientEmail: '<EMAIL>',
            productType: 'AUTO',
            incidentDate: '2024-01-10',
            reportDate: '2024-01-11',
            status: 'EXPERT_ASSIGNED',
            priority: 'HIGH',
            estimatedAmount: 25000,
            reserveAmount: 30000,
            deductible: 2000,
            description: 'Collision avec un autre véhicule au carrefour',
            location: 'Avenue Mohammed V, Casablanca',
            circumstances: 'Collision frontale lors d\'un dépassement. Dégâts importants sur l\'avant du véhicule.',
            coverage: ['RC', 'Tous risques', 'Assistance'],
            assignedExpert: {
              id: 'EXP-001',
              name: 'Mohammed ALAMI',
              phone: '+212 6XX-XXXXXX',
              email: '<EMAIL>',
              specialization: 'Automobile'
            },
            assignedAdjuster: {
              id: 'ADJ-001',
              name: 'Fatima MOUMEN',
              email: '<EMAIL>'
            },
            documents: [
              {
                id: 'DOC-001',
                name: 'Photos du véhicule',
                type: 'PHOTO',
                url: '/documents/photos-vehicule.jpg',
                uploadDate: '2024-01-11'
              },
              {
                id: 'DOC-002',
                name: 'Constat amiable',
                type: 'REPORT',
                url: '/documents/constat-amiable.pdf',
                uploadDate: '2024-01-11'
              }
            ],
            timeline: [
              {
                id: 'TL-001',
                date: '2024-01-11T09:00:00Z',
                action: 'DECLARATION',
                description: 'Déclaration du sinistre par le client',
                user: 'Ahmed Benali'
              },
              {
                id: 'TL-002',
                date: '2024-01-11T14:30:00Z',
                action: 'ASSIGNMENT',
                description: 'Assignation à l\'expert Mohammed ALAMI',
                user: 'Fatima MOUMEN',
                status: 'EXPERT_ASSIGNED'
              },
              {
                id: 'TL-003',
                date: '2024-01-12T10:15:00Z',
                action: 'EXPERTISE',
                description: 'Expertise réalisée sur site',
                user: 'Mohammed ALAMI'
              }
            ],
            thirdParties: [
              {
                name: 'Hassan Idrissi',
                phone: '+212 6XX-XXXXXX',
                insuranceCompany: 'Wafa Assurance',
                policyNumber: 'WA-2023-5678'
              }
            ],
            createdAt: '2024-01-11T09:00:00Z',
            updatedAt: '2024-01-12T10:15:00Z'
          },
          {
            id: 'SIN-2024-002',
            policyNumber: 'POL-HOME-2023-5678',
            clientName: 'Aicha Bennani',
            clientPhone: '+212 6XX-XXXXXX',
            clientEmail: '<EMAIL>',
            productType: 'HOME',
            incidentDate: '2024-01-08',
            reportDate: '2024-01-09',
            status: 'SETTLEMENT_PROPOSED',
            priority: 'MEDIUM',
            estimatedAmount: 15000,
            approvedAmount: 12000,
            reserveAmount: 15000,
            deductible: 1000,
            description: 'Dégât des eaux suite à rupture de canalisation',
            location: 'Appartement 3ème étage, Résidence Al Manar, Rabat',
            circumstances: 'Rupture de canalisation dans la salle de bain causant des infiltrations dans l\'appartement du dessous.',
            coverage: ['Dégâts des eaux', 'Recours des voisins'],
            assignedAdjuster: {
              id: 'ADJ-002',
              name: 'Youssef ALAMI',
              email: '<EMAIL>'
            },
            documents: [
              {
                id: 'DOC-003',
                name: 'Photos des dégâts',
                type: 'PHOTO',
                url: '/documents/degats-eau.jpg',
                uploadDate: '2024-01-09'
              },
              {
                id: 'DOC-004',
                name: 'Devis réparations',
                type: 'INVOICE',
                url: '/documents/devis-reparations.pdf',
                uploadDate: '2024-01-10'
              }
            ],
            timeline: [
              {
                id: 'TL-004',
                date: '2024-01-09T08:30:00Z',
                action: 'DECLARATION',
                description: 'Déclaration du sinistre',
                user: 'Aicha Bennani'
              },
              {
                id: 'TL-005',
                date: '2024-01-09T15:00:00Z',
                action: 'INVESTIGATION',
                description: 'Visite sur site et constatation des dégâts',
                user: 'Youssef ALAMI',
                status: 'INVESTIGATING'
              },
              {
                id: 'TL-006',
                date: '2024-01-12T11:00:00Z',
                action: 'SETTLEMENT',
                description: 'Proposition de règlement: 12,000 DH',
                user: 'Youssef ALAMI',
                status: 'SETTLEMENT_PROPOSED'
              }
            ],
            createdAt: '2024-01-09T08:30:00Z',
            updatedAt: '2024-01-12T11:00:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des sinistres:', error);
      showToast('Erreur lors du chargement des sinistres', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DECLARED': return 'bg-blue-100 text-blue-800';
      case 'UNDER_REVIEW': return 'bg-yellow-100 text-yellow-800';
      case 'INVESTIGATING': return 'bg-orange-100 text-orange-800';
      case 'EXPERT_ASSIGNED': return 'bg-purple-100 text-purple-800';
      case 'SETTLEMENT_PROPOSED': return 'bg-indigo-100 text-indigo-800';
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'PAID': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'CLOSED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-500';
      case 'HIGH': return 'bg-orange-500';
      case 'MEDIUM': return 'bg-yellow-500';
      case 'LOW': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getProductIcon = (productType: string) => {
    switch (productType) {
      case 'AUTO': return <Car className="h-4 w-4" />;
      case 'HOME': return <Home className="h-4 w-4" />;
      case 'HEALTH': return <Heart className="h-4 w-4" />;
      case 'LIFE': return <Shield className="h-4 w-4" />;
      case 'BUSINESS': return <Building className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'DECLARED': 'Déclaré',
      'UNDER_REVIEW': 'En révision',
      'INVESTIGATING': 'Enquête',
      'EXPERT_ASSIGNED': 'Expert assigné',
      'SETTLEMENT_PROPOSED': 'Règlement proposé',
      'APPROVED': 'Approuvé',
      'PAID': 'Payé',
      'REJECTED': 'Rejeté',
      'CLOSED': 'Fermé'
    };
    return statusMap[status] || status;
  };

  const filteredClaims = claims.filter(claim => {
    const matchesSearch = claim.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         claim.policyNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         claim.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || claim.status === statusFilter;
    const matchesPriority = priorityFilter === 'ALL' || claim.priority === priorityFilter;
    const matchesProduct = productFilter === 'ALL' || claim.productType === productFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesProduct;
  });

  const claimsStats = {
    total: claims.length,
    open: claims.filter(c => !['PAID', 'REJECTED', 'CLOSED'].includes(c.status)).length,
    urgent: claims.filter(c => c.priority === 'URGENT').length,
    totalReserve: claims.reduce((sum, c) => sum + c.reserveAmount, 0),
    avgProcessingTime: 8.5 // jours
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-axa-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Sinistres</h1>
          <p className="text-gray-600">Suivi professionnel des déclarations et règlements</p>
        </div>
        
        <button
          onClick={() => setShowNewClaimModal(true)}
          className="flex items-center px-4 py-2 bg-axa-blue text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouveau Sinistre
        </button>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-bold text-gray-900">{claimsStats.total}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">En cours</p>
              <p className="text-lg font-bold text-gray-900">{claimsStats.open}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Urgents</p>
              <p className="text-lg font-bold text-gray-900">{claimsStats.urgent}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Réserves</p>
              <p className="text-lg font-bold text-gray-900">{formatCurrency(claimsStats.totalReserve)}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Délai moy.</p>
              <p className="text-lg font-bold text-gray-900">{claimsStats.avgProcessingTime}j</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous les statuts</option>
            <option value="DECLARED">Déclaré</option>
            <option value="UNDER_REVIEW">En révision</option>
            <option value="INVESTIGATING">Enquête</option>
            <option value="EXPERT_ASSIGNED">Expert assigné</option>
            <option value="SETTLEMENT_PROPOSED">Règlement proposé</option>
            <option value="APPROVED">Approuvé</option>
            <option value="PAID">Payé</option>
            <option value="REJECTED">Rejeté</option>
            <option value="CLOSED">Fermé</option>
          </select>

          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Toutes priorités</option>
            <option value="URGENT">Urgent</option>
            <option value="HIGH">Élevée</option>
            <option value="MEDIUM">Moyenne</option>
            <option value="LOW">Faible</option>
          </select>

          <select
            value={productFilter}
            onChange={(e) => setProductFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-axa-blue focus:border-transparent"
          >
            <option value="ALL">Tous produits</option>
            <option value="AUTO">Automobile</option>
            <option value="HOME">Habitation</option>
            <option value="HEALTH">Santé</option>
            <option value="LIFE">Vie</option>
            <option value="BUSINESS">Entreprise</option>
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
            <Filter className="h-4 w-4 mr-2" />
            Filtres avancés
          </button>
        </div>
      </div>

      {/* Liste des sinistres */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sinistre
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Produit
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Montant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(filteredClaims) ? filteredClaims.map((claim) => (
                <motion.tr
                  key={claim.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedClaim(claim);
                    setShowClaimDetails(true);
                  }}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-3 ${getPriorityColor(claim.priority)}`}></div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{claim.id}</div>
                        <div className="text-sm text-gray-500">{claim.policyNumber}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{claim.clientName}</div>
                        <div className="text-sm text-gray-500">{claim.clientPhone}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getProductIcon(claim.productType)}
                      <span className="ml-2 text-sm text-gray-900">{claim.productType}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(claim.status)}`}>
                      {getStatusText(claim.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{formatCurrency(claim.estimatedAmount)}</div>
                      <div className="text-xs text-gray-500">Réserve: {formatCurrency(claim.reserveAmount)}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>{new Date(claim.incidentDate).toLocaleDateString('fr-FR')}</div>
                    <div className="text-xs">Déclaré: {new Date(claim.reportDate).toLocaleDateString('fr-FR')}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedClaim(claim);
                          setShowClaimDetails(true);
                        }}
                        className="text-axa-blue hover:text-blue-800"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Logique d'édition
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Menu d'actions
                        }}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              )) : []}
            </tbody>
          </table>
        </div>

        {(!Array.isArray(filteredClaims) || filteredClaims.length === 0) && (
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Aucun sinistre trouvé</p>
          </div>
        )}
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export { ClaimsManagement_Professional as default };
