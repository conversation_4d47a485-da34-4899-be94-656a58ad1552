# Database MySQL
# Développement local
DATABASE_URL="mysql://mtp_user:mtp_password_2024!@localhost:3306/mtp_development"

# Production Hostinger VPS
# DATABASE_URL="mysql://username:password@localhost:3306/mtp_production"

# Alternative pour Hostinger avec SSL
# DATABASE_URL="mysql://username:password@hostname:3306/mtp_production?sslaccept=strict"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"

# CORS
FRONTEND_URL="http://localhost:5173"

# Email Configuration (Nodemailer)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="MOUMEN TECHNIQUE ET PREVOYANCE"

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH="./uploads"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-this"

# External APIs (if needed)
AXA_API_URL="https://api.axa.ma"
AXA_API_KEY="your-axa-api-key"

# Monitoring (optional)
SENTRY_DSN="your-sentry-dsn"
