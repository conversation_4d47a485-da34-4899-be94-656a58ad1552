import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserPlus,
  Users,
  DollarSign,
  TrendingUp,
  ArrowUp,
  ArrowDown,
  Calendar,
  Target,
  Activity,
  Bell,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  Eye,
  MessageSquare
} from 'lucide-react';
import { api } from '../../../services/api';
import { SystemDiagnostic  } from '../../../components/admin/SystemDiagnostic';
import { APIStatus  } from '../../../components/admin/APIStatus';
import { ModuleTester  } from '../../../components/admin/ModuleTester';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

interface DashboardStats {
  totalLeads: number;
  totalClients: number;
  totalQuotes: number;
  totalRevenue: number;
  leadsThisMonth: number;
  clientsThisMonth: number;
  quotesThisMonth: number;
  revenueThisMonth: number;
  leadsByStatus: Array<{ status: string; count: number }>;
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
  }>;
}

export const DashboardOverview: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setIsLoading(true);
      const response = await api.getDashboardStats();
      if (response.success) {
        setStats(response.data);
      } else {
        setError('Erreur lors du chargement des statistiques');
      }
    } catch (error) {
      console.error('Erreur dashboard:', error);
      setError('Erreur lors du chargement des statistiques');
      // Données de fallback pour le développement
      setStats({
        totalLeads: 156,
        totalClients: 89,
        totalQuotes: 234,
        totalRevenue: 125000,
        leadsThisMonth: 23,
        clientsThisMonth: 12,
        quotesThisMonth: 34,
        revenueThisMonth: 15000,
        leadsByStatus: [
          { status: 'NEW', count: 45 },
          { status: 'CONTACTED', count: 32 },
          { status: 'QUALIFIED', count: 28 },
          { status: 'CONVERTED', count: 51 }
        ],
        recentActivity: [
          { id: '1', type: 'lead', message: 'Nouveau lead: Mohammed Alami', timestamp: '2024-01-15T10:30:00Z' },
          { id: '2', type: 'quote', message: 'Devis envoyé à Aicha Benali', timestamp: '2024-01-15T09:15:00Z' },
          { id: '3', type: 'client', message: 'Nouveau client: Hassan Idrissi', timestamp: '2024-01-15T08:45:00Z' }
        ]
      });
    } finally {
      setIsLoading(false);
    }
  };
  // Sample data
  const salesData = [
    { month: 'Jan', auto: 45, habitation: 32, sante: 28, prevoyance: 15, total: 120 },
    { month: 'Fév', auto: 52, habitation: 38, sante: 35, prevoyance: 18, total: 143 },
    { month: 'Mar', auto: 48, habitation: 42, sante: 31, prevoyance: 22, total: 143 },
    { month: 'Avr', auto: 61, habitation: 45, sante: 38, prevoyance: 25, total: 169 },
    { month: 'Mai', auto: 55, habitation: 48, sante: 42, prevoyance: 28, total: 173 },
    { month: 'Jun', auto: 67, habitation: 52, sante: 45, prevoyance: 32, total: 196 },
  ];

  const leadsData = [
    { source: 'Facebook', value: 35, color: '#1877F2' },
    { source: 'Instagram', value: 25, color: '#E4405F' },
    { source: 'Site Web', value: 20, color: '#00008F' },
    { source: 'Référencement', value: 15, color: '#34D399' },
    { source: 'Autres', value: 5, color: '#9CA3AF' },
  ];

  const recentActivities = [
    { id: 1, type: 'lead', message: 'Nouveau lead Facebook - Ahmed Benali', time: '5 min', icon: UserPlus, color: 'text-blue-500' },
    { id: 2, type: 'contract', message: 'Contrat auto signé - Fatima Zahra', time: '15 min', icon: CheckCircle, color: 'text-green-500' },
    { id: 3, type: 'quote', message: 'Devis habitation envoyé - Mohammed Alami', time: '30 min', icon: Activity, color: 'text-orange-500' },
    { id: 4, type: 'renewal', message: 'Renouvellement à traiter - Aicha Tazi', time: '1h', icon: AlertCircle, color: 'text-red-500' },
    { id: 5, type: 'meeting', message: 'RDV client programmé - Youssef Alaoui', time: '2h', icon: Calendar, color: 'text-purple-500' },
  ];

  const kpiCards = [
    {
      title: 'Leads ce mois',
      value: '127',
      change: '+12%',
      trend: 'up',
      icon: UserPlus,
      color: 'bg-blue-500',
      description: 'vs mois dernier'
    },
    {
      title: 'Nouveaux clients',
      value: '43',
      change: '+8%',
      trend: 'up',
      icon: Users,
      color: 'bg-green-500',
      description: 'vs mois dernier'
    },
    {
      title: 'CA ce mois',
      value: '285K DH',
      change: '+15%',
      trend: 'up',
      icon: DollarSign,
      color: 'bg-red-500',
      description: 'vs mois dernier'
    },
    {
      title: 'Taux conversion',
      value: '34%',
      change: '+3%',
      trend: 'up',
      icon: TrendingUp,
      color: 'bg-purple-500',
      description: 'vs mois dernier'
    }
  ];

  const upcomingTasks = [
    { id: 1, task: 'Relancer prospect auto - Ahmed B.', priority: 'high', due: 'Aujourd\'hui 14h00' },
    { id: 2, task: 'Préparer devis habitation - Fatima Z.', priority: 'medium', due: 'Demain 10h00' },
    { id: 3, task: 'Renouvellement contrat santé - Mohammed A.', priority: 'high', due: 'Demain 16h00' },
    { id: 4, task: 'Formation produit AXA Épargne', priority: 'low', due: 'Vendredi 9h00' },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-r from-axa-blue to-blue-700 rounded-2xl p-8 text-white"
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">Bonjour, Admin</h1>
            <p className="text-blue-100">Voici un aperçu de votre activité aujourd'hui</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{new Date().toLocaleDateString('fr-FR')}</div>
            <div className="text-blue-100">Tableau de bord principal</div>
          </div>
        </div>
      </motion.div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiCards.map((kpi, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`${kpi.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                <kpi.icon className="h-6 w-6 text-white" />
              </div>
              <div className={`flex items-center space-x-1 ${
                kpi.trend === 'up' ? 'text-green-500' : 'text-red-500'
              }`}>
                {kpi.trend === 'up' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                <span className="text-sm font-medium">{kpi.change}</span>
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 mb-1">{kpi.value}</div>
              <div className="text-sm text-gray-600">{kpi.title}</div>
              <div className="text-xs text-gray-500 mt-1">{kpi.description}</div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Sales Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold text-gray-900">Évolution des ventes</h3>
            <select className="border border-gray-300 rounded-lg px-3 py-1 text-sm">
              <option>6 derniers mois</option>
              <option>12 derniers mois</option>
              <option>Cette année</option>
            </select>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={salesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="total" stroke="#00008F" fill="#00008F" fillOpacity={0.1} />
              <Line type="monotone" dataKey="total" stroke="#00008F" strokeWidth={3} />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Leads Sources */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6">Sources de leads</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={leadsData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {leadsData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Activity and Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activities */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold text-gray-900">Activité récente</h3>
            <button className="text-axa-blue hover:text-axa-red text-sm font-medium">
              Voir tout
            </button>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className={`${activity.color} bg-gray-100 p-2 rounded-lg`}>
                  <activity.icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">Il y a {activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Upcoming Tasks */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="bg-white rounded-xl shadow-lg p-6"
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold text-gray-900">Tâches à venir</h3>
            <button className="text-axa-blue hover:text-axa-red text-sm font-medium">
              Gérer
            </button>
          </div>
          <div className="space-y-4">
            {upcomingTasks.map((task) => (
              <div key={task.id} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                <div className={`w-3 h-3 rounded-full ${
                  task.priority === 'high' ? 'bg-red-500' :
                  task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{task.task}</p>
                  <p className="text-xs text-gray-500 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {task.due}
                  </p>
                </div>
                <button className="text-gray-400 hover:text-gray-600">
                  <CheckCircle className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* API Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <APIStatus />
      </motion.div>

      {/* Module Tester */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      >
        <ModuleTester />
      </motion.div>

      {/* System Diagnostic */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <SystemDiagnostic />
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
        className="bg-white rounded-xl shadow-lg p-6"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-6">Actions rapides</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <UserPlus className="h-8 w-8 text-blue-500 mb-2" />
            <span className="text-sm font-medium">Nouveau lead</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Activity className="h-8 w-8 text-green-500 mb-2" />
            <span className="text-sm font-medium">Créer devis</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Calendar className="h-8 w-8 text-purple-500 mb-2" />
            <span className="text-sm font-medium">Planifier RDV</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Target className="h-8 w-8 text-red-500 mb-2" />
            <span className="text-sm font-medium">Campagne</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};
export { DashboardOverview as default };