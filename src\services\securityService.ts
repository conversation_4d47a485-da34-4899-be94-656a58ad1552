import { AdminModule, AdminPermission } from '../types/admin';

interface SecurityEvent {
  timestamp: string;
  type: 'ACCESS_ATTEMPT' | 'PERMISSION_CHANGE' | 'SECURITY_ALERT' | 'SUSPICIOUS_ACTIVITY';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  module?: AdminModule;
  userId?: string;
  ip?: string;
  details: Record<string, any>;
  metadata: {
    userAgent?: string;
    location?: {
      country?: string;
      city?: string;
      coordinates?: [number, number];
    };
    device?: {
      type: string;
      os: string;
      browser: string;
    };
  };
}

interface SecurityRule {
  id: string;
  name: string;
  description: string;
  condition: (event: SecurityEvent) => boolean;
  action: (event: SecurityEvent) => Promise<void>;
  priority: number;
  enabled: boolean;
}

class SecurityService {
  private static instance: SecurityService;
  private securityEvents: SecurityEvent[] = [];
  private securityRules: SecurityRule[] = [];
  private activeAlerts: Map<string, SecurityEvent> = new Map();
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  private loginAttempts: Map<string, { count: number; lastAttempt: number }> = new Map();

  private constructor() {
    this.initializeDefaultRules();
  }

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  private initializeDefaultRules() {
    // Règle : Détection d'accès multiples non autorisés
    this.addRule({
      id: 'MULTIPLE_UNAUTHORIZED_ACCESS',
      name: 'Détection d\'accès multiples non autorisés',
      description: 'Détecte les tentatives répétées d\'accès à des modules non autorisés',
      condition: (event) => {
        if (event.type !== 'ACCESS_ATTEMPT') return false;
        const recentEvents = this.getRecentEvents(event.userId || '', 5 * 60 * 1000); // 5 minutes
        return recentEvents.filter(e => e.type === 'ACCESS_ATTEMPT' && !e.details.authorized).length >= 3;
      },
      action: async (event) => {
        await this.notifySecurityTeam({
          type: 'SECURITY_ALERT',
          severity: 'HIGH',
          message: `Tentatives d'accès multiples détectées pour l'utilisateur ${event.userId}`,
          event
        });
        await this.lockAccount(event.userId || '');
      },
      priority: 1,
      enabled: true
    });

    // Règle : Détection d'activité géographique suspecte
    this.addRule({
      id: 'SUSPICIOUS_GEO_ACCESS',
      name: 'Détection d\'activité géographique suspecte',
      description: 'Détecte les accès depuis des localisations inhabituelles',
      condition: (event) => {
        if (!event.metadata.location) return false;
        return this.isUnusualLocation(event.metadata.location);
      },
      action: async (event) => {
        await this.notifySecurityTeam({
          type: 'SECURITY_ALERT',
          severity: 'MEDIUM',
          message: `Accès détecté depuis une localisation inhabituelle: ${event.metadata.location?.country}`,
          event
        });
        await this.requireAdditionalAuthentication(event.userId || '');
      },
      priority: 2,
      enabled: true
    });

    // Règle : Modification massive de permissions
    this.addRule({
      id: 'MASS_PERMISSION_CHANGE',
      name: 'Modification massive de permissions',
      description: 'Détecte les modifications importantes de permissions en peu de temps',
      condition: (event) => {
        if (event.type !== 'PERMISSION_CHANGE') return false;
        const recentEvents = this.getRecentEvents(event.userId || '', 5 * 60 * 1000);
        return recentEvents.filter(e => e.type === 'PERMISSION_CHANGE').length >= 5;
      },
      action: async (event) => {
        await this.notifySecurityTeam({
          type: 'SECURITY_ALERT',
          severity: 'HIGH',
          message: 'Modifications massives de permissions détectées',
          event
        });
        await this.freezePermissionChanges(event.userId || '');
      },
      priority: 1,
      enabled: true
    });
  }

  public async processSecurityEvent(event: SecurityEvent): Promise<void> {
    // Enrichir l'événement avec des métadonnées supplémentaires
    const enrichedEvent = await this.enrichEventData(event);
    this.securityEvents.push(enrichedEvent);

    // Appliquer les règles de sécurité
    for (const rule of this.securityRules.sort((a, b) => a.priority - b.priority)) {
      if (rule.enabled && rule.condition(enrichedEvent)) {
        await rule.action(enrichedEvent);
      }
    }

    // Nettoyage des anciens événements
    this.cleanupOldEvents();
  }

  public async validateAccess(
    userId: string,
    module: AdminModule,
    requiredPermissions?: AdminPermission[]
  ): Promise<{
    granted: boolean;
    reason?: string;
    additionalChecksRequired?: boolean;
  }> {
    // Vérifier le verrouillage du compte
    if (await this.isAccountLocked(userId)) {
      return {
        granted: false,
        reason: 'Compte temporairement verrouillé pour des raisons de sécurité'
      };
    }

    // Vérifier les tentatives de connexion
    const attempts = this.loginAttempts.get(userId);
    if (attempts && attempts.count >= this.MAX_LOGIN_ATTEMPTS) {
      const timeRemaining = this.LOCKOUT_DURATION - (Date.now() - attempts.lastAttempt);
      if (timeRemaining > 0) {
        return {
          granted: false,
          reason: `Compte verrouillé. Réessayez dans ${Math.ceil(timeRemaining / 60000)} minutes`
        };
      }
      this.loginAttempts.delete(userId);
    }

    // Vérifier si une authentification supplémentaire est requise
    if (await this.requiresAdditionalAuth(userId)) {
      return {
        granted: false,
        additionalChecksRequired: true,
        reason: 'Authentification supplémentaire requise'
      };
    }

    return { granted: true };
  }

  public addRule(rule: SecurityRule): void {
    this.securityRules.push(rule);
    this.securityRules.sort((a, b) => a.priority - b.priority);
  }

  public getActiveAlerts(): SecurityEvent[] {
    return Array.from(this.activeAlerts.values());
  }

  // Méthodes privées utilitaires
  private async enrichEventData(event: SecurityEvent): Promise<SecurityEvent> {
    return {
      ...event,
      metadata: {
        ...event.metadata,
        timestamp: new Date().toISOString(),
        // Ajouter d'autres métadonnées selon le contexte
      }
    };
  }

  private getRecentEvents(userId: string, timeWindow: number): SecurityEvent[] {
    const now = Date.now();
    return this.securityEvents.filter(
      event =>
        event.userId === userId &&
        now - new Date(event.timestamp).getTime() <= timeWindow
    );
  }

  private async isAccountLocked(userId: string): Promise<boolean> {
    // Implémenter la logique de vérification du verrouillage
    return false;
  }

  private async lockAccount(userId: string): Promise<void> {
    // Implémenter la logique de verrouillage du compte
  }

  private async requireAdditionalAuthentication(userId: string): Promise<void> {
    // Implémenter la logique d'authentification supplémentaire
  }

  private async notifySecurityTeam(alert: {
    type: string;
    severity: string;
    message: string;
    event: SecurityEvent;
  }): Promise<void> {
    // Implémenter la notification de l'équipe de sécurité
  }

  private async freezePermissionChanges(userId: string): Promise<void> {
    // Implémenter le gel des modifications de permissions
  }

  private isUnusualLocation(location: SecurityEvent['metadata']['location']): boolean {
    // Implémenter la logique de détection de localisation inhabituelle
    return false;
  }

  private async requiresAdditionalAuth(userId: string): Promise<boolean> {
    // Implémenter la logique de vérification d'authentification supplémentaire
    return false;
  }

  private cleanupOldEvents(): void {
    const retentionPeriod = 30 * 24 * 60 * 60 * 1000; // 30 jours
    const cutoffDate = new Date(Date.now() - retentionPeriod);
    this.securityEvents = this.securityEvents.filter(
      event => new Date(event.timestamp) >= cutoffDate
    );
  }
}

export const securityService = SecurityService.getInstance();
export default securityService;
