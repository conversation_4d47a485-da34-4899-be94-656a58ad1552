-- Script de configuration MySQL pour MOUMEN TECHNIQUE ET PREVOYANCE
-- À exécuter en tant que root MySQL

-- Créer la base de données
CREATE DATABASE IF NOT EXISTS mtp_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mtp_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mtp_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- C<PERSON>er l'utilisateur pour l'application
CREATE USER IF NOT EXISTS 'mtp_user'@'localhost' IDENTIFIED BY 'mtp_password_2024!';

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON mtp_development.* TO 'mtp_user'@'localhost';
GRANT ALL PRIVILEGES ON mtp_test.* TO 'mtp_user'@'localhost';
GRANT ALL PRIVILEGES ON mtp_production.* TO 'mtp_user'@'localhost';

-- Appliquer les changements
FLUSH PRIVILEGES;

-- Afficher les bases créées
SHOW DATABASES LIKE 'mtp_%';

-- Afficher les utilisateurs
SELECT User, Host FROM mysql.user WHERE User = 'mtp_user';

-- Instructions d'utilisation :
-- 1. Connectez-vous à MySQL en tant que root : mysql -u root -p
-- 2. Exécutez ce script : source /chemin/vers/setup-mysql.sql
-- 3. Configurez votre .env avec : DATABASE_URL="mysql://mtp_user:mtp_password_2024!@localhost:3306/mtp_development"
