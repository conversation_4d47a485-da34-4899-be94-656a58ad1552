import { Router } from 'express';
import { prisma } from '@/config/database';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { authenticate, AuthenticatedRequest } from '@/middleware/auth';

const router = Router();

/**
 * GET /api/analytics/dashboard
 * Statistiques du dashboard
 */
router.get('/dashboard', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Calculer les dates
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

  // Statistiques totales
  const [
    totalLeads,
    totalClients,
    totalQuotes,
    totalContracts,
    leadsThisMonth,
    clientsThis<PERSON><PERSON><PERSON>,
    quotesT<PERSON><PERSON><PERSON><PERSON>,
    contractsThis<PERSON><PERSON><PERSON>,
    leads<PERSON><PERSON><PERSON><PERSON><PERSON>,
    clientsLast<PERSON><PERSON>h,
    quotesLast<PERSON>onth,
    contractsLastMonth
  ] = await Promise.all([
    // Totaux
    prisma.lead.count(),
    prisma.client.count(),
    prisma.quote.count(),
    prisma.contract.count(),
    
    // Ce mois
    prisma.lead.count({ where: { createdAt: { gte: startOfMonth } } }),
    prisma.client.count({ where: { createdAt: { gte: startOfMonth } } }),
    prisma.quote.count({ where: { createdAt: { gte: startOfMonth } } }),
    prisma.contract.count({ where: { createdAt: { gte: startOfMonth } } }),
    
    // Mois dernier
    prisma.lead.count({ 
      where: { 
        createdAt: { 
          gte: startOfLastMonth,
          lte: endOfLastMonth
        } 
      } 
    }),
    prisma.client.count({ 
      where: { 
        createdAt: { 
          gte: startOfLastMonth,
          lte: endOfLastMonth
        } 
      } 
    }),
    prisma.quote.count({ 
      where: { 
        createdAt: { 
          gte: startOfLastMonth,
          lte: endOfLastMonth
        } 
      } 
    }),
    prisma.contract.count({ 
      where: { 
        createdAt: { 
          gte: startOfLastMonth,
          lte: endOfLastMonth
        } 
      } 
    })
  ]);

  // Leads par statut
  const leadsByStatus = await prisma.lead.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  // Revenus (somme des contrats actifs)
  const revenueData = await prisma.contract.aggregate({
    where: { status: 'ACTIVE' },
    _sum: { premium: true },
  });

  const revenueThisMonth = await prisma.contract.aggregate({
    where: { 
      status: 'ACTIVE',
      createdAt: { gte: startOfMonth }
    },
    _sum: { premium: true },
  });

  // Activité récente
  const recentLeads = await prisma.lead.findMany({
    take: 5,
    orderBy: { createdAt: 'desc' },
    select: { id: true, name: true, product: true, createdAt: true },
  });

  const recentQuotes = await prisma.quote.findMany({
    take: 5,
    orderBy: { createdAt: 'desc' },
    include: { client: { select: { name: true } } },
  });

  // Construire l'activité récente
  const recentActivity = [
    ...recentLeads.map(lead => ({
      id: lead.id,
      type: 'lead',
      message: `Nouveau lead: ${lead.name} (${lead.product})`,
      timestamp: lead.createdAt.toISOString(),
    })),
    ...recentQuotes.map(quote => ({
      id: quote.id,
      type: 'quote',
      message: `Devis ${quote.product} pour ${quote.client.name}`,
      timestamp: quote.createdAt.toISOString(),
    }))
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 10);

  // Calculer les pourcentages de croissance
  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  const stats = {
    totalLeads,
    totalClients,
    totalQuotes,
    totalContracts,
    totalRevenue: Number(revenueData._sum.premium || 0),
    
    leadsThisMonth,
    clientsThisMonth,
    quotesThisMonth,
    contractsThisMonth,
    revenueThisMonth: Number(revenueThisMonth._sum.premium || 0),
    
    // Croissance par rapport au mois dernier
    leadsGrowth: calculateGrowth(leadsThisMonth, leadsLastMonth),
    clientsGrowth: calculateGrowth(clientsThisMonth, clientsLastMonth),
    quotesGrowth: calculateGrowth(quotesThisMonth, quotesLastMonth),
    contractsGrowth: calculateGrowth(contractsThisMonth, contractsLastMonth),
    
    leadsByStatus: leadsByStatus.map(item => ({
      status: item.status,
      count: item._count.status,
    })),
    
    recentActivity,
  };

  res.json({
    success: true,
    data: stats,
  });
}));

/**
 * GET /api/analytics/leads
 * Statistiques des leads
 */
router.get('/leads', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const period = req.query.period as string || '30'; // jours
  const days = parseInt(period);
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Leads par jour
  const leadsPerDay = await prisma.$queryRaw`
    SELECT 
      DATE(createdAt) as date,
      COUNT(*) as count
    FROM leads 
    WHERE createdAt >= ${startDate}
    GROUP BY DATE(createdAt)
    ORDER BY date ASC
  `;

  // Leads par produit
  const leadsByProduct = await prisma.lead.groupBy({
    by: ['product'],
    _count: { product: true },
    where: { createdAt: { gte: startDate } },
  });

  // Leads par source
  const leadsBySource = await prisma.lead.groupBy({
    by: ['source'],
    _count: { source: true },
    where: { createdAt: { gte: startDate } },
  });

  // Taux de conversion
  const totalLeads = await prisma.lead.count({
    where: { createdAt: { gte: startDate } },
  });

  const convertedLeads = await prisma.lead.count({
    where: { 
      createdAt: { gte: startDate },
      status: 'CONVERTED'
    },
  });

  const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;

  res.json({
    success: true,
    data: {
      period: days,
      totalLeads,
      convertedLeads,
      conversionRate: Math.round(conversionRate * 100) / 100,
      leadsPerDay,
      leadsByProduct: leadsByProduct.map(item => ({
        product: item.product,
        count: item._count.product,
      })),
      leadsBySource: leadsBySource.map(item => ({
        source: item.source,
        count: item._count.source,
      })),
    },
  });
}));

/**
 * GET /api/analytics/revenue
 * Statistiques de revenus
 */
router.get('/revenue', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const period = req.query.period as string || '12'; // mois
  const months = parseInt(period);
  
  // Revenus par mois
  const revenuePerMonth = await prisma.$queryRaw`
    SELECT 
      YEAR(createdAt) as year,
      MONTH(createdAt) as month,
      SUM(premium) as revenue
    FROM contracts 
    WHERE createdAt >= DATE_SUB(NOW(), INTERVAL ${months} MONTH)
    AND status = 'ACTIVE'
    GROUP BY YEAR(createdAt), MONTH(createdAt)
    ORDER BY year ASC, month ASC
  `;

  // Revenus par produit
  const revenueByProduct = await prisma.contract.groupBy({
    by: ['product'],
    _sum: { premium: true },
    where: { status: 'ACTIVE' },
  });

  // Revenus totaux
  const totalRevenue = await prisma.contract.aggregate({
    where: { status: 'ACTIVE' },
    _sum: { premium: true },
  });

  res.json({
    success: true,
    data: {
      period: months,
      totalRevenue: Number(totalRevenue._sum.premium || 0),
      revenuePerMonth,
      revenueByProduct: revenueByProduct.map(item => ({
        product: item.product,
        revenue: Number(item._sum.premium || 0),
      })),
    },
  });
}));

/**
 * GET /api/analytics/performance
 * Indicateurs de performance
 */
router.get('/performance', authenticate, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Temps de réponse moyen (simulé)
  const avgResponseTime = 2.5; // heures

  // Taux de satisfaction (simulé)
  const satisfactionRate = 94.5; // %

  // Objectifs vs réalisé
  const monthlyTarget = 50; // leads par mois
  const actualLeads = await prisma.lead.count({
    where: { createdAt: { gte: startOfMonth } },
  });

  const targetAchievement = (actualLeads / monthlyTarget) * 100;

  res.json({
    success: true,
    data: {
      avgResponseTime,
      satisfactionRate,
      monthlyTarget,
      actualLeads,
      targetAchievement: Math.round(targetAchievement * 100) / 100,
    },
  });
}));

export default router;
